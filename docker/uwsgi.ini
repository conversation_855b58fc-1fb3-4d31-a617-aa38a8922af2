[uwsgi]
master = true
plugins = python3
plugins = logfile
http-socket = 127.0.0.1:7002

wsgi = bolfak.application
pyargv = --config=/etc/bolfak/bolfak.cfg

processes = BOL_WORKER_PROCESS_COUNT
threads = 1

# Run BOL as a user bol (otherwise BOL runs as user root, unless the dockerfile says otherwise)
# uid = bol
# gid = bol

# newrelic requires both --enable-threads and --single-interpreter
enable-threads = 1
single-interpreter = 1
buffer-size = 65535

# suppress logs of Kubernetes readiness and liveness probes that are repeated every 10 seconds
logger = devnull file:/dev/null
log-req-route = devnull GET /api/status .* \(HTTP/[0-9.]+ 200\)

# For Azure application insights to work we need to set lazy-apps to true
lazy-apps = true

