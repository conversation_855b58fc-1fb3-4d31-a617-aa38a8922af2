[main]

person_org_contract_type = BOL_PERSON_ORG_CONTRACT_TYPE
org_contract_type = BOL_ORG_CONTRACT_TYPE
creditsafe_ggs_wsdl_username = BOL_CREDITSAFE_GGS_WSDL_USERNAME
creditsafe_ggs_wsdl_password = BOL_CREDITSAFE_GGS_WSDL_PASSWORD
default_storage = BOL_DEFAULT_STORAGE

[feature-flags]
company_related_projects = BOL_FEATURE_FLAG_COMPANY_RELATED_PROJECTS
import_sole_traders = BOL_FEATURE_FLAG_IMPORT_SOLE_TRADERS
pagination = BOL_FEATURE_FLAG_PAGINATION
projects = BOL_FEATURE_FLAG_PROJECTS
notifications = BOL_FEATURE_FLAG_NOTIFICATIONS
suppliers = BOL_FEATURE_FLAG_SUPPLIERS
visitors = BOL_FEATURE_FLAG_VISITORS
project_report = BOL_FEATURE_FLAG_PROJECT_REPORT
search = BOL_FEATURE_FLAG_SEARCH
archived_reports = BOL_FEATURE_FLAG_ARCHIVED_REPORTS
extended_report = BOL_FEATURE_FLAG_EXTENDED_REPORT
allow_sp_admin_access = BOL_FEATURE_FLAG_ALLOW_SP_ADMIN_ACCESS
use_stv_theme = BOL_FEATURE_FLAG_USE_STV_THEME
require_creditsafe_contract = BOL_FEATURE_FLAG_REQUIRE_CREDITSAFE_CONTRACT
finnchat = BOL_FEATURE_FLAG_FINNCHAT
disable_emails = BOL_FEATURE_FLAG_DISABLE_EMAILS
company_registry = BOL_FEATURE_FLAG_COMPANY_REGISTRY
celery_for_bulk_imports = BOL_FEATURE_FLAG_CELERY_FOR_BULK_IMPORTS
celery_for_sendgrid = BOL_FEATURE_FLAG_CELERY_FOR_SENDGRID
celery_for_status_reports = BOL_FEATURE_FLAG_CELERY_FOR_STATUS_REPORTS
celery_for_monitoring = BOL_FEATURE_FLAG_CELERY_FOR_MONITORING
bda_client = BOL_FEATURE_FLAG_BDA_CLIENT
bda_company_list = BOL_FEATURE_FLAG_BDA_COMPANY_LIST
bda_project_suppliers = BOL_FEATURE_FLAG_BDA_PROJECT_SUPPLIERS
user_account_api = BOL_FEATURE_FLAG_USER_ACCOUNT_API
web_reports = BOL_FEATURE_FLAG_WEB_REPORTS
web_reports_ee = BOL_FEATURE_FLAG_WEB_REPORTS_EE
web_reports_combined = BOL_FEATURE_FLAG_WEB_REPORTS_COMBINED
cqpoc_client = BOL_FEATURE_FLAG_CQPOC_CLIENT
cqpoc_get_companies = BOL_FEATURE_FLAG_CQPOC_GET_COMPANIES
stamp_workaround_one_site_at_a_time = BOL_FEATURE_FLAG_STAMP_WORKAROUND_ONE_SITE_AT_A_TIME
pre_announcements = BOL_FEATURE_FLAG_PRE_ANNOUNCEMENTS
show_rala_extract = BOL_FEATURE_FLAG_SHOW_RALA_EXTRACT
non_paed_suppliers = BOL_FEATURE_FLAG_NON_PAED_SUPPLIERS
contract_api_creditsafe_contract = BOL_FEATURE_FLAG_CONTRACT_API_CREDITSAFE_CONTRACT
on_azure = BOL_FEATURE_FLAG_ON_AZURE
report_employer_contributions = BOL_FEATURE_FLAG_REPORT_EMPLOYER_CONTRIBUTIONS
lazy_qvarn_startup = BOL_FEATURE_FLAG_LAZY_QVARN_STARTUP
pa_form_checkbox_disabled = BOL_FEATURE_FLAG_PA_FORM_CHECKBOX_DISABLED
add_project_client = BOL_FEATURE_FLAG_ADD_PROJECT_CLIENT
block_project_client = BOL_FEATURE_FLAG_BLOCK_PROJECT_CLIENT
skip_pa_reg_step = BOL_FEATURE_FLAG_SKIP_PA_REG_STEP
create_and_activate_cs_accounts = BOL_FEATURE_FLAG_CREATE_AND_ACTIVATE_CS_ACCOUNTS
person_id_for_project_users = BOL_FEATURE_FLAG_PERSON_ID_FOR_PROJECT_USERS
core_mitt_id06 = BOL_FEATURE_FLAG_CORE_MITT_ID06
project_supplier_comments = BOL_FEATURE_FLAG_PROJECT_SUPPLIER_COMMENTS
skip_gluu_login = BOL_FEATURE_FLAG_SKIP_GLUU_LOGIN
dependency_request_cache = BOL_FEATURE_FLAG_DEPENDENCY_REQUEST_CACHE

[celery]
broker_url = CELERY_BROKER_URL
task_default_queue = CELERY_TASK_DEFAULT_QUEUE
task_default_exchange = CELERY_TASK_DEFAULT_EXCHANGE

[gluu]
base_url = BOL_GLUU_ADDRESS

[bol-data-api]
base_url = BOL_BDA_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
scope =
    uapi_reports_search_id_get

    uapi_ext_bol_project_list_get,
    uapi_ext_bol_project_suppliers_get,
    uapi_ext_bol_company_list_get,

    bda_creditsafe_account_get,
    bda_creditsafe_account_post,
    bda_creditsafe_account_put,
    bda_creditsafe_account_delete,
    bda_creditsafe_account_search,

    bda_project_users_get,
    bda_project_users_search,

[qvarn]
verify_requests = BOL_VERIFY_REQUESTS
base_url = BOL_QVARN_ADDRESS
url_for_orgs = BOL_QVARNLIKE_URL_FOR_ORGS
url_for_cards = BOL_QVARNLIKE_URL_FOR_CARDS
url_for_persons = BOL_QVARNLIKE_URL_FOR_PERSONS
url_for_bol_suppliers = BOL_BDA_BASE_URL/api/v1/boldata
url_for_projects = BOL_BDA_BASE_URL/api/v1/boldata
url_for_reports = BOL_BDA_BASE_URL/api/v1/boldata
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
scope =
    uapi_contracts_id_document_get,
    uapi_contracts_id_get,
    uapi_contracts_search_id_get,

    uapi_orgs_get,
    uapi_orgs_id_get
    uapi_orgs_search_id_get,

    uapi_projects_get,
    uapi_projects_id_get,
    uapi_projects_search_id_get,

    uapi_bol_suppliers_get,
    uapi_bol_suppliers_id_get,
    uapi_bol_suppliers_search_id_get,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

threads = BOL_QVARN_THREADPOOL_SIZE

[core-system-api]
base_url = BOL_CORESYSAPI_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    view_extended:organisation_person,
    view:person,
    view:organisation,
    subscribe:organisationrecord,
    view:organisationrecord,
    view_report:organisationrecord,

[contract]
base_url = CONTRACT_API_BASE_URL
verify_requests = true
scopes =
    contract_creditsafe_account_read,
    contract_creditsafe_account_create,
    contract_creditsafe_account_delete,
    contract_creditsafe_account_update,

[user-account-api]
base_url = USER_ACCOUNT_API_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    user_account_read
    user_account_create
    user_account_delete
    user_account_update

[statusreports.creditsafe]
username = BOL_CREDITSAFE_USER_ID
password = BOL_CREDITSAFE_PASSWORD
use_test_server = false
symmetric_secret_key = BOL_CREDITSAFE_SYMMETRIC_SECRET_KEY


# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[loggers]
keys = root, suds, requests, qvarnmq

[logger_root]
handlers = console
level = BOL_LOG_LEVEL

[logger_suds]
qualname = suds
handlers =
level = ERROR

[logger_requests]
qualname = requests
handlers =
level = INFO

[logger_qvarnmq]
# Subtle: qvarnmq sets propagate=False in the class __init__, so we need to
# configure a handler explicitly, and also set propagate=False so we don't
# double-log by accident if the __init__ is not called or is called with
# debug=True (or if a new qvarn-utils stops the let's change logging policy in
# a random constructor madness).
# The side effect of this is that exceptions from qvarnmq are not logged at higher levels
qualname = qvarnmq
handlers = console
# NB: do not use level=INFO until https://jira.tilaajavastuu.fi/browse/UTIL-114 is fixed!
level = WARNING
propagate = 0


[handlers]
keys = console

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = default
level = BOL_LOG_LEVEL


[formatters]
keys = default

[formatter_default]
class = bolfak.logging.JsonFormatter
format =
    [
        "asctime", "levelname", "message", {
            "logger": "name",
            "pid": "process",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName"
        }
    ]
    
[monitoring]
visitor_whitelist_org_ids = BOL_VISITOR_WHITELIST_ORG_IDS
