# Development config for BOL
frontend_url=http://localhost:8080/
backend_url=http://localhost:8080/api/
service_portal_url=http://localhost:8081/
service_provider=id06
auth_data_caching_duration_in_seconds=5
environment_config=/etc/bolfak/env-config.yaml
person_org_contract_type=user_account
org_contract_type=customership
qvarn_base_url=http://haproxy
qvarnlike_url_for_orgs=http://haproxy
qvarnlike_url_for_cards=http://haproxy
qvarnlike_url_for_persons=http://haproxy
coresysapi_base_url=https://pimcore.alpha.id06.se
bda_base_url=http://bol-data-api:8000
client_id=@!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F
client_secret=supersecret
gluu_base_url=https://auth-azure-alpha.id06.se
contract_api_base_url=http://contract-api
session_type=redis
redis_dsn=redispass@redis:6379/0
session_cookie_domain=
session_encrypt_key=very
session_validate_key=secret
report_version_id06=
bulkimport_data_provider=bolfak.statusreports.sandbox.SandboxInfoProvider
report_data_provider=bolfak.statusreports.sandbox.Sandbox
creditsafe_username=
creditsafe_password=
creditsafe_symmetric_secret_key=
creditsafe_ggs_wsdl_username=
creditsafe_ggs_wsdl_password=
autoaccount_url=
autoaccount_username=
autoaccount_password=
autoaccount_request_package=
autoaccount_use_testing_org_gov_org_id=
autoaccount_testing_org_gov_org_id=
autoaccount_id06_gov_org_id=
change_user_details_url=
default_storage=qvarn
skatteverket_client_pem=
skatteverket_ca_pem=
worker_process_count=1
session_cookie_secure=no
qvarn_threadpool_size=20
log_level=DEBUG
swagger_api=true
bol_permission=bolagsdeklaration_user
feature_flag_extended_report=yes
feature_flag_pagination=yes
feature_flag_visitors=true
feature_flag_celery_for_sendgrid=yes
sp_framework_api_url=http://sp:8080/api/sp-framework-api
sp_framework_api_verify_tls=false
shared_cookie_domain=localhost
i18_variant=id06
feature_flag_bda_client=yes
feature_flag_bda_company_list=yes
feature_flag_web_reports=no
feature_flag_project_report=yes
feature_flag_bda_project_suppliers=yes
feature_flag_cqpoc_client=no
feature_flag_cqpoc_get_companies=no
feature_flag_pre_announcements=yes
feature_flag_pa_form_checkbox_disabled=yes
feature_flag_non_paed_suppliers=yes
feature_flag_contract_api_creditsafe_contract=no
feature_flag_user_account_api=no
feature_flag_add_project_client=no
feature_flag_block_project_client=no
feature_flag_skip_pa_reg_step=no
feature_flag_create_and_activate_cs_accounts=no
# Email sending via Sendgrid
sendgrid_sender='ID06 alpha <<EMAIL>>'
sendgrid_api_key=*********************************************************************
test_api=yes
