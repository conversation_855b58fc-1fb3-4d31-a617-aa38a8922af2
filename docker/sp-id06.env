# UPDATE THESE VARIABLES, IF YOU WANT SP TO USE DIFFERENT REDIS:

session_stateless_redis_password_with_host=redispass@redis:6379/0

# THESE SETTINGS SHOULD BE OK AS THEY ARE:

ssl_verify=no
sign_out_url=www.id06.se
shared_cookie_domain=localhost
self_url_protocol=http

self_url=localhost:8081

gluu_valid_issuers=https://auth-azure-alpha.id06.se
qvarn_parallel_threads=20
legacy_api_enabled=no
auth_data_caching_duration_in_seconds=20
refresh_token_grace_period_in_seconds=120
customer_service_email='<EMAIL>'
sendgrid_sender=
sendgrid_api_key=
tos_version=2017-04-22 00:00:00
session_timeout_in_seconds=600
session_secure=true
session_cookie_samesite="None"
session_redis_prefix=sp-id06
invite_link_expiration_time_seconds=60
ldap_enabled=false
content_config_file_name=id06-fs3.yaml
max_requests=200
bol_url=https://bfalpha.id06.se
my_data_url=https://mittid06.alpha.id06.se
company_reg_inbox=
other_sp_in_stv_url=portal.dev-vastuugroup.fi
company_api_url=https://company-api.alpha.id06.se/api
use_old_contract_types=false
worker_process_count=5
org_name_restricted_length=30
env=alpha
card_api_url=https://card-api.alpha.id06.se
person_api_url=https://personapi.alpha.tilaajavastuu.fi/api/v1
card_supplier_1_skipped_in_list=false
scope_set=id06-fs3-scopes-for-test-app
access_token=testgw_bz0NjkCj5RBTaKn7WdBLABe3JxeQWVQF
cs_testing_org_gov_org_id=5590522040
access_token=testgw_bz0NjkCj5RBTaKn7WdBLABe3JxeQWVQF
dokobit_base_url=https://gateway-sandbox.dokobit.com
virtual_card_enabled=false
feature_disabled_billing_admin=false
email_blacklisting=false
manual_id_url=https://manual-id-alpha.id06.se
cards_app_url=https://cards-alpha.id06.se
company_app_url=https://companies-alpha.id06.se
invoicing_web_url=https://invoiceweb-dev.id06.se
portal_token_secret_key=TmgXfux4nunn83piI47X9bJ3kvh2MSQt2zfYJNHDInAA6yd3Wt
session_encrypt_key=very
session_validate_key=secret
session_type=redis
ldap_enabled=false
current_build_version=test
current_build_commit_sha=test
# Required for SP container startup (BOL-6332: SP expects this env var even though Sentry is disabled)
sentry_url=-
redis_with_ssl=false
newrelic_license_key=04eeb616db1502876c069b353554a90510a1d356
pin_salt=V0kvvh2bEce4J8yn
pin_secret_key=NXL363fhFcZUISSp
cs_auto_account_password=Mush5ahx
symmetric_secret_key=Dx4TxP6MT26ikZ5PraiUsffFSojptc5UNda9NbviD6Ejbw
