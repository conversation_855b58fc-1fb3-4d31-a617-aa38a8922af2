[main]
base_path = /api
static_path = /static
static_map = /srv/bolfak/www/static/SHA256SUMS
host = 127.0.0.1
port = 7002
frontend_url = BOL_FRONTEND_URL
standalone_url = BOL_STANDALONE_URL
backend_url = BOL_BACKEND_URL
docker_tag = VERSION
auth_data_caching_duration_in_seconds = BOL_AUTH_DATA_CACHE_DURATION_SECONDS
service_portal_url = BOL_SERVICE_PORTAL_URL
company_registry_url = BOL_COMPANY_REGISTRY_URL
companies_url = BOL_COMPANIES_URL
service_provider = BOL_SERVICE_PROVIDER
insecure_test_api = BOL_TEST_API
insecure_testdata_api = BOL_TESTDATA_API
swagger_api = BOL_SWAGGER_API
person_org_contract_type = BOL_PERSON_ORG_CONTRACT_TYPE
org_contract_type = BOL_ORG_CONTRACT_TYPE
service_key = BOL_SERVICE_KEY
bol_permission = BOL_BOL_PERMISSION
content_config_name = BOL_CONTENT_CONFIG_NAME
terms_of_service_version = BOL_TERMS_OF_SERVICE_VERSION
shared_cookie_domain = BOL_SHARED_COOKIE_DOMAIN
raportit_url = BOL_RAPORTIT_URL
valvoja_url = BOL_VALVOJA_URL
veronumero_url = BOL_VERONUMERO_URL
taito_url = BOL_TAITO_URL
reseller_tool_url = BOL_RESELLER_TOOL_URL
sp_framework_api_url = SP_FRAMEWORK_API_URL
sp_framework_api_verify_tls = SP_FRAMEWORK_API_VERIFY_TLS
default_storage = BOL_DEFAULT_STORAGE
locale_dir = locale/BOL_I18N_VARIANT
change_user_details_url = BOL_CHANGE_USER_DETAILS_URL

[bol-data-api]
base_url = BOL_BDA_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
scope =
    uapi_reports_search_id_get,
    uapi_reports_post,

    uapi_ext_bol_project_list_get,
    uapi_ext_bol_project_suppliers_get,
    uapi_ext_bol_company_list_get,

    uapi_jobs_id_get,
    uapi_jobs_search_id_get,
    uapi_jobs_post,
    uapi_jobs_id_put,

    bda_project_users_get,
    bda_project_users_post,
    bda_project_users_put,
    bda_project_users_delete,
    bda_project_users_search,

    uapi_data_cache_get,
    uapi_data_cache_id_delete,
    uapi_data_cache_id_get,
    uapi_data_cache_id_put,
    uapi_data_cache_post,
    uapi_data_cache_search_id_get,

    bda_creditsafe_account_get,
    bda_creditsafe_account_post,
    bda_creditsafe_account_put,
    bda_creditsafe_account_delete,
    bda_creditsafe_account_search,

    bda_project_supplier_comments_get,
    bda_project_supplier_comments_post,
    bda_project_supplier_comments_put,
    bda_project_supplier_comments_delete,

[celery]
broker_url = CELERY_BROKER_URL
task_default_queue = CELERY_TASK_DEFAULT_QUEUE
task_default_exchange = CELERY_TASK_DEFAULT_EXCHANGE

[company-qvarn-poc]
base_url = BOL_CQPOC_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    uapi_orgs_multiple_post,

[core-system-api]
base_url = BOL_CORESYSAPI_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    view_extended:organisation_person,
    view:person,
    view:organisation,
    subscribe:organisationrecord,
    view:organisationrecord,
    view_report:organisationrecord,

[contract]
base_url = CONTRACT_API_BASE_URL
verify_requests = true
scopes =
    contract_creditsafe_account_read,
    contract_creditsafe_account_create,
    contract_creditsafe_account_delete,
    contract_creditsafe_account_update,

[user-account-api]
base_url = USER_ACCOUNT_API_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    user_account_read
    user_account_create
    user_account_delete
    user_account_update

[stamp]
base_url = BOL_STAMP_BASE_URL
verify_requests = true
scopes =
    stampdata_companies_whitelist_get,
    stampdata_events_post,
    stampdata_event_batches_id_status_get,
    stampdata_visitor_companies_post,

[qvarn]
verify_requests = BOL_VERIFY_REQUESTS
base_url = BOL_QVARN_ADDRESS
url_for_orgs = BOL_QVARNLIKE_URL_FOR_ORGS
url_for_cards = BOL_QVARNLIKE_URL_FOR_CARDS
url_for_persons = BOL_QVARNLIKE_URL_FOR_PERSONS
url_for_bol_suppliers = BOL_BDA_BASE_URL/api/v1/boldata
url_for_projects = BOL_BDA_BASE_URL/api/v1/boldata
url_for_reports = BOL_BDA_BASE_URL/api/v1/boldata
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
scope =
    uapi_contracts_id_delete,
    uapi_contracts_id_document_get,
    uapi_contracts_id_document_put,
    uapi_contracts_id_get,
    uapi_contracts_id_put,
    uapi_contracts_post,
    uapi_contracts_search_id_get,

    uapi_orgs_get,
    uapi_orgs_id_delete,
    uapi_orgs_id_get,
    uapi_orgs_id_put,
    uapi_orgs_post,
    uapi_orgs_search_id_get,
    uapi_orgs_id_sync_get,

    uapi_persons_get,
    uapi_persons_id_delete,
    uapi_persons_id_get,
    uapi_persons_id_private_get,
    uapi_persons_id_private_put,
    uapi_persons_id_put,
    uapi_persons_post,
    uapi_persons_search_id_get,

    uapi_reports_get,
    uapi_reports_id_delete,
    uapi_reports_id_get,
    uapi_reports_id_pdf_get,
    uapi_reports_id_pdf_put,
    uapi_reports_id_put,
    uapi_reports_post,
    uapi_reports_search_id_get,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

    uapi_service_plans_search_id_get,

threads = BOL_QVARN_THREADPOOL_SIZE
extended_project_fields = yes

[gluu]
base_url = BOL_GLUU_ADDRESS
end_session_support = yes

[sendgrid]
sendgrid_sender = SENDGRID_SENDER
sendgrid_api_key = SENDGRID_API_KEY

[sessions]
type = BOL_SESSION_TYPE
dsn = BOL_REDIS_DSN
cookie_name = boldek_session
cookie_domain = BOL_SESSION_COOKIE_DOMAIN
cookie_path = /api/
timeout = 14400
ttl = 14400
hkey_prefix = session_v2
httponly = true
secure = BOL_SESSION_COOKIE_SECURE
samesite = BOL_SESSION_COOKIE_SAMESITE
encrypt_key = BOL_SESSION_ENCRYPTION_KEY
validate_key = BOL_SESSION_VALIDATION_KEY

[feature-flags]
company_related_projects = BOL_FEATURE_FLAG_COMPANY_RELATED_PROJECTS
import_sole_traders = BOL_FEATURE_FLAG_IMPORT_SOLE_TRADERS
pagination = BOL_FEATURE_FLAG_PAGINATION
projects = BOL_FEATURE_FLAG_PROJECTS
notifications = BOL_FEATURE_FLAG_NOTIFICATIONS
suppliers = BOL_FEATURE_FLAG_SUPPLIERS
visitors = BOL_FEATURE_FLAG_VISITORS
project_report = BOL_FEATURE_FLAG_PROJECT_REPORT
search = BOL_FEATURE_FLAG_SEARCH
archived_reports = BOL_FEATURE_FLAG_ARCHIVED_REPORTS
extended_report = BOL_FEATURE_FLAG_EXTENDED_REPORT
allow_sp_admin_access = BOL_FEATURE_FLAG_ALLOW_SP_ADMIN_ACCESS
use_stv_theme = BOL_FEATURE_FLAG_USE_STV_THEME
require_creditsafe_contract = BOL_FEATURE_FLAG_REQUIRE_CREDITSAFE_CONTRACT
finnchat = BOL_FEATURE_FLAG_FINNCHAT
disable_emails = BOL_FEATURE_FLAG_DISABLE_EMAILS
company_registry = BOL_FEATURE_FLAG_COMPANY_REGISTRY
celery_for_sendgrid = BOL_FEATURE_FLAG_CELERY_FOR_SENDGRID
bda_client = BOL_FEATURE_FLAG_BDA_CLIENT
bda_company_list = BOL_FEATURE_FLAG_BDA_COMPANY_LIST
bda_project_suppliers = BOL_FEATURE_FLAG_BDA_PROJECT_SUPPLIERS
user_account_api = BOL_FEATURE_FLAG_USER_ACCOUNT_API
web_reports = BOL_FEATURE_FLAG_WEB_REPORTS
web_reports_ee = BOL_FEATURE_FLAG_WEB_REPORTS_EE
web_reports_combined = BOL_FEATURE_FLAG_WEB_REPORTS_COMBINED
cqpoc_client = BOL_FEATURE_FLAG_CQPOC_CLIENT
cqpoc_get_companies = BOL_FEATURE_FLAG_CQPOC_GET_COMPANIES
stamp_workaround_one_site_at_a_time = BOL_FEATURE_FLAG_STAMP_WORKAROUND_ONE_SITE_AT_A_TIME
pre_announcements = BOL_FEATURE_FLAG_PRE_ANNOUNCEMENTS
show_rala_extract = BOL_FEATURE_FLAG_SHOW_RALA_EXTRACT
non_paed_suppliers = BOL_FEATURE_FLAG_NON_PAED_SUPPLIERS
contract_api_creditsafe_contract = BOL_FEATURE_FLAG_CONTRACT_API_CREDITSAFE_CONTRACT
on_azure = BOL_FEATURE_FLAG_ON_AZURE
report_employer_contributions = BOL_FEATURE_FLAG_REPORT_EMPLOYER_CONTRIBUTIONS
lazy_qvarn_startup = BOL_FEATURE_FLAG_LAZY_QVARN_STARTUP
pa_form_checkbox_disabled = BOL_FEATURE_FLAG_PA_FORM_CHECKBOX_DISABLED
add_project_client = BOL_FEATURE_FLAG_ADD_PROJECT_CLIENT
block_project_client = BOL_FEATURE_FLAG_BLOCK_PROJECT_CLIENT
skip_pa_reg_step = BOL_FEATURE_FLAG_SKIP_PA_REG_STEP
create_and_activate_cs_accounts = BOL_FEATURE_FLAG_CREATE_AND_ACTIVATE_CS_ACCOUNTS
person_id_for_project_users = BOL_FEATURE_FLAG_PERSON_ID_FOR_PROJECT_USERS
core_mitt_id06 = BOL_FEATURE_FLAG_CORE_MITT_ID06
project_supplier_comments = BOL_FEATURE_FLAG_PROJECT_SUPPLIER_COMMENTS
skip_gluu_login = BOL_FEATURE_FLAG_SKIP_GLUU_LOGIN
dependency_request_cache = BOL_FEATURE_FLAG_DEPENDENCY_REQUEST_CACHE

[statusreports]
report_version_id06 = BOL_REPORT_VERSION_ID06
bulkimport_data_provider = BOL_BULKIMPORT_DATA_PROVIDER
report_swedish_data_provider = BOL_REPORT_SWEDISH_DATA_PROVIDER
report_foreign_data_provider = BOL_REPORT_FOREIGN_DATA_PROVIDER

[statusreports.creditsafe]
username = BOL_CREDITSAFE_USER_ID
password = BOL_CREDITSAFE_PASSWORD
use_test_server = false
symmetric_secret_key = BOL_CREDITSAFE_SYMMETRIC_SECRET_KEY

[statusreports.creditsafe_v2]
username = BOL_CREDITSAFE_USER_ID
password = BOL_CREDITSAFE_PASSWORD
use_test_server = false
symmetric_secret_key = BOL_CREDITSAFE_SYMMETRIC_SECRET_KEY

[statusreports.creditsafe_ggs]
creditsafe_ggs_wsdl_username = BOL_CREDITSAFE_GGS_WSDL_USERNAME
creditsafe_ggs_wsdl_password = BOL_CREDITSAFE_GGS_WSDL_PASSWORD

[autoaccount]
autoaccount_url = BOL_AUTOACCOUNT_URL
autoaccount_username = BOL_AUTOACCOUNT_USERNAME
autoaccount_password = BOL_AUTOACCOUNT_PASSWORD
autoaccount_request_package = BOL_AUTOACCOUNT_REQUEST_PACKAGE
autoaccount_use_testing_org_gov_org_id = BOL_AUTOACCOUNT_USE_TESTING_ORG_GOV_ORG_ID
autoaccount_testing_org_gov_org_id = BOL_AUTOACCOUNT_TESTING_ORG_GOV_ORG_ID
autoaccount_id06_gov_org_id = BOL_AUTOACCOUNT_ID06_GOV_ORG_ID
autoaccount_email = BOL_AUTOACCOUNT_EMAIL

[lima]
url = LIMA_BASE_URL
version = LIMA_VERSION
client_id = LIMA_CLIENT_ID
client_secret = LIMA_CLIENT_SECRET
scopes = licenses_get, licenses_post, licenses_search_post

[companyapi]
url = COMPANY_API_BASE_URL
version = COMPANY_API_VERSION
client_id = COMPANY_API_CLIENT_ID
client_secret = COMPANY_API_CLIENT_SECRET
scopes =
  company_by_fetched_reports_get,
  company_single_get,
  company_search,
  company_latest_report_get,
  company_latest_report_pdf_get,
  company_archived_report_get,
  company_archived_report_pdf_get,
  company_archived_reports_by_company_get,

# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[formatters]
keys = default

[formatter_default]
class = bolfak.logging.JsonFormatter
format =
    [
        "asctime", "levelname", {
            "message": "message",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName",
            "request": "requestId",
            "user": "user"
        }
    ]


[handlers]
keys = console

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = default
level = BOL_LOG_LEVEL


[loggers]
keys = root, newrelic, suds

[logger_root]
level = BOL_LOG_LEVEL
handlers = console

[logger_newrelic]
qualname = newrelic
level = INFO
handlers =

[logger_suds]
# suds DEBUG messages dump entire XML schemas in one message, which
# causes an OSError: [Errno 90] Message too long inside the logging
# framework
qualname = suds
level = INFO
handlers =

[monitoring]
visitor_whitelist_org_ids = BOL_VISITOR_WHITELIST_ORG_IDS
