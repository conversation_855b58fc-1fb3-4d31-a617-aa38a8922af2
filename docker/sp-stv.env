# UPDATE THESE VARIABLES, IF YOU WANT SP TO USE DIFFERENT REDIS:

session_redis_password_with_host=@redis

# THESE SETTINGS SHOULD BE OK AS THEY ARE:

ssl_verify=no
sign_out_url=www.tilaajavastuu.fi
shared_cookie_domain=.dev-vastuugroup.fi
self_url_protocol=http

# if running bol locally use:
#self_url=localhost:8081
# instead of:
self_url=sp

qvarn_parallel_threads=20
person_id_in_token=yes
sp_api_enabled=no
legacy_api_enabled=yes
auth_data_caching_duration_in_seconds=300
refresh_token_grace_period_in_seconds=120
sendgrid_sender='ID06 alpha <<EMAIL>>'
sendgrid_api_key=*********************************************************************
tos_version=2019-11-18 00:00:00
session_timeout_in_seconds=600
session_secure=false
session_redis_prefix=sp-stv
invite_link_expiration_time_seconds=60
ldap_enabled=false
ldap_host=devel-1.tilaajavastuu.fi
content_config_file_name=stv.yaml
ldap_uid=password_change_sync
max_requests=200
reseller_tool_url=https://reseller.alpha.tilaajavastuu.fi
raportit_url=https://bol.dev-vastuugroup.fi/#/login/[ORG_ID]
raportit_description_link_url=https://bol.dev-vastuugroup.fi/#/subscription
valvoja_url=https://raportit.dev-vastuugroup.fi
veronumero_url=https://devel-www.veronumero.fi
taito_url=https://taito.dev-vastuugroup.fi
qat_url=https://qat.alpha.tilaajavastuu.fi
other_sp_in_id06_url=spalpha-dev.id06.se
use_old_contract_types=false
worker_process_count=5
env=alpha
portal_token_secret_key=TmgXfux4nunn83piI47X9bJ3kvh2MSQt2zfYJNHDInAA6yd3Wt
session_encrypt_key=HbSo4D9ink8p
session_validate_key=HeJhf2FP3p4V
ldap_password=testadmin
bankid_cert=-
bankid_key=-
# Required for SP container startup (BOL-6332: SP expects this env var even though Sentry is disabled)
sentry_url=-
newrelic_license_key=04eeb616db1502876c069b353554a90510a1d356

