#!/bin/bash
set -o errexit

CONFIG_FILES=(
  /etc/bolfak/bolfak.cfg
  /etc/bolfak/statusreports.cfg
  /etc/bolfak/notifications.cfg
  /etc/bolfak/monitoring.cfg
)

for flag in ${!feature_flag_*}
do
  sed -i -e "s#BOL_${flag^^}\b#${!flag}#g" "${CONFIG_FILES[@]}"
done

sed -i -e "/BOL_FEATURE_FLAG_/d" "${CONFIG_FILES[@]}"

if [ "$feature_flag_use_stv_theme" = "true" ]; then
  cp /srv/bolfak/www/index.stv.html /srv/bolfak/www/index.html
else
  cp /srv/bolfak/www/index.id06.html /srv/bolfak/www/index.html
fi

sed -i \
  -e "s#BOL_WORKER_PROCESS_COUNT#${worker_process_count}#g" \
  /etc/uwsgi/apps-enabled/bolagsfakta.ini

sed -i \
  -e "s#BOL_SERVICE_KEY#${service_key}#g" \
  -e "s#BOL_FRONTEND_URL#${frontend_url}#g" \
  -e "s!BOL_STANDALONE_URL!${standalone_url}!g" \
  -e "s#BOL_BACKEND_URL#${backend_url}#g" \
  -e "s#BOL_AUTH_DATA_CACHE_DURATION_SECONDS#${auth_data_caching_duration_in_seconds}#g" \
  -e "s#BOL_ENVIRONMENT_CONFIG#${environment_config}#g" \
  -e "s#BOL_VERIFY_REQUESTS#${verify_requests}#g" \
  -e "s#BOL_BDA_BASE_URL#${bda_base_url}#g" \
  -e "s#BOL_CQPOC_BASE_URL#${cqpoc_base_url}#g" \
  -e "s#BOL_QVARN_ADDRESS#${qvarn_base_url}#g" \
  -e "s#BOL_QVARNLIKE_URL_FOR_ORGS#${qvarnlike_url_for_orgs}#g" \
  -e "s#BOL_QVARNLIKE_URL_FOR_CARDS#${qvarnlike_url_for_cards}#g" \
  -e "s#BOL_QVARNLIKE_URL_FOR_PERSONS#${qvarnlike_url_for_persons}#g" \
  -e "s#BOL_CORESYSAPI_BASE_URL#${coresysapi_base_url}#g" \
  -e "s#BOL_CLIENT_ID#${client_id}#g" \
  -e "s#BOL_CLIENT_SECRET#${client_secret}#g" \
  -e "s#BOL_GLUU_ADDRESS#${gluu_base_url}#g" \
  -e "s#BOL_SESSION_TYPE#${session_type}#g" \
  -e "s#BOL_REDIS_DSN#${redis_dsn}#g" \
  -e "s#BOL_SESSION_COOKIE_DOMAIN#${session_cookie_domain}#g" \
  -e "s#BOL_SESSION_COOKIE_SECURE#${session_cookie_secure}#g" \
  -e "s#BOL_SESSION_COOKIE_SAMESITE#${session_cookie_samesite}#g" \
  -e "s#BOL_SESSION_ENCRYPTION_KEY#${session_encrypt_key}#g" \
  -e "s#BOL_SESSION_VALIDATION_KEY#${session_validate_key}#g" \
  -e "s#BOL_BULKIMPORT_DATA_PROVIDER#${bulkimport_data_provider}#g" \
  -e "s#BOL_REPORT_VERSION_ID06#${report_version_id06}#g" \
  -e "s#BOL_REPORT_SWEDISH_DATA_PROVIDER#${report_swedish_data_provider}#g" \
  -e "s#BOL_REPORT_FOREIGN_DATA_PROVIDER#${report_foreign_data_provider}#g" \
  -e "s#BOL_TAX_DATA_PROVIDER#${tax_data_provider}#g" \
  -e "s#BOL_CREDITSAFE_USER_ID#${creditsafe_username}#g" \
  -e "s#BOL_CREDITSAFE_PASSWORD#${creditsafe_password}#g" \
  -e "s#BOL_CREDITSAFE_SYMMETRIC_SECRET_KEY#${creditsafe_symmetric_secret_key}#g" \
  -e "s#BOL_CREDITSAFE_GGS_WSDL_USERNAME#${creditsafe_ggs_wsdl_username}#g" \
  -e "s#BOL_CREDITSAFE_GGS_WSDL_PASSWORD#${creditsafe_ggs_wsdl_password}#g" \
  -e "s#BOL_AUTOACCOUNT_URL#${autoaccount_url}#g" \
  -e "s#BOL_AUTOACCOUNT_USERNAME#${autoaccount_username}#g" \
  -e "s#BOL_AUTOACCOUNT_PASSWORD#${autoaccount_password}#g" \
  -e "s#BOL_AUTOACCOUNT_REQUEST_PACKAGE#${autoaccount_request_package}#g" \
  -e "s#BOL_AUTOACCOUNT_USE_TESTING_ORG_GOV_ORG_ID#${autoaccount_use_testing_org_gov_org_id}#g" \
  -e "s#BOL_AUTOACCOUNT_TESTING_ORG_GOV_ORG_ID#${autoaccount_testing_org_gov_org_id}#g" \
  -e "s#BOL_AUTOACCOUNT_ID06_GOV_ORG_ID#${autoaccount_id06_gov_org_id}#g" \
  -e "s#BOL_AUTOACCOUNT_CURRENT_TOS_VERSION#${autoaccount_current_tos_version}#g" \
  -e "s#BOL_AUTOACCOUNT_EMAIL#${autoaccount_email}#g" \
  -e "s#BOL_DEFAULT_STORAGE#${default_storage}#g" \
  -e "s#BOL_SKATTEVERKET_CLIENT_CERT#${skatteverket_client_cert}#g" \
  -e "s#BOL_SKATTEVERKET_CA_CERT#${skatteverket_ca_cert}#g" \
  -e "s#BOL_QVARN_THREADPOOL_SIZE#${qvarn_threadpool_size}#g" \
  -e "s#BOL_PERSON_ORG_CONTRACT_TYPE#${person_org_contract_type}#g" \
  -e "s#BOL_ORG_CONTRACT_TYPE#${org_contract_type}#g" \
  -e "s#BOL_SERVICE_PORTAL_URL#${service_portal_url}#g" \
  -e "s#BOL_COMPANY_REGISTRY_URL#${company_registry_url}#g" \
  -e "s#BOL_COMPANIES_URL#${companies_url}#g" \
  -e "s#BOL_SERVICE_PROVIDER#${service_provider}#g" \
  -e "s#BOL_LOG_LEVEL#${log_level}#g" \
  -e "s#BOL_TEST_API#${test_api}#g" \
  -e "s#BOL_TESTDATA_API#${testdata_api}#g" \
  -e "s#BOL_SWAGGER_API#${swagger_api}#g" \
  -e "s#SENDGRID_SENDER#${sendgrid_sender}#g" \
  -e "s#SENDGRID_API_KEY#${sendgrid_api_key}#g" \
  -e "s#BOL_CONTENT_CONFIG_NAME#${content_config_name}#g" \
  -e "s#BOL_BOL_PERMISSION#${bol_permission}#g" \
  -e "s#BOL_TERMS_OF_SERVICE_VERSION#${terms_of_service_version}#g" \
  -e "s#BOL_SHARED_COOKIE_DOMAIN#${shared_cookie_domain}#g" \
  -e "s#BOL_RAPORTIT_URL#${raportit_url}#g" \
  -e "s#BOL_VALVOJA_URL#${valvoja_url}#g" \
  -e "s#BOL_VERONUMERO_URL#${veronumero_url}#g" \
  -e "s#BOL_TAITO_URL#${taito_url}#g" \
  -e "s#BOL_RESELLER_TOOL_URL#${reseller_tool_url}#g" \
  -e "s#BOL_I18N_VARIANT#${i18n_variant}#g" \
  -e "s#LIMA_BASE_URL#${lima_base_url}#g" \
  -e "s#LIMA_VERSION#${lima_version}#g" \
  -e "s#LIMA_CLIENT_ID#${lima_client_id}#g" \
  -e "s#LIMA_CLIENT_SECRET#${lima_client_secret}#g" \
  -e "s#COMPANY_API_BASE_URL#${company_api_base_url}#g" \
  -e "s#COMPANY_API_VERSION#${company_api_version}#g" \
  -e "s#COMPANY_API_CLIENT_ID#${company_api_client_id}#g" \
  -e "s#COMPANY_API_CLIENT_SECRET#${company_api_client_secret}#g" \
  -e "s#SP_FRAMEWORK_API_URL#${sp_framework_api_url}#g" \
  -e "s#SP_FRAMEWORK_API_VERIFY_TLS#${sp_framework_api_verify_tls}#g" \
  -e "s#BOL_STAMP_BASE_URL#${bol_stamp_base_url}#g" \
  -e "s#CELERY_BROKER_URL#${CELERY_BROKER_URL}#g" \
  -e "s#CELERY_TASK_DEFAULT_QUEUE#${CELERY_TASK_DEFAULT_QUEUE}#g" \
  -e "s#CELERY_TASK_DEFAULT_EXCHANGE#${CELERY_TASK_DEFAULT_EXCHANGE}#g" \
  -e "s#CONTRACT_API_BASE_URL#${contract_api_base_url}#g" \
  -e "s#USER_ACCOUNT_API_BASE_URL#${user_account_api_base_url}#g" \
  -e "s!BOL_CHANGE_USER_DETAILS_URL!${change_user_details_url}!g" \
  -e "s#BOL_VISITOR_WHITELIST_ORG_IDS#${visitor_whitelist_org_ids}#g" \
  -e "s/VERSION/${build}/g" \
  "${CONFIG_FILES[@]}"

sed -i \
  -e "s#BOL_NEWRELIC_LICENSE_KEY#${newrelic_license_key}#g" \
  /etc/bolfak/newrelic.ini

# Remove possible trailing slashes from the config vars
BOL_URL="${standalone_url%/}"
MITT_ID06_URL="${frontend_url%/}"
BOL_OLD_DOMAIN="${old_domain%/}"
BOL_OLD_DOMAIN="${old_domain:-unused.local}"

# Allow origin replacement
sed -i \
  -e "s#BOL_URL_PLACEHOLDER#${BOL_URL}#g" \
  -e "s#MITT_ID06_URL_PLACEHOLDER#${MITT_ID06_URL}#g" \
  -e "s#OLD_DOMAIN_PLACEHOLDER#${BOL_OLD_DOMAIN}#g" \
  /etc/nginx/nginx.conf

# BOL url replacement
find /srv/bolfak/www/mfe -type f -exec sed -i \
  -e "s#BOL_URL_PLACEHOLDER#${BOL_URL}#g" {} +

# mitt id06 url replacement
find /srv/bolfak/www/mfe -type f -exec sed -i \
  -e "s#MITT_ID06_URL_PLACEHOLDER#${MITT_ID06_URL}#g" {} +

if [ "$1" = celery ]; then
  if [ -z "$CELERY_BROKER_URL" ]; then
    echo "CELERY_BROKER_URL not specified, disabling celery worker"
    echo "(looping forever to prevent Kubernetes from restarting the container)"
    while :; do sleep 3600; done
    exit 0
  fi
fi
if [ "$1" = uwsgi ]; then
  /usr/sbin/nginx
  if [ -n "$newrelic_license_key" ]; then
    echo "Enabling NewRelic integration"
    exec newrelic-admin run-program "$@"
  else
    echo "Not enabling NewRelic integration"
  fi
fi

exec "$@"
