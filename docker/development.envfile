# Development config for BOL (outdated and doesn't work)
frontend_url=http://localhost:8080/
backend_url=http://localhost:8080/api/
service_portal_url=http://localhost:8081/
service_provider=id06
auth_data_caching_duration_in_seconds=5
environment_config=/etc/bolfak/env-config.yaml
person_org_contract_type=tilaajavastuu_account
org_contract_type=tilaajavastuu_subscription
bda_base_url=http://localhost:8070
qvarn_base_url=https://bolagsfakta-qvarn-dev2.pov.lt
client_id=@!2027.861B.4505.5885!0001!200B.B5FE!0008!75E2.AF79
client_secret=verysecret
gluu_base_url=https://bolagsfakta-gluu-dev2.pov.lt
session_type=redis
redis_dsn=redis:6379/0
session_cookie_domain=
session_encrypt_key=very
session_validate_key=secret
bulkimport_data_provider=bolfak.statusreports.sandbox.SandboxInfoProvider
report_swedish_data_provider=bolfak.statusreports.sandbox.Sandbox
report_foreign_data_provider=bolfak.statusreports.sandbox.Sandbox
creditsafe_username=
creditsafe_password=
skatteverket_client_pem=
skatteverket_ca_pem=
worker_process_count=1
session_cookie_secure=no
qvarn_threadpool_size=20
log_level=DEBUG
sp_framework_api_url=https://spalpha.id06.se/api/sp-framework-api
