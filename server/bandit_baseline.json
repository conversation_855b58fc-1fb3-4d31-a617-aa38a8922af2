{"errors": [], "generated_at": "2022-03-01T07:53:09Z", "metrics": {"_totals": {"CONFIDENCE.HIGH": 51, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 6, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 49, "SEVERITY.MEDIUM": 7, "SEVERITY.UNDEFINED": 0, "loc": 34692, "nosec": 2, "skipped_tests": 0}, "bolfak/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "bolfak/__main__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 115, "nosec": 0, "skipped_tests": 0}, "bolfak/application.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "bolfak/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 38, "nosec": 0, "skipped_tests": 0}, "bolfak/bottle.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 50, "nosec": 0, "skipped_tests": 0}, "bolfak/celery/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 15, "nosec": 0, "skipped_tests": 0}, "bolfak/celery/tasks.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 36, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/bda.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 65, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/company_api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 62, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/company_qvarn_poc.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 85, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/company_registry_api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 1, "skipped_tests": 0}, "bolfak/clients/composite.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/gluu.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 113, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/lima_api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 81, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/qvarn.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 76, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/stamp.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 56, "nosec": 0, "skipped_tests": 0}, "bolfak/clients/viestikeskus.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 45, "nosec": 0, "skipped_tests": 0}, "bolfak/config.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 87, "nosec": 0, "skipped_tests": 0}, "bolfak/decorators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 72, "nosec": 0, "skipped_tests": 0}, "bolfak/exceptions.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "bolfak/featureflags.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 120, "nosec": 0, "skipped_tests": 0}, "bolfak/fixtures/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/fixtures/factories.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 2, "SEVERITY.UNDEFINED": 0, "loc": 664, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/bulkimport.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 112, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/companies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 5, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/language.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/preannouncements.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 70, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/project_tree.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 104, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/projects.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 128, "nosec": 0, "skipped_tests": 0}, "bolfak/forms/users.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "bolfak/i18n.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 246, "nosec": 0, "skipped_tests": 0}, "bolfak/logging.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 96, "nosec": 0, "skipped_tests": 0}, "bolfak/mixins/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/mixins/cs_account_credentials_mixin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 44, "nosec": 0, "skipped_tests": 0}, "bolfak/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 336, "nosec": 0, "skipped_tests": 0}, "bolfak/plugins/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/plugins/active_org_plugin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 28, "nosec": 0, "skipped_tests": 0}, "bolfak/plugins/auth_check_plugin.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 66, "nosec": 0, "skipped_tests": 0}, "bolfak/plugins/csrf_plugin.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 45, "nosec": 0, "skipped_tests": 0}, "bolfak/schemas/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/schemas/basicfields.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "bolfak/schemas/fields.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 169, "nosec": 0, "skipped_tests": 0}, "bolfak/schemas/models.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 7, "nosec": 0, "skipped_tests": 0}, "bolfak/schemas/reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 559, "nosec": 0, "skipped_tests": 0}, "bolfak/schemas/validators.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 78, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/add_visitors.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 34, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/bollogs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 206, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/clean_jobs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/clean_sandbox.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 39, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/cs_monitoring/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/cs_monitoring/add_to_monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 39, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/cs_monitoring/inspect_monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 150, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/cs_monitoring/remove_from_monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 40, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/export_suppliers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 53, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/generate_loadtest_data.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 207, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/migrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/migrations/enable_notifications.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 44, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/notify.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 115, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/org_usage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 169, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/pybabel.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 10, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/reports2csv.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 164, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/statusreports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 259, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/sync_fixture_data.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 300, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/sync_users.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 157, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/validate_data.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 243, "nosec": 0, "skipped_tests": 0}, "bolfak/scripts/validate_report.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 558, "nosec": 0, "skipped_tests": 0}, "bolfak/services/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/services/archived_reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 547, "nosec": 0, "skipped_tests": 0}, "bolfak/services/authentication.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 111, "nosec": 0, "skipped_tests": 0}, "bolfak/services/authorization.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 214, "nosec": 0, "skipped_tests": 0}, "bolfak/services/bulkimport.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 530, "nosec": 0, "skipped_tests": 0}, "bolfak/services/cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 149, "nosec": 0, "skipped_tests": 0}, "bolfak/services/companies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 765, "nosec": 0, "skipped_tests": 0}, "bolfak/services/creditsafe.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 31, "nosec": 0, "skipped_tests": 0}, "bolfak/services/cryptography.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "bolfak/services/fixtures.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 86, "nosec": 0, "skipped_tests": 0}, "bolfak/services/helpers/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/services/helpers/archived_reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 204, "nosec": 0, "skipped_tests": 0}, "bolfak/services/helpers/reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "bolfak/services/imports.py": {"CONFIDENCE.HIGH": 2, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 2, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 207, "nosec": 0, "skipped_tests": 0}, "bolfak/services/jobs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 30, "nosec": 0, "skipped_tests": 0}, "bolfak/services/notifications.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 374, "nosec": 0, "skipped_tests": 0}, "bolfak/services/preannouncements.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "bolfak/services/project_tree.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 414, "nosec": 0, "skipped_tests": 0}, "bolfak/services/projects.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 542, "nosec": 0, "skipped_tests": 0}, "bolfak/services/qvarn/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/services/qvarn/notification_reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "bolfak/services/qvarn/report_accesses.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 80, "nosec": 0, "skipped_tests": 0}, "bolfak/services/qvarn/reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 93, "nosec": 0, "skipped_tests": 0}, "bolfak/services/reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 249, "nosec": 0, "skipped_tests": 0}, "bolfak/services/resources.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 8, "nosec": 0, "skipped_tests": 0}, "bolfak/services/roles.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 336, "nosec": 0, "skipped_tests": 0}, "bolfak/services/stamp.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 164, "nosec": 0, "skipped_tests": 0}, "bolfak/services/status.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 111, "nosec": 0, "skipped_tests": 0}, "bolfak/services/statusreports.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1013, "nosec": 0, "skipped_tests": 0}, "bolfak/services/storage.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "bolfak/services/subscription.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 80, "nosec": 0, "skipped_tests": 0}, "bolfak/services/testapi.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 69, "nosec": 0, "skipped_tests": 0}, "bolfak/services/testdata.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 191, "nosec": 0, "skipped_tests": 0}, "bolfak/services/users.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 482, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 189, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/bisnode.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 273, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/creditsafe.py": {"CONFIDENCE.HIGH": 7, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 8, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1908, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/creditsafe_cached_ggs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 37, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/creditsafe_ggs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 499, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/creditsafe_report_monitoring.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 341, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/noop.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/sandbox.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 217, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/skatteverket.py": {"CONFIDENCE.HIGH": 7, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 4, "SEVERITY.UNDEFINED": 0, "loc": 482, "nosec": 0, "skipped_tests": 0}, "bolfak/statusreports/xmlschemas.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 2917, "nosec": 0, "skipped_tests": 0}, "bolfak/storage/preannouncements.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "bolfak/storage/qvarn/projects.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 169, "nosec": 0, "skipped_tests": 0}, "bolfak/storage/qvarn/stamp.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 45, "nosec": 0, "skipped_tests": 0}, "bolfak/storage/qvarn/suppliers.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 315, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/bda.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 77, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/company_api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 46, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/lima_api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/qvarn/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/qvarn/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 136, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/qvarn/bdaqvarn.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/qvarn/preloaded_data.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/qvarn/pretenderqvarn.py": {"CONFIDENCE.HIGH": 4, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 4, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 375, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/qvarn/realqvarn.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 204, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/services.py": {"CONFIDENCE.HIGH": 3, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 3, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 72, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/statusreports.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 1, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1846, "nosec": 0, "skipped_tests": 0}, "bolfak/testing/utils.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 14, "nosec": 0, "skipped_tests": 0}, "bolfak/utils.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 120, "nosec": 0, "skipped_tests": 0}, "bolfak/views/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/views/apidocs.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 260, "nosec": 0, "skipped_tests": 0}, "bolfak/views/archived_reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 654, "nosec": 0, "skipped_tests": 0}, "bolfak/views/authentication.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 128, "nosec": 0, "skipped_tests": 0}, "bolfak/views/bulkimport.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 422, "nosec": 0, "skipped_tests": 0}, "bolfak/views/common.py": {"CONFIDENCE.HIGH": 1, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 85, "nosec": 0, "skipped_tests": 0}, "bolfak/views/companies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1190, "nosec": 0, "skipped_tests": 0}, "bolfak/views/config_js.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 61, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/bulkimport.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 38, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/companies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 120, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/preannouncements.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/project_tree.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 339, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/projects.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 129, "nosec": 0, "skipped_tests": 0}, "bolfak/views/helpers/reports.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 539, "nosec": 0, "skipped_tests": 0}, "bolfak/views/notifications.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 385, "nosec": 0, "skipped_tests": 0}, "bolfak/views/preannouncements.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 339, "nosec": 0, "skipped_tests": 0}, "bolfak/views/project_tree.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1182, "nosec": 0, "skipped_tests": 0}, "bolfak/views/projects.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1345, "nosec": 0, "skipped_tests": 0}, "bolfak/views/search.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 344, "nosec": 0, "skipped_tests": 0}, "bolfak/views/staticfiles.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 19, "nosec": 0, "skipped_tests": 0}, "bolfak/views/status.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 125, "nosec": 0, "skipped_tests": 0}, "bolfak/views/subscription.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 249, "nosec": 0, "skipped_tests": 0}, "bolfak/views/swagger.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 455, "nosec": 0, "skipped_tests": 0}, "bolfak/views/testapi.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 225, "nosec": 1, "skipped_tests": 0}, "bolfak/views/testdata.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 135, "nosec": 0, "skipped_tests": 0}, "bolfak/views/version.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 103, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "71 \n72 ACCESS_TOKEN_SESSION_KEY = \"access_token\"\n73 \n74 \n75 # List of applications to mount to API_APP\n76 API_APPS = (\n", "col_offset": 27, "filename": "bolfak/application.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'access_token'", "line_number": 72, "line_range": [72, 73, 74, 75], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "13 RELEVANT_COMPANY_API_SCOPES = 'company_api_search_report'\n14 ACCESS_TOKEN_SESSION_KEY = 'access_token'\n15 \n16 \n17 # XXX: replace `CompanyRegistryClientForBOL` with\n18 # `stv.data.clients.company_registry.company_registry.setup_company_registry_client`\n19 # after stv-utils upgrade version >= 0.11.53\n20 # https://jira.tilaajavastuu.fi/browse/BOL-2574\n21 class CompanyRegistryClientForBOL(CompanyRegistryClient):\n", "col_offset": 27, "filename": "bolfak/clients/company_registry_api.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'access_token'", "line_number": 14, "line_range": [14, 15, 16, 17, 18, 19, 20], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "10 GLUU_AUTHORIZE_PATH = '/oxauth/seam/resource/restv1/oxauth/authorize'\n11 GLUU_TOKEN_PATH = '/oxauth/seam/resource/restv1/oxauth/token'\n12 \n13 HeadersDict = Dict[str, str]\n", "col_offset": 18, "filename": "bolfak/clients/gluu.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: '/oxauth/seam/resource/restv1/oxauth/token'", "line_number": 11, "line_range": [11, 12], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "126 def feature_active(name):\n127     assert name in FEATURE_FLAGS\n128     return get_config().getboolean('feature-flags', name, fallback=FEATURE_FLAGS[name])\n", "col_offset": 4, "filename": "bolfak/featureflags.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 127, "line_range": [127], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "56     \"\"\"\n57     digits = ''.join(map(str, hashlib.sha1(name.encode('utf-8')).digest()))\n58     return digits[:6] + '-' + digits[6:10]\n", "col_offset": 30, "filename": "bolfak/fixtures/factories.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "MEDIUM", "issue_text": "Use of insecure MD2, MD4, MD5, or SHA1 hash function.", "line_number": 57, "line_range": [57], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b303-md5", "test_id": "B303", "test_name": "blacklist"}, {"code": "68     A, Z = ord('A'), ord('Z')\n69     digits = list(map(int, hashlib.sha1(name.encode('utf-8')).digest()))\n70     letters = ''.join([chr(A + x % (Z - A)) for x in digits[:2]])\n", "col_offset": 27, "filename": "bolfak/fixtures/factories.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 327, "link": "https://cwe.mitre.org/data/definitions/327.html"}, "issue_severity": "MEDIUM", "issue_text": "Use of insecure MD2, MD4, MD5, or SHA1 hash function.", "line_number": 69, "line_range": [69], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b303-md5", "test_id": "B303", "test_name": "blacklist"}, {"code": "115     def __init__(self, **kw):\n116         assert self._message is not None, 'Override self._message in subclasses'\n117         self.__dict__.update(kw)\n", "col_offset": 8, "filename": "bolfak/i18n.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 116, "line_range": [116], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "131 \n132 assert sorted(STATUSES) == sorted(STATUS_TEXT_TRANSLATIONS), (\n133     \"please keep bolfak.models.STATUSES in sync with bolfak.i18n.STATUS_TEXT_TRANSLATIONS\")\n134 \n135 \n136 ORG_ROLE_TRANSLATIONS = {\n", "col_offset": 0, "filename": "bolfak/models.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 132, "line_range": [132, 133, 134, 135], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "33         username = ''\n34         password = ''\n35 \n36         logger.info('Using ID06 sub-account for CS monitoring interested_org_id=%s '\n", "col_offset": 19, "filename": "bolfak/mixins/cs_account_credentials_mixin.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: ''", "line_number": 34, "line_range": [34, 35], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "25         if allow_unauthenticated:\n26             assert not require_admin\n27             return callback\n", "col_offset": 12, "filename": "bolfak/plugins/auth_check_plugin.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 26, "line_range": [26], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "62 def delete_organisations(qvarn, prefix):\n63     assert prefix, 'prefix \"%s\" cannot be empty'\n64     orgs_to_delete = qvarn.search('orgs', names__startswith=prefix)\n", "col_offset": 4, "filename": "bolfak/scripts/generate_loadtest_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 63, "line_range": [63], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "85 def delete_reports(storage, prefix):\n86     assert prefix, 'prefix \"%s\" cannot be empty'\n87 \n88     qvarn = storage.qvarn\n", "col_offset": 4, "filename": "bolfak/scripts/generate_loadtest_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 86, "line_range": [86, 87], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "162 def create_suppliers(qvarn, num_suppliers, projects, orgs, org_persons):\n163     assert len(orgs) >= num_suppliers, 'Cannot create more suppliers than there are orgs'\n164     org_iterator = itertools.cycle(orgs)\n", "col_offset": 4, "filename": "bolfak/scripts/generate_loadtest_data.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 163, "line_range": [163], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "21 SCIM_RESOURCE_ID = 1203.825aa654-a95d-4195-a75d-1d6720ed357b\n22 USER_DEFAULT_PASSWORD = 'TestPass1'\n23 \n24 \n25 def setup_scim_client(conf):\n", "col_offset": 24, "filename": "bolfak/scripts/sync_users.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'TestPass1'", "line_number": 22, "line_range": [22, 23, 24], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "15 import dateutil.parser\n16 from lxml import etree\n17 \n18 from bolfak import __version__\n", "col_offset": 0, "filename": "bolfak/scripts/validate_report.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "LOW", "issue_text": "Using etree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace etree with the equivalent defusedxml package.", "line_number": 16, "line_range": [16, 17], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_imports.html#b410-import-lxml", "test_id": "B410", "test_name": "blacklist"}, {"code": "420     try:\n421         tree = etree.fromstring(xml)\n422         return tree.find('{se/skatteverket/foretagsregistrering/instans/1.0}Refnr').text\n", "col_offset": 15, "filename": "bolfak/scripts/validate_report.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "MEDIUM", "issue_text": "Using lxml.etree.fromstring to parse untrusted XML data is known to be vulnerable to XML attacks. Replace lxml.etree.fromstring with its defusedxml equivalent function.", "line_number": 421, "line_range": [421], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b313-b320-xml-bad-etree", "test_id": "B320", "test_name": "blacklist"}, {"code": "119 def get_active_org_representation(user_profile):\n120     assert user_profile is not None, 'User profile must not be None'\n121     org_id = get_active_org_id_from_request(bottle.request)\n", "col_offset": 4, "filename": "bolfak/services/authentication.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 120, "line_range": [120], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "480     provider = DataProvider(config, storage, cache_id=job.id)\n481     assert isinstance(provider, CompanyInfoProvider)\n482 \n483     orgs = list(orgs)\n", "col_offset": 4, "filename": "bolfak/services/bulkimport.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 481, "line_range": [481, 482], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "613     provider = DataProvider(config, storage, cache_id)\n614     assert isinstance(provider, CompanyInfoProvider)\n615     return provider\n", "col_offset": 4, "filename": "bolfak/services/bulkimport.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 614, "line_range": [614], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "67     errors = []\n68     assert isinstance(e.messages, dict)  # could be list/tuple\n69     for token, messages in e.messages.items():\n", "col_offset": 4, "filename": "bolfak/services/imports.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 68, "line_range": [68], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "69     for token, messages in e.messages.items():\n70         assert isinstance(messages, (str, list))  # could be dict?\n71         if not isinstance(messages, list):\n", "col_offset": 8, "filename": "bolfak/services/imports.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 70, "line_range": [70], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "233     else:\n234         assert reports.split_point == until\n235         assert reports.org_ids is None or (\n", "col_offset": 8, "filename": "bolfak/services/notifications.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 234, "line_range": [234], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "234         assert reports.split_point == until\n235         assert reports.org_ids is None or (\n236             org_ids is not None and set(reports.org_ids) >= set(org_ids))\n237 \n", "col_offset": 8, "filename": "bolfak/services/notifications.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 235, "line_range": [235, 236], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "302     else:\n303         assert reports.org_ids is None or reports.org_ids >= supplier_orgs\n304     reports_for = reports.by_org_and_interested_org\n", "col_offset": 8, "filename": "bolfak/services/notifications.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 303, "line_range": [303], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "413 \n414                 assert user['notify']\n415                 assert user['has_bol_permission']\n", "col_offset": 16, "filename": "bolfak/services/notifications.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 414, "line_range": [414], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "414                 assert user['notify']\n415                 assert user['has_bol_permission']\n416 \n417                 email = user['user_info']['email']\n", "col_offset": 16, "filename": "bolfak/services/notifications.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 415, "line_range": [415, 416], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "121 def get_project_id(project, *, default=None, id_type=PROJECT_ID_TYPE):\n122     assert id_type != PROJECT_ID_TYPE, 'Use get_project_internal_id to retrieve Internal Project ID'\n123     try:\n", "col_offset": 4, "filename": "bolfak/services/projects.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 122, "line_range": [122], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "255             continue\n256         assert org_id == org['id']\n257         orgs[org_id] = org\n", "col_offset": 8, "filename": "bolfak/services/statusreports.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 256, "line_range": [256], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "46     qvarn = storage.qvarn\n47     assert role in PROJECT_USER_ROLES\n48     org = companies.find_company_by_reg_no(qvarn, org_reg_no)\n", "col_offset": 4, "filename": "bolfak/services/testdata.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 47, "line_range": [47], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "180             country = Creditsafe.SUPPORTED_COUTRIES[0]\n181             assert len(Creditsafe.SUPPORTED_COUTRIES) == 1\n182 \n", "col_offset": 12, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 181, "line_range": [181], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "239     def _fetch_and_parse(self, client, xml_returning_client, token, block_name):\n240         assert block_name in (CREDITSAFE_BASIC_BLOCK, CREDITSAFE_FULL_BLOCK)\n241         try:\n", "col_offset": 8, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 240, "line_range": [240], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "257     def _fetch_entry(self, client, token, block_name):\n258         assert block_name in (CREDITSAFE_BASIC_BLOCK, CREDITSAFE_FULL_BLOCK)\n259 \n260         cache_name = RESPONSE_CACHE_NAME\n", "col_offset": 8, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 258, "line_range": [258, 259], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "287     def _make_request(self, client, token, block_name):\n288         assert block_name in (CREDITSAFE_BASIC_BLOCK, CREDITSAFE_FULL_BLOCK)\n289         username, password = self.get_credentials(block_name, token)\n", "col_offset": 8, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 288, "line_range": [288], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "305         # For basic company information use master ID06 account\n306         assert block_name == CREDITSAFE_BASIC_BLOCK\n307         logger.info('Using ID06 account username and password for org=%s', token)\n", "col_offset": 8, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 306, "line_range": [306], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "396 \n397         assert token.is_private_person\n398         try:\n", "col_offset": 8, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 397, "line_range": [397], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "509         username = ''\n510         password = ''\n511 \n512         # For basic company information use master ID06 account\n513         if block_name == CREDITSAFE_BASIC_BLOCK:\n", "col_offset": 19, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: ''", "line_number": 510, "line_range": [510, 511, 512], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "518         # For report information use sub-accounts\n519         assert block_name == CREDITSAFE_FULL_BLOCK\n520 \n521         interested_org_id = self.interested_org_id\n", "col_offset": 8, "filename": "bolfak/statusreports/creditsafe.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 519, "line_range": [519, 520], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "6 import shlex\n7 import subprocess\n8 import tempfile\n", "col_offset": 0, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 7, "line_range": [7], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "12 \n13 from lxml import etree\n14 \n15 from bolfak.clients.qvarn import QvarnResultDict\n", "col_offset": 0, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "LOW", "issue_text": "Using etree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace etree with the equivalent defusedxml package.", "line_number": 13, "line_range": [13, 14], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_imports.html#b410-import-lxml", "test_id": "B410", "test_name": "blacklist"}, {"code": "100     logger.debug('$ %s' % ' '.join(map(shlex.quote, cmd)))\n101     subprocess.check_call(cmd)\n102 \n", "col_offset": 4, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 101, "line_range": [101], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "185 \n186     enquiry = etree.fromstring(content)\n187     xsd = etree.fromstring(SKATTEVERKET_INSTANS_SCHEMA)\n", "col_offset": 14, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "MEDIUM", "issue_text": "Using lxml.etree.fromstring to parse untrusted XML data is known to be vulnerable to XML attacks. Replace lxml.etree.fromstring with its defusedxml equivalent function.", "line_number": 186, "line_range": [186], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b313-b320-xml-bad-etree", "test_id": "B320", "test_name": "blacklist"}, {"code": "186     enquiry = etree.fromstring(content)\n187     xsd = etree.fromstring(SKATTEVERKET_INSTANS_SCHEMA)\n188 \n", "col_offset": 10, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "MEDIUM", "issue_text": "Using lxml.etree.fromstring to parse untrusted XML data is known to be vulnerable to XML attacks. Replace lxml.etree.fromstring with its defusedxml equivalent function.", "line_number": 187, "line_range": [187], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b313-b320-xml-bad-etree", "test_id": "B320", "test_name": "blacklist"}, {"code": "309     try:\n310         xml = strip_namespaces(etree.fromstring(response))\n311         return xml.find('Refnr').text\n", "col_offset": 31, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "MEDIUM", "issue_text": "Using lxml.etree.fromstring to parse untrusted XML data is known to be vulnerable to XML attacks. Replace lxml.etree.fromstring with its defusedxml equivalent function.", "line_number": 310, "line_range": [310], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b313-b320-xml-bad-etree", "test_id": "B320", "test_name": "blacklist"}, {"code": "544 \n545         xml = strip_namespaces(etree.fromstring(data))\n546 \n", "col_offset": 31, "filename": "bolfak/statusreports/skatteverket.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 20, "link": "https://cwe.mitre.org/data/definitions/20.html"}, "issue_severity": "MEDIUM", "issue_text": "Using lxml.etree.fromstring to parse untrusted XML data is known to be vulnerable to XML attacks. Replace lxml.etree.fromstring with its defusedxml equivalent function.", "line_number": 545, "line_range": [545], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_calls.html#b313-b320-xml-bad-etree", "test_id": "B320", "test_name": "blacklist"}, {"code": "181         resource = self.patterns.resources.search(request.url).group(1)\n182         assert resource in self.storage, resource\n183 \n184         doc = request.json()\n", "col_offset": 8, "filename": "bolfak/testing/qvarn/pretenderqvarn.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 182, "line_range": [182, 183], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "200         resource, resource_id = self.patterns.resource.search(request.url).groups()\n201         assert resource in self.storage, resource\n202 \n203         doc = request.json()\n", "col_offset": 8, "filename": "bolfak/testing/qvarn/pretenderqvarn.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 201, "line_range": [201, 202], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "224         resource, resource_id, subresource = self.patterns.subresource.search(request.url).groups()\n225         assert resource in self.storage, resource\n226 \n227         if resource_id not in self.storage[resource]:\n", "col_offset": 8, "filename": "bolfak/testing/qvarn/pretenderqvarn.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 225, "line_range": [225, 226], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "357         value = [v.lower() for v in json.loads(value)]\n358         assert value != [], \"<PERSON><PERSON><PERSON> breaks if you pass [] to __any queries\"  # see BOL-1978.\n359         def condition(k, v):  # noqa: blank lines above inner functions look bad to me\n", "col_offset": 8, "filename": "bolfak/testing/qvarn/pretenderqvarn.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 358, "line_range": [358], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "259             schema = yaml.safe_load(content)\n260             assert schema['path'].startswith('/')\n261             path = schema['path'][1:]  # strip '/' prefix\n", "col_offset": 12, "filename": "bolfak/testing/qvarn/realqvarn.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 260, "line_range": [260], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "3 import pathlib\n4 import subprocess\n5 import time\n", "col_offset": 0, "filename": "bolfak/testing/services.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 4, "line_range": [4], "more_info": "https://bandit.readthedocs.io/en/1.7.3/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "46     ]\n47     subprocess.run(docker_compose + ['up', '-d', 'db-test'], cwd=str(docker_compose_dir))\n48     wait_for_postgres(engine)\n", "col_offset": 4, "filename": "bolfak/testing/services.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 47, "line_range": [47], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "50     try:\n51         subprocess.Popen(docker_compose + ['rm', '-sf', 'db-test'], cwd=str(docker_compose_dir),\n52                          stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL).wait(timeout=2.0)\n53     except subprocess.TimeoutExpired:\n", "col_offset": 8, "filename": "bolfak/testing/services.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 51, "line_range": [51, 52], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "81 def get_bisnode_response(*, countryRegNumber='05450167731'):\n82     env = Environment(loader=FileSystemLoader(get_fixture_path('statusreports')))\n83     template = env.get_template('bisnode.xml')\n", "col_offset": 10, "filename": "bolfak/testing/statusreports.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 94, "link": "https://cwe.mitre.org/data/definitions/94.html"}, "issue_severity": "HIGH", "issue_text": "By default, jinja2 sets autoescape to False. Consider using autoescape=True or use the select_autoescape function to mitigate XSS vulnerabilities.", "line_number": 82, "line_range": [82], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b701_jinja2_autoescape_false.html", "test_id": "B701", "test_name": "jinja2_autoescape_false"}, {"code": "37     \"\"\"Serialize a date to JSON.\"\"\"\n38     assert isinstance(date, (str, datetime.date, type(None)))\n39     if isinstance(date, datetime.date):\n", "col_offset": 4, "filename": "bolfak/utils.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 38, "line_range": [38], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "102     \"\"\"\n103     assert isinstance(_status, int)  # don't confuse error messages with status codes\n104     abort(\n", "col_offset": 4, "filename": "bolfak/views/common.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 103, "line_range": [103], "more_info": "https://bandit.readthedocs.io/en/1.7.3/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}]}