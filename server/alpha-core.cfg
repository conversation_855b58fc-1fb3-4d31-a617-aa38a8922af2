[main]
base_path = /api
static_path = /api/static/
static_map = ../build/client/static/SHA256SUMS
host = 0.0.0.0
port = 7002
debug = true
insecure_testdata_api = true
insecure_test_api = true
swagger_api = true
frontend_url = http://localhost:8080/
standalone_url = http://localhost:8082/
backend_url = http://localhost:7002/api/
auth_data_caching_duration_in_seconds = 5
service_provider = id06
bol_permission = bolagsdeklaration_user
content_config_name = id06-fs3.yaml
# Contracts not used in core
#person_org_contract_type = user_account
#org_contract_type = customership
shared_cookie_domain = localhost
locale_dir = locale/id06
# Default storage is pimcore not qvarn
#default_storage = qvarn
# TODO point companies_url to somewhere at mitt id06
companies_url =
# TODO user details edit link should point to mittid06
change_user_details_url =

[bol-data-api]
base_url = https://bol-data-api-core.alpha.id06.se
client_id = 28a036c2-c64f-41a9-9758-db4cea4a5656
client_secret = 6HzvULKONTIdci26JCVi7d0n95KE4BJKk7NYiyQf
verify_requests = true
scope =
    uapi_reports_search_id_get,

    uapi_ext_bol_project_list_get,
    uapi_ext_bol_project_suppliers_get,
    uapi_ext_bol_company_list_get,

    bda_project_users_get,
    bda_project_users_post,
    bda_project_users_put,
    bda_project_users_delete,
    bda_project_users_search,

    uapi_data_cache_get,
    uapi_data_cache_id_delete,
    uapi_data_cache_id_get,
    uapi_data_cache_id_put,
    uapi_data_cache_post,
    uapi_data_cache_search_id_get,

    bda_creditsafe_account_get,
    bda_creditsafe_account_post,
    bda_creditsafe_account_put,
    bda_creditsafe_account_delete,
    bda_creditsafe_account_search,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

[core-system-api]
base_url = https://pimcore.alpha.id06.se
client_id = 28a036c2-c64f-41a9-9758-db4cea4a5656
client_secret = 6HzvULKONTIdci26JCVi7d0n95KE4BJKk7NYiyQf
verify_requests = true
scope =
    view_extended:organisation_person,
    view:person,
    view:organisation,
    subscribe:organisationrecord,
    view:organisationrecord,
    view_report:organisationrecord,

# See https://id06.atlassian.net/browse/BOL-6221 for re-enabling this integration & feature-flag
[stamp]
base_url = https://stampdata-api.alpha.vaultit.org
verify_requests = true
scopes =
   stampdata:visitor_companies_get

[celery]
broker_url = amqp://localhost
task_default_queue = celery-que-bol-local
task_default_exchange = celery-exc-bol-local

[qvarn]
verify_requests = true
base_url = http://NOT_USED
client_id = 28a036c2-c64f-41a9-9758-db4cea4a5656
client_secret = 6HzvULKONTIdci26JCVi7d0n95KE4BJKk7NYiyQf
scope =
    # BDA scopes
    uapi_projects_get,
    uapi_projects_id_delete,
    uapi_projects_id_get,
    uapi_projects_id_put,
    uapi_projects_post,
    uapi_projects_search_id_get,

    # BDA scopes
    uapi_reports_get,
    uapi_reports_post,
    uapi_reports_id_get,
    uapi_reports_id_put,
    uapi_reports_id_delete,
    uapi_reports_id_pdf_get,
    uapi_reports_id_pdf_put,
    uapi_reports_search_id_get,

    # BDA scopes
    uapi_bol_suppliers_get,
    uapi_bol_suppliers_id_delete,
    uapi_bol_suppliers_id_get,
    uapi_bol_suppliers_id_put,
    uapi_bol_suppliers_post,
    uapi_bol_suppliers_search_id_get,

    # BDA scopes
    uapi_ext_bol_company_list_get
    uapi_ext_bol_project_list_get
    uapi_ext_bol_project_suppliers_get

threads = 10
extended_project_fields = true

url_for_bol_suppliers = https://bol-data-api-core.alpha.id06.se/api/v1/boldata
url_for_projects = https://bol-data-api-core.alpha.id06.se/api/v1/boldata
url_for_reports = https://bol-data-api-core.alpha.id06.se/api/v1/boldata


[gluu]
base_url = https://auth-core-alpha.id06.se
end_session_support = true

[sessions]
type = file
cookie_name = session_id
cookie_domain =
cookie_path = /api/
timeout = 14400
data_dir = var/sessions
httponly = true
secure = false
encrypt_key = 'very'
validate_key = 'secret'

# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[formatters]
keys = jsonl, console, file

[formatter_jsonl]
class = bolfak.logging.JsonFormatter
format =
    [
        "asctime", "process", "levelname", {
            "message": "message",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName",
            "logger": "name",
            "request": "requestId",
            "user": "user"
        }
    ]

[formatter_console]
class = bolfak.logging.Formatter
format = %(asctime)s [%(levelname)s] %(message)s

[formatter_file]
class = bolfak.logging.Formatter
format = %(asctime)s %(programName)s[%(process)d] [%(levelname)s] %(message)s


[handlers]
keys = console, debug, file, jsonl

[handler_jsonl]
class = logging.handlers.RotatingFileHandler
args = ('var/app.log.jsonl', 'a', 1000000, 5)
formatter = jsonl
level = DEBUG

[handler_file]
class = logging.handlers.RotatingFileHandler
args = ('var/app.log', 'a', 1000000, 5)
formatter = file
level = INFO

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = INFO

[handler_debug]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = DEBUG


[loggers]
keys = root, roles, bulkimports

[logger_root]
level = DEBUG
handlers = console, file, jsonl

[logger_roles]
level = DEBUG
qualname = bolfak.services.roles
handlers = debug

[logger_bulkimports]
level = DEBUG
qualname = bolfak.services.bulkimport
handlers = debug


# Company report providers

[statusreports]
bulkimport_data_provider = bolfak.statusreports.sandbox.SandboxInfoProvider
#report_data_provider = bolfak.statusreports.noop.NoopProvider
report_swedish_data_provider = bolfak.statusreports.noop.NoopProvider
report_foreign_data_provider = bolfak.statusreports.creditsafe_connect_core.CreditsafeConnectCoreReportProvider
tax_data_provider = bolfak.statusreports.noop.NoopProvider


[statusreports.sandbox]
sleep = 2


[statusreports.bisnode]
user_id =
user_password =
customer_code = BJA8
customer_code_owner = 022551
language = SE
from_country = SE
to_country = SE


[statusreports.creditsafe]
use_test_server = true
username =
password =
lod_cust_free_text = The report is taken on behalf of ID06
symmetric_secret_key = secret_terces


[statusreports.creditsafe_ggs]
creditsafe_ggs_wsdl_username =
creditsafe_ggs_wsdl_password =


[autoaccount]
autoaccount_url = https://webservice.creditsafe.se/AutoAccount/AutoAccountService.asmx?WSDL
autoaccount_username = # Get from Alpha secrets
autoaccount_password = # Get from Alpha secrets
autoaccount_email = <EMAIL>
autoaccount_request_package = ID06_TEST_AA
autoaccount_use_testing_org_gov_org_id = True
autoaccount_testing_org_gov_org_id = **********
autoaccount_id06_gov_org_id = **********

[feature-flags]
extended_report = True
lazy_qvarn_startup = True
import_sole_traders = True
# This is for company-api, which is not in core
company_registry = False
pagination = True
bda_client = True
bda_project_suppliers = True
bda_company_list = True
# See https://id06.atlassian.net/browse/BOL-6221 for re-enabling this feature flag
visitors = True
project_report = True
pre_announcements = True
pa_form_checkbox_disabled = True
non_paed_suppliers = True
# Contract api is not in core
contract_api_creditsafe_contract = False
require_creditsafe_contract = False
# User account api is not in core
user_account_api = False
on_azure = False
add_project_client = True
skip_pa_reg_step = True
create_and_activate_cs_accounts = True
block_project_client = True
person_id_for_project_users = True
core_mitt_id06 = True
project_supplier_comments = True
# Company-identical-api client is not in core
cqpoc_client = False
dependency_request_cache = False

# Email sending via SendGrid

[sendgrid]
sendgrid_sender = ID06 alpha <<EMAIL>>
sendgrid_api_key = *********************************************************************
