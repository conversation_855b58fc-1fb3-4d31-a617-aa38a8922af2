[uwsgi]

# Python3 plugin is needed
plugins = python3

# HTTP socket the service runs on
# http-socket = 127.0.0.1:9001
http-socket = <REPLACE ME>

# WSGI application located is located in bolfak.application
# module, tell uwsgi to load it
wsgi = bolfak.application


# Configuration file location
# pyargv = --config /etc/bolagfakta/bolagsfakta.cfg
pyargv = <REPLACE ME>

# Parallelisation should be done through processes
processes = 10

# Bolagsfakta should be run with one thread per process
threads = 1

# enable-threads is needed for proper threading support
enable-threads = 1
