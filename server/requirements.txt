#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --find-links=vendor/ --no-emit-index-url --no-emit-options --output-file=requirements.txt --resolver=backtracking requirements/prod.in
#
amqp==5.1.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
asgiref==3.8.1
    # via opentelemetry-instrumentation-asgi
async-timeout==4.0.2
    # via redis
attrs==23.1.0
    # via zeep
azure-core==1.31.0
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   msrest
azure-core-tracing-opentelemetry==1.0.0b11
    # via azure-monitor-opentelemetry
azure-monitor-opentelemetry==1.6.2
    # via stv-utils
azure-monitor-opentelemetry-exporter==1.0.0b30
    # via azure-monitor-opentelemetry
beaker==1.13.0
    # via beaker-redis
beaker-redis==1.1.0
    # via -r requirements/prod.in
billiard==4.1.0
    # via celery
bottle==0.12.25
    # via
    #   -r requirements/prod.in
    #   stv-utils
bottleswagger==1.5
    # via -r requirements/prod.in
celery==5.3.1
    # via -r requirements/prod.in
certifi==2024.7.4
    # via
    #   msrest
    #   requests
cffi==1.14.3
    # via cryptography
charset-normalizer==2.0.11
    # via requests
click==8.1.3
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
click-didyoumean==0.3.0
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.2.0
    # via celery
core-utils==1.0.3
    # via -r requirements/prod.in
cryptography==44.0.2
    # via
    #   -r requirements/prod.in
    #   qvarn-utils
deprecated==1.2.14
    # via
    #   opentelemetry-api
    #   opentelemetry-semantic-conventions
dnspython==2.6.1
    # via email-validator
email-validator==2.2.0
    # via core-utils
fixedint==0.1.6
    # via azure-monitor-opentelemetry-exporter
idna==3.7
    # via
    #   email-validator
    #   requests
importlib-metadata==8.4.0
    # via
    #   opentelemetry-api
    #   opentelemetry-instrumentation-flask
iso3166==2.1.1
    # via -r requirements/prod.in
isodate==0.6.0
    # via
    #   msrest
    #   zeep
jinja2==3.1.6
    # via -r requirements/prod.in
kombu==5.3.1
    # via celery
ldap3==2.5.1
    # via -r requirements/prod.in
lxml==4.9.1
    # via
    #   -r requirements/prod.in
    #   zeep
markupsafe==2.1.1
    # via jinja2
msrest==0.7.1
    # via azure-monitor-opentelemetry-exporter
newrelic==8.4.0
    # via -r requirements/prod.in
oauthlib==3.2.2
    # via requests-oauthlib
opentelemetry-api==1.27.0
    # via
    #   azure-core-tracing-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-instrumentation==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-psycopg2
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
opentelemetry-instrumentation-asgi==0.48b0
    # via opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-dbapi==0.48b0
    # via opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-django==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-fastapi==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-flask==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-psycopg2==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-requests==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-threading==0.48b0
    # via stv-utils
opentelemetry-instrumentation-urllib==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-urllib3==0.48b0
    # via azure-monitor-opentelemetry
opentelemetry-instrumentation-wsgi==0.48b0
    # via
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-flask
opentelemetry-resource-detector-azure==0.1.5
    # via azure-monitor-opentelemetry
opentelemetry-sdk==1.27.0
    # via
    #   azure-monitor-opentelemetry
    #   azure-monitor-opentelemetry-exporter
    #   opentelemetry-resource-detector-azure
opentelemetry-semantic-conventions==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
    #   opentelemetry-sdk
opentelemetry-util-http==0.48b0
    # via
    #   opentelemetry-instrumentation-asgi
    #   opentelemetry-instrumentation-django
    #   opentelemetry-instrumentation-fastapi
    #   opentelemetry-instrumentation-flask
    #   opentelemetry-instrumentation-requests
    #   opentelemetry-instrumentation-urllib
    #   opentelemetry-instrumentation-urllib3
    #   opentelemetry-instrumentation-wsgi
packaging==24.1
    # via opentelemetry-instrumentation-flask
platformdirs==4.3.8
    # via zeep
prompt-toolkit==3.0.38
    # via click-repl
psutil==5.9.4
    # via azure-monitor-opentelemetry-exporter
pyasn1==0.4.8
    # via ldap3
pycparser==2.14
    # via cffi
pycryptodome==3.20.0
    # via -r requirements/prod.in
pydantic==2.9.1
    # via core-utils
pydantic-core==2.23.3
    # via pydantic
pyjwt==2.10.1
    # via
    #   -r requirements/prod.in
    #   qvarn-utils
    #   stv-utils
python-dateutil==2.8.2
    # via
    #   -r requirements/prod.in
    #   celery
    #   qvarn-utils
    #   stv-utils
python-http-client==3.3.7
    # via sendgrid
python-stdnum==1.9
    # via -r requirements/prod.in
pytz==2021.3
    # via
    #   -r requirements/prod.in
    #   stv-utils
    #   zeep
pyyaml==6.0.1
    # via
    #   bottleswagger
    #   yamlordereddictloader
qvarn-utils==2.26
    # via
    #   -r requirements/prod.in
    #   stv-utils
redis==4.5.4
    # via
    #   -r requirements/prod.in
    #   beaker-redis
requests==2.32.4
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   msrest
    #   qvarn-utils
    #   requests-file
    #   requests-futures
    #   requests-oauthlib
    #   requests-toolbelt
    #   stv-utils
    #   zeep
requests-file==2.1.0
    # via zeep
requests-futures==1.0.1
    # via
    #   -r requirements/prod.in
    #   qvarn-utils
    #   stv-utils
requests-oauthlib==2.0.0
    # via msrest
requests-toolbelt==1.0.0
    # via
    #   -r requirements/prod.in
    #   zeep
schematics==2.1.1
    # via -r requirements/prod.in
sendgrid==6.10.0
    # via -r requirements/prod.in
six==1.16.0
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   click-repl
    #   isodate
    #   python-dateutil
starkbank-ecdsa==2.2.0
    # via sendgrid
stv-utils==3.0.6
    # via -r requirements/prod.in
suds-community==1.1.2
    # via -r requirements/prod.in
treelib==1.3.5
    # via -r requirements/prod.in
typing-extensions==4.12.2
    # via
    #   -r requirements/prod.in
    #   azure-core
    #   opentelemetry-sdk
    #   pydantic
    #   pydantic-core
tzdata==2023.3
    # via celery
unidecode==0.4.16
    # via -r requirements/prod.in
urllib3==2.5.0
    # via requests
uwsgidecorators==1.1.0
    # via -r requirements/prod.in
vine==5.0.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.5
    # via prompt-toolkit
wrapt==1.15.0
    # via
    #   deprecated
    #   opentelemetry-instrumentation
    #   opentelemetry-instrumentation-dbapi
    #   opentelemetry-instrumentation-threading
    #   opentelemetry-instrumentation-urllib3
xmltodict==0.13.0
    # via -r requirements/prod.in
yamlordereddictloader==0.3.0
    # via -r requirements/prod.in
zeep==4.3.1
    # via
    #   -r requirements/prod.in
    #   stv-utils
zipp==3.20.2
    # via importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
