import json
from urllib.parse import urljoin

import bottle

import bolfak
from bolfak.base import BaseBottle
from bolfak.config import (
    get_ai_connection_string,
    get_bol_permission,
    get_config,
    get_no_permission_redirect_url,
    get_service_portal_url,
)
from bolfak.decorators import allow_anonymous
from bolfak.featureflags import FEATURE_FLAGS, feature_active


app = BaseBottle()


@app.get('/config.js')
@allow_anonymous
def config_js():
    """Output a config.js containing feature flags and other configuration.

    These configuration values are independent from the logged in user.

    ---
    responses:
      200:
        description: JavaScript code that sets `window.bolfak_config`.
        content:
          "application/javascript":
            example: |
              window.bolfak_config = {
                'version': '1.8.2',
                'feature_flags': {
                  'archived_reports': false,
                  ...
                },
                'bol_permission': 'bolagsdeklaration_user',
                'no_permission_url': 'https://...',
                'register_new_company_url': 'https://...',
                'page_size': 100,
                'change_user_details_url': 'https://...',
                'backend_url': 'https://...',
              };
    """

    bottle.response.content_type = 'application/javascript'

    config = get_config()

    feature_flags = {}
    for flag in FEATURE_FLAGS.keys():
        feature_flags[flag] = feature_active(flag)

    if feature_active('core_mitt_id06'):
        register_new_company_url = None
        no_permission_url = None
    else:
        no_permission_url = get_no_permission_redirect_url(config)
        service_portal_url = get_service_portal_url(config)
        if feature_active('use_stv_theme'):
            register_new_company_url = urljoin(service_portal_url, '/#/registration/findcompany')
        else:
            register_new_company_url = urljoin(service_portal_url, '/#/registration/createaccount')

    parameters = {
        'ai_connection_string': get_ai_connection_string(config),
        'version': bolfak.__version__,
        'feature_flags': feature_flags,
        'bol_permission': get_bol_permission(config),
        'no_permission_url': no_permission_url,
        'register_new_company_url': register_new_company_url,
        'default_storage': config.get('main', 'default_storage', fallback='N/A'),
        'page_size': config.getint('main', 'page_size', fallback=100),
        'change_user_details_url': config.get('main', 'change_user_details_url', fallback=''),
        'backend_url': config.get('main', 'backend_url', fallback=''),
        'standalone_url': (config.get('main', 'standalone_url', fallback='')
                           or config.get('main', 'frontend_url', fallback='')),
    }

    # Return a Javascript representation of the config parameters
    return 'window.bolfak_config = {};'.format(json.dumps(parameters))
