import json
from typing import Any
from bolfak.clients.composite import Storage
from bolfak.storage.qvarn.projects import PROJECT_STATES_NOT_CLOSED
from bolfak.storage.bda_helper import get_multiple_entities_in_batches


def get_projects(storage, *, ids=None, states=None, pa_form_enabled=None,
                 project_responsible_org=None):
    query = dict(
        project_responsible_org__ne='',
    )

    if states is not None:
        query["state__any"] = states

    if pa_form_enabled is not None:
        query["pa_form_enabled"] = pa_form_enabled

    if project_responsible_org is not None:
        query["project_responsible_org"] = project_responsible_org

    if ids is not None:
        return get_multiple_entities_in_batches(
            storage,
            entity_name="projects",
            query_ids=ids,
            query_key="id",
            additional_filters=query,
        )
    else:
        response = storage.bda.get(f"/projects/query?q={json.dumps(query)}")
        return response.json()["resources"]


def get_non_closed_projects(storage):
    return get_projects(storage, states=PROJECT_STATES_NOT_CLOSED)


def update_project(storage: Storage, project_id: str, data: dict[str, Any]) -> dict[str, Any]:
    response = storage.bda.put(f'/projects/{project_id}', json=data)
    return response.json()
