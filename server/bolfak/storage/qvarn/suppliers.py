import datetime
import json
from typing import Collection, Dict, Optional, Union, cast, Any

from bolfak.clients.composite import Storage
from bolfak.featureflags import feature_active
from bolfak.models import (
    MAIN_CONTRACTOR_ROLE,
    SUPERVISOR_ROLE,
    SUPPLIER_ROLE,
    SUPPLIER_ROLES,
    SUPPLIER_TYPES,
    VISITOR,
)
from bolfak.services.resources import R
from bolfak.storage.projects import get_projects
from bolfak.storage.qvarn.models import QvarnSupplierResultDict
from bolfak.utils import serialize_date
from bolfak.storage.bda_helper import get_multiple_entities_in_batches


def get_supplier_payload(project_resource_id=None,
                         supplier_type=None,
                         parent_org_id=None,
                         supplier_org_id=None,
                         materialized_path=None,
                         contract_start_date=None,
                         contract_end_date=None,
                         contract_work_areas=None,
                         parent_supplier_id=None,
                         supplier_role=None,
                         supplier_contacts=None,
                         first_visited=None,
                         last_visited=None,
                         contract_type=None,
                         visitor_type=None,
                         is_one_man_company=None,
                         has_collective_agreement=None,
                         collective_agreement_name=None):
    # Defaults
    if supplier_contacts is None:
        supplier_contacts = []
    if materialized_path is None:
        materialized_path = []
    if supplier_role is None:
        supplier_role = SUPPLIER_ROLE
    if first_visited is not None:
        if not isinstance(first_visited, datetime.datetime):
            raise ValueError('Expected datetime for `first_visited` but got %s',
                             first_visited)
    if last_visited is not None:
        if not isinstance(last_visited, datetime.datetime):
            raise ValueError('Expected datetime for `last_visited` but got %s',
                             last_visited)

    # Rudimentary validation
    if supplier_type not in SUPPLIER_TYPES:
        raise ValueError('supplier.supplier_type should be one of %s' % SUPPLIER_TYPES)
    if supplier_role not in SUPPLIER_ROLES:
        raise ValueError('supplier.supplier_ro should be one of %s' % SUPPLIER_ROLES)

    data = {
        'project_resource_id': project_resource_id,
        'supplier_type': supplier_type,
        'parent_org_id': parent_org_id,
        'supplier_org_id': supplier_org_id,
        'materialized_path': materialized_path,
        'contract_start_date': serialize_date(contract_start_date),
        'contract_end_date': serialize_date(contract_end_date),
        'contract_work_areas': contract_work_areas,
        'parent_supplier_id': parent_supplier_id,
        'supplier_role': supplier_role,
        'supplier_contacts': supplier_contacts,
        'contract_type': contract_type,
        'visitor_type': visitor_type,
        'is_one_man_company': is_one_man_company,
        'has_collective_agreement': has_collective_agreement,
        'collective_agreement_name': collective_agreement_name
    }
    if feature_active('visitors'):
        data['first_visited'] = first_visited
        data['last_visited'] = last_visited
    return data


# UPSERT

def upsert_supplier(storage: Storage,
                    supplier_resource_id=None,
                    revision=None,
                    project_resource_id=None,
                    supplier_type=None,
                    parent_org_id=None,
                    supplier_org_id=None,
                    parent_supplier_id=None,
                    materialized_path=None,
                    contract_start_date=None,
                    contract_end_date=None,
                    contract_work_areas=None,
                    supplier_role=None,
                    supplier_contacts=None,
                    first_visited=None,
                    last_visited=None,
                    visitor_type=None,
                    contract_type=None,
                    is_one_man_company=None,
                    has_collective_agreement=None,
                    collective_agreement_name=None) -> QvarnSupplierResultDict:
    payload = get_supplier_payload(project_resource_id=project_resource_id,
                                   supplier_type=supplier_type,
                                   parent_org_id=parent_org_id,
                                   supplier_org_id=supplier_org_id,
                                   materialized_path=materialized_path,
                                   contract_start_date=contract_start_date,
                                   contract_end_date=contract_end_date,
                                   contract_work_areas=contract_work_areas,
                                   parent_supplier_id=parent_supplier_id,
                                   supplier_role=supplier_role,
                                   supplier_contacts=supplier_contacts,
                                   first_visited=first_visited,
                                   last_visited=last_visited,
                                   visitor_type=visitor_type,
                                   contract_type=contract_type,
                                   is_one_man_company=is_one_man_company,
                                   has_collective_agreement=has_collective_agreement,
                                   collective_agreement_name=collective_agreement_name)
    if revision is not None:
        payload['revision'] = revision
    if supplier_resource_id is not None:
        return update_supplier(storage, supplier_resource_id, payload)
    else:
        return create_supplier(storage, payload)


# CREATE

def create_supplier(storage: Storage, payload: Dict) -> QvarnSupplierResultDict:
    qvarn = storage.qvarn

    return cast(QvarnSupplierResultDict, qvarn.create(R.SUPPLIERS, payload))


def create_supplier_visitor(storage, project=None, supplier_company_id=None, visitor_type=None):
    return upsert_supplier(
        storage,
        project_resource_id=project['id'],
        supplier_org_id=supplier_company_id,
        materialized_path=[project['project_responsible_org'], supplier_company_id],
        supplier_type=VISITOR,
        visitor_type=visitor_type,
    )


# READ

def get_supplier(storage: Storage, id_: str) -> QvarnSupplierResultDict:
    qvarn = storage.qvarn

    return cast(QvarnSupplierResultDict, qvarn.get(R.SUPPLIERS, id_))


def get_multiple_suppliers(storage: Storage, ids: list[str]) -> list[QvarnSupplierResultDict]:
    qvarn = storage.qvarn

    return cast(list[QvarnSupplierResultDict], qvarn.get_multiple(R.SUPPLIERS, ids))


def _get_all_suppliers(storage, *, batch_size=1000):
    # We need to load ALL THE SUPPLIERS here!  There are hundreds of thousands of records,
    # so we need to be careful to avoid haproxy timeouts.
    #
    # To discourage its use in other modules without thinking, this function is
    # left with underscore prefix.
    all_supplier_ids = get_all_suppliers_ids(storage)
    if not all_supplier_ids:
        return []
    all_supplier_ids.sort()
    all_suppliers = []
    for batch in range(0, len(all_supplier_ids), batch_size):
        query = {}
        if batch:
            # All but the first batch get a lower limit
            query['id__ge'] = all_supplier_ids[batch]
        if batch + batch_size < len(all_supplier_ids):
            # All but the last batch get an upper limit
            query['id__lt'] = all_supplier_ids[batch + batch_size]
        show = [
            'project_resource_id',
            'supplier_org_id',
            'parent_supplier_id',
            'supplier_role',
            'supplier_type'
        ]
        response = storage.bda.get(
            f'/bol_suppliers/query?q={json.dumps(query)}&show={json.dumps(show)}'
        )
        suppliers = response.json()['resources']
        all_suppliers += suppliers

    return all_suppliers


def get_all_suppliers_ids(storage):
    qvarn = storage.qvarn

    return qvarn.get_list(R.SUPPLIERS)


def get_all_suppliers_w_selector(qvarn, select):
    return qvarn.search(R.SUPPLIERS, show=select)


def get_project_tree_suppliers(storage: Storage,
                               project_resource_id: str) -> list[QvarnSupplierResultDict]:
    qvarn = storage.qvarn

    supplier_ids = get_project_supplier_ids(storage, project_resource_id)
    suppliers = cast(list[QvarnSupplierResultDict], qvarn.get_multiple(R.SUPPLIERS, supplier_ids))
    return suppliers


def get_one_org_supplier(qvarn, supplier_org_id: str) -> Optional[QvarnSupplierResultDict]:
    return cast(Optional[QvarnSupplierResultDict],
                qvarn.search_one(R.SUPPLIERS, supplier_org_id=supplier_org_id))


def get_all_supplier_org_ids(qvarn):
    # project_resource_id__ne='' is workaround for https://jira.tilaajavastuu.fi/browse/QS-72
    suppliers = qvarn.search(R.SUPPLIERS, show=('supplier_org_id',), project_resource_id__ne='')
    org_ids = list({supplier['supplier_org_id'] for supplier in suppliers})
    return org_ids


def get_suppliers_for_company_list(storage, query, project_ids, types=SUPPLIER_TYPES):
    suppliers = []
    for project_id in project_ids:
        suppliers += get_project_suppliers(storage, project_id, supplier_types=types)
    return suppliers


def get_project_supplier_ids(storage,
                             project_resource_id,
                             supplier_roles=SUPPLIER_ROLES,
                             supplier_types=SUPPLIER_TYPES,
                             supplier_org_ids=None,
                             batch_size=100):
    qvarn = storage.qvarn

    if supplier_org_ids:
        supplier_ids = []
        for start in range(0, len(supplier_org_ids), batch_size):
            end = start + batch_size
            batch_supplier_org_ids = supplier_org_ids[start:end]

            predicate = dict(supplier_org_id__any=batch_supplier_org_ids)
            supplier_ids += qvarn.search(
                R.SUPPLIERS,
                project_resource_id__exact=project_resource_id,
                supplier_type__any=supplier_types,
                supplier_role__any=supplier_roles,
                **predicate,
            )
        return supplier_ids
    else:
        if supplier_org_ids is not None:
            return []

    # supplier_org_ids is None, thus fetch all supplier ids for project
    return qvarn.search(
        R.SUPPLIERS,
        project_resource_id__exact=project_resource_id,
        supplier_type__any=supplier_types,
        supplier_role__any=supplier_roles,
    )


def get_projects_suppliers(
    storage: Storage,
    project_ids: Collection[str],
    select: Optional[list[str]] = None,
    supplier_roles: Optional[list[str]] = None,
    supplier_types: Optional[list[str]] = None,
) -> list[QvarnSupplierResultDict]:
    return _get_suppliers_helper(
        storage,
        query_ids=list(project_ids),
        query_key="project_resource_id",
        select=select,
        supplier_roles=supplier_roles,
        supplier_types=supplier_types,
    )


def get_orgs_suppliers(
    storage: Storage,
    org_ids: Collection[str],
    select: Optional[list[str]] = None,
    supplier_roles: Optional[list[str]] = None,
    supplier_types: Optional[list[str]] = None,
) -> list[QvarnSupplierResultDict]:
    return _get_suppliers_helper(
        storage,
        query_ids=list(org_ids),
        query_key="supplier_org_id",
        select=select,
        supplier_roles=supplier_roles,
        supplier_types=supplier_types,
    )


def _get_suppliers_helper(
    storage,
    query_ids: list[str],
    query_key: str,
    select: Optional[list[str]] = None,
    supplier_roles: Optional[list[str]] = None,
    supplier_types: Optional[list[str]] = None,
) -> list[QvarnSupplierResultDict]:
    if not query_ids:
        return []
    query: dict[str, Any] = {}
    if supplier_roles is not None:
        query["supplier_role__any"] = supplier_roles
    if supplier_types is not None:
        query["supplier_type__any"] = supplier_types
    return [
        cast(QvarnSupplierResultDict, supplier)
        for supplier in get_multiple_entities_in_batches(
            storage,
            entity_name="bol_suppliers",
            query_ids=query_ids,
            query_key=query_key,
            select=select,
            additional_filters=query,
        )
    ]


def get_project_suppliers(storage: Storage,
                          project_resource_id: str,
                          *,
                          supplier_roles=SUPPLIER_ROLES,
                          supplier_types=SUPPLIER_TYPES,
                          supplier_org_ids=None) -> list[QvarnSupplierResultDict]:
    supplier_ids = get_project_supplier_ids(storage, project_resource_id,
                                            supplier_roles, supplier_types,
                                            supplier_org_ids)
    suppliers = get_multiple_suppliers(storage, supplier_ids)
    return suppliers


def get_project_supplier(*args, **kwargs) -> QvarnSupplierResultDict:
    """A wrapper around `get_project_suppliers()` that returns exactly one item."""
    many = get_project_suppliers(*args, **kwargs)
    if len(many) != 1:
        raise ValueError(f'Expected to find 1 supplier but got {len(many)}')
    return many[0]


def get_visitor_supplier(storage: Storage, project: Dict,
                         supplier_company_id: str) -> Optional[QvarnSupplierResultDict]:
    visitors = get_project_suppliers(storage,
                                     project['id'],
                                     supplier_types=[VISITOR],
                                     supplier_org_ids=[supplier_company_id])

    if len(visitors) == 0:
        return None

    return visitors[0]


def get_visitor_suppliers(storage: Storage, project: Dict,
                          supplier_company_ids: list[str]) -> list[QvarnSupplierResultDict]:
    visitors = get_project_suppliers(storage,
                                     project['id'],
                                     supplier_types=[VISITOR],
                                     supplier_org_ids=supplier_company_ids)

    return visitors


def get_project_suppliers_by_org_and_type(storage, project_id, org_id, supplier_type):
    return get_project_supplier_ids(
        storage,
        project_id,
        supplier_types=[supplier_type],
        supplier_org_ids=[org_id],
    )


def get_project_suppliers_by_org_w_selector(storage, project_id, org_id, select):
    """Deprecated: Use `get_project_suppliers()` instead, unless you know what you are doing."""
    query = {
        "project_resource_id__eq": project_id,
        "supplier_org_id__eq": org_id,
    }
    url = f"/bol_suppliers/query?q={json.dumps(query)}&show={json.dumps(select)}"
    response = storage.bda.get(url)
    return response.json()["resources"]


def get_project_suppliers_w_selector(storage, project_id, select, types=SUPPLIER_TYPES):
    """Deprecated: Use `get_project_suppliers()` instead, unless you know what you are doing."""
    return get_projects_suppliers(storage, [project_id], select, supplier_types=types)


def get_org_suppliers_w_selector(storage, org_id, select):
    """Deprecated: Use `get_project_suppliers()` instead, unless you know what you are doing."""
    return get_orgs_suppliers(storage, [org_id], select)


def get_any_project_suppliers_w_selector(storage, project_ids, select,
                                         types=SUPPLIER_TYPES):
    """Deprecated: Use `get_project_suppliers()` instead, unless you know what you are doing."""
    # qvarn's `__any` operator does not accept empty list values,
    # so if `project_ids` are empty - return empty list without querying.
    return get_projects_suppliers(storage, project_ids, select, supplier_types=types)


def get_supplier_project_ids(
    storage,
    org_id,
    supplier_roles=None,
    supplier_types=None,
    pa_form_enabled=None,
    materialized_path_contains=None,
):
    """Get a list of IDs of projects for which org_id is a supplier.

    * org_id - limit search for suppliers of the given company ID
    * supplier_role - limit search for suppliers of the given role
    * supplier_type - limit search for suppliers of the given type
    * pa_form_enabled - limit search to projects that use preanouncement forms
    * materialized_path_contains - limit search for suppliers that are
      subsuppliers of a given company ID
    """

    select = ["project_resource_id"] + (
        ["materialized_path"] if materialized_path_contains is not None else []
    )

    suppliers = get_orgs_suppliers(
        storage,
        [org_id],
        select=select,
        supplier_roles=supplier_roles,
        supplier_types=supplier_types,
    )
    # Keep suppliers that are 'nested under or equal to' supplier of
    # materialized_path_contains
    if materialized_path_contains is not None:
        suppliers = [s for s in suppliers
                     if materialized_path_contains in s['materialized_path']]

    project_ids = [s['project_resource_id'] for s in suppliers]

    if pa_form_enabled is not None:
        # Keep projects with pa_form_enabled
        projects_w_pa = get_projects(storage, ids=project_ids,
                                     pa_form_enabled=pa_form_enabled)
        project_ids = [p['id'] for p in projects_w_pa]

    return project_ids


def get_any_org_suppliers(storage, org_ids):
    """Deprecated: Use `get_project_suppliers()` instead, unless you know what you are doing."""
    qvarn = storage.qvarn
    return qvarn.search(R.SUPPLIERS, supplier_org_id__any=org_ids, show=['project_resource_id'])


def get_privileged_suppliers(storage, project_id, org_id):
    qvarn = storage.qvarn

    privileged_suppliers = []
    privileged_supplier_roles = [MAIN_CONTRACTOR_ROLE, SUPERVISOR_ROLE]
    for privileged_role in privileged_supplier_roles:
        org_supps_project_privileged = qvarn.search(
            R.SUPPLIERS,
            project_resource_id=project_id,
            supplier_org_id__exact=org_id,
            supplier_role__exact=privileged_role,
        )
        if org_supps_project_privileged:
            privileged_suppliers.extend(org_supps_project_privileged)
    return privileged_suppliers


def get_exact_parent_orgs_suppliers_w_selector(qvarn, org_id, select):
    return qvarn.search(R.SUPPLIERS, parent_org_id=org_id, show=select)


def get_suppliers_w_org_id_in_materialized_path(storage, org_id):
    qvarn = storage.qvarn

    return qvarn.search(R.SUPPLIERS,
                        materialized_path__contains=org_id,
                        show=['id', 'materialized_path'])


# UPDATE

def update_supplier(
        storage: Storage, supplier_resource_id: str,
        payload: Union[Dict[str, Any], QvarnSupplierResultDict]
) -> QvarnSupplierResultDict:
    qvarn = storage.qvarn

    return cast(QvarnSupplierResultDict, qvarn.update(R.SUPPLIERS, supplier_resource_id, payload))


def update_supplier_for_project_tree(
    storage: Storage,
    project_id: str,
    supplier_id: str,
    supplier_revision: str,
    supplier_role: str,
    supplier_contacts: list[Dict[str, Any]],
    contract_type: str,
    contract_start_date: str,
    contract_end_date: str,
    contract_work_areas: str,
    is_one_man_company: Optional[bool] = None,
    has_collective_agreement: Optional[bool] = None,
    collective_agreement_name: Optional[str] = None,
) -> QvarnSupplierResultDict:
    supplier: Any = get_supplier(storage, supplier_id)

    if supplier_role and supplier['supplier_role'] != supplier_role:
        supplier['supplier_role'] = supplier_role

    if supplier_contacts:
        supplier['supplier_contacts'] = supplier_contacts
    if contract_type:
        supplier['contract_type'] = contract_type
    if contract_start_date:
        supplier['contract_start_date'] = serialize_date(contract_start_date)
    if contract_end_date:
        supplier['contract_end_date'] = serialize_date(contract_end_date)
    if contract_work_areas is not None:
        supplier['contract_work_areas'] = contract_work_areas
    if is_one_man_company is not None:
        supplier['is_one_man_company'] = is_one_man_company
    if has_collective_agreement is not None:
        supplier['has_collective_agreement'] = has_collective_agreement
    if collective_agreement_name:
        supplier['collective_agreement_name'] = collective_agreement_name

    # If is_one_man_company is True, we need to set
    # has_collective_agreement and collective_agreement_name to None
    # "manually" since they're not required fields.
    if is_one_man_company:
        supplier['has_collective_agreement'] = None
        supplier['collective_agreement_name'] = None

    # The same idea as above applies here
    # (just that in this case we check if has_collective agreement is False)
    if has_collective_agreement is False:
        supplier['collective_agreement_name'] = None

    return update_supplier(storage, supplier_id, supplier)


def update_supplier_visitor_type(storage: Storage, supplier_id: str,
                                 visitor_type: str) -> QvarnSupplierResultDict:
    payload = {'visitor_type': visitor_type}

    return update_supplier(storage, supplier_id, payload)


def update_project_supplier_internal_id(storage, resource_id, supplier_org_id, internal_id):
    qvarn = storage.qvarn

    supplier_ids = get_project_supplier_ids(storage, resource_id,
                                            supplier_org_ids=[supplier_org_id])
    suppliers = qvarn.get_multiple(R.SUPPLIERS, supplier_ids)
    for supplier in suppliers:
        supplier['internal_project_id'] = internal_id
        qvarn.update(R.SUPPLIERS, supplier['id'], supplier)


# DELETE

def delete_multiple_suppliers(storage, supplier_ids):
    qvarn = storage.qvarn

    qvarn.delete_multiple(R.SUPPLIERS, supplier_ids)
