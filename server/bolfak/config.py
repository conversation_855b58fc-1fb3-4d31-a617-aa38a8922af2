import configparser
import logging
import logging.config
import os
import pathlib
import warnings
from urllib.parse import urljoin


logger = logging.getLogger(__name__)

_config = None
_config_filename = None


DEFAULT_BOL_PERMISSION = 'bolagsdeklaration_user'
DEFAULT_PERSON_ORG_CONTRACT_TYPE = 'tilaajavastuu_account'
DEFAULT_ORG_CONTRACT_TYPE = 'tilaajavastuu_subscription'

REPORT_PROVIDER_CONTRACT_TYPE_CREDITSAFE = 'creditsafe_account'

MAX_ORG_IDS_PER_QUERY = 100
MAX_REPORT_IDS_PER_QUERY = 1000


class ConfigMissingError(RuntimeError):
    """You need to call set_config() before get_config()."""


def read_config(config) -> configparser.RawConfigParser:
    """Read configuration from a file path or a dict.

    Args:
        config (Union[str, dict]): configuration to be read
    """
    parser = configparser.RawConfigParser()
    if isinstance(config, (str, pathlib.Path)):
        parser.read(config)
    else:
        parser.read_dict(config)
    return parser


def setup_logging(config: configparser.RawConfigParser):
    # Configure logging only if configuration is provided.
    if config.has_section('loggers'):
        # We can't disable existing loggers, because loggers are created at module level, before
        # this line.
        logging.config.fileConfig(config, disable_existing_loggers=False)


def config_to_object(config):
    class ConfigObject:
        def __init__(self, config):
            self._config = config

        def __getattr__(self, item):
            return self._config[item]

    return ConfigObject(config)


def set_config(config):
    global _config, _config_filename
    if isinstance(config, str):
        _config_filename = config
    else:
        _config_filename = '<dict>'

    if _config is not None:
        warnings.warn('set_config() called recursively', UserWarning, stacklevel=2)
        raise RuntimeError("don't call set_config() more than once without calling reset_config()")
    _config = read_config(config)
    setup_logging(_config)
    logger.info(f'Config was read from {_config_filename}')
    return _config


def update_config(section, name, value):
    global _config
    if not isinstance(_config, configparser.RawConfigParser):
        raise ConfigMissingError('You neet to call set_config() first!')
    _config.set(section, name, value)


def reset_config():
    global _config, _config_filename
    _config = None
    _config_filename = None


def get_config():
    global _config
    # Read configuration
    if _config is None:
        raise ConfigMissingError('You neet to call set_config() first!')
    return _config


def get_config_filename():
    global _config_filename
    return _config_filename


def get_person_organisation_contract_type(config=None):
    config = config or get_config()
    return config.get('main', 'person_org_contract_type',
                      fallback=DEFAULT_PERSON_ORG_CONTRACT_TYPE).strip()


def get_organisation_contract_type(config=None):
    config = config or get_config()
    return config.get('main', 'org_contract_type', fallback=DEFAULT_ORG_CONTRACT_TYPE).strip()


def get_service_portal_url(config=None):
    config = config or get_config()
    return config.get('main', 'service_portal_url')


def get_no_permission_redirect_url(config=None):
    service_portal_url = get_service_portal_url(config)
    return urljoin(service_portal_url, '/#/nopermissionforservice')


def get_service_provider(config=None):
    config = config or get_config()
    return config.get('main', 'service_provider', fallback=None)


def get_service_key(config=None):
    config = config or get_config()
    return config.get('main', 'service_key')


def get_bol_permission(config=None):
    config = config or get_config()
    return config.get('main', 'bol_permission', fallback=DEFAULT_BOL_PERMISSION)


def get_terms_of_service_version(config=None):
    config = config or get_config()
    return config.get('main', 'terms_of_service_version')


def get_report_version_id06(config=None):
    config = config or get_config()
    return config.get('statusreports', 'report_version_id06', fallback='')


def get_ai_connection_string(config=None):
    connection_string = os.getenv("APPLICATIONINSIGHTS_CONNECTION_STRING")
    return connection_string


def get_visitor_whitelist_org_ids(config=None):
    config = config or get_config()
    whitelist_str = config.get('monitoring', 'visitor_whitelist_org_ids', fallback='')
    if whitelist_str:
        return [org_id.strip() for org_id in whitelist_str.split(',') if org_id.strip()]
    return []
