"""
Enable notifications for all project users.
"""
import argparse
import logging

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.project_users import BOL_PERMISSION_NOTIFY_PROJECT_USER
from bolfak.storage.qvarn.contracts import BOLFAK_USER_CONTRACT_TYPE
from bolfak.storage.qvarn.persons import get_person
from bolfak.storage.qvarn.projects import get_project


logger = logging.getLogger(__name__)


def should_notify_user(permissions):
    return {'permission_name': BOL_PERMISSION_NOTIFY_PROJECT_USER} in permissions


def migrate(storage, *, dry_run):
    qvarn = storage.qvarn

    logger.info('Enabling notifications for all project users%s',
                ' (dry run)' if dry_run else '')
    ids = qvarn.search('contracts', contract_type=BOLFAK_USER_CONTRACT_TYPE)
    for contract in qvarn.get_multiple('contracts', ids):
        project_party = contract.get_one('contract_parties', type='project', role='target')
        if not should_notify_user(project_party['permissions']):
            project_party['permissions'].append(
                {'permission_name': BOL_PERMISSION_NOTIFY_PROJECT_USER})
            project = get_project(storage, project_party['resource_id'])
            user_party = contract.get_one('contract_parties', type='contract', role='user')
            user_contract = qvarn.get('contracts', user_party['resource_id'])
            person_party = user_contract.get_one('contract_parties', type='person', role='user')
            person = get_person(qvarn, person_party['resource_id'])
            assert person is not None  # Migration script assumes person exists
            logger.info("Enabling notification for %s for %s project (contract %s)",
                        person['names'][0]['full_name'], project['names'][0], contract['id'])
            if not dry_run:
                qvarn.update('contracts', contract['id'], contract)


def main():
    parser = argparse.ArgumentParser(description="Enable notifications for all project users")
    parser.add_argument('-c', '--config', metavar='FILE', required=True,
                        help='app config file for Qvarn connection details')
    parser.add_argument('--dry-run', default=False, action='store_true',
                        help='do not write to the database, only log what will be done')
    args = parser.parse_args()

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-enable-notifications')
    qvarn = setup_qvarn_api(conf,
                            reason_getter=lambda: 'enabling notifications for all project users')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)

    migrate(storage, dry_run=args.dry_run)


if __name__ == '__main__':
    main()
