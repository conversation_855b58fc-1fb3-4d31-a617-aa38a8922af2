"""
Prepare a list of suppliers for bulk data import.
"""

import argparse
from collections import defaultdict

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.models import get_gov_org_id
from bolfak.services.projects import get_project_id
from bolfak.storage.company import get_orgs
from bolfak.storage.qvarn.projects import PROJECT_ID_TYPE, TAX_ID_TYPE, get_all_projects
from bolfak.storage.qvarn.suppliers import get_all_suppliers_w_selector
from bolfak.views.common import name_key


def main(args=None):
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument('-c', '--config', metavar='FILE', required=True,
                        help='app config file for Qvarn connection details')
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='also show project internal and tax IDs')
    args = parser.parse_args(args)

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-export-suppliers')

    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'listing suppliers for all projects')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        core = setup_core_sysapi_client(conf)
    else:
        core = None
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)

    project_suppliers = defaultdict(set)
    all_org_ids = set()
    for supplier in get_all_suppliers_w_selector(qvarn, ('supplier_org_id', 'project_resource_id')):
        project_suppliers[supplier['project_resource_id']].add(supplier['supplier_org_id'])
        all_org_ids.add(supplier['supplier_org_id'])

    project_instances = {
        project['id']: project
        for project in get_all_projects(qvarn)
    }

    org_instances = {
        org['id']: org
        for org in get_orgs(storage, sorted(all_org_ids))
    }

    project_fmt = (
        '{name}, internal ID: {internal_id}, tax ID: {tax_id}'
        if args.verbose else
        '{name}'
    )
    for project in sorted(project_instances.values(), key=lambda p: name_key(p['names'][0])):
        print(project_fmt.format(
            name=project['names'][0],
            internal_id=get_project_id(project, default='-', id_type=PROJECT_ID_TYPE),
            tax_id=get_project_id(project, default='-', id_type=TAX_ID_TYPE),
        ))
        orgs = [org_instances[org_id] for org_id in project_suppliers.get(project['id'], [])]
        supplier_org_ids: list[str] = [
            gov_org_id for org in orgs if (gov_org_id := get_gov_org_id(org)) is not None
        ]
        for supplier_org_id in sorted(supplier_org_ids):
            print("  {}".format(supplier_org_id))
        print()


if __name__ == '__main__':
    main()
