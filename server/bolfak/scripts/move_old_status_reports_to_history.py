"""
Default behavior: Move all status reports that are older than 3 days except for the
latest status report.

If start_date and end_date are provided, move all status reports that are older than
from start_date to end_date except for the latest status report.
"""

import argparse
import datetime
import logging

from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.storage.statusreports import move_old_status_reports_to_history


logger = logging.getLogger(__name__)


def main(argv=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)

    parser.add_argument(
        '-c', '--config', metavar='FILE',
        help='app config file (default: %(default)s)',
        default='/etc/bolfak/bolfak.cfg'
    )
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)

    parser.add_argument('--days-ago', metavar='N', type=int,
                        help='Cover reports that are created at the earliest N days ago.')

    parser.add_argument(
        '--start-date', metavar='YYYY-MM-DD',
        help='start date for the report',
    )

    parser.add_argument(
        '--end-date', metavar='YYYY-MM-DD',
        help="end date for the report",)
    args = parser.parse_args(argv)

    if args.days_ago and (args.start_date or args.end_date):
        parser.error('Cannot use --days-ago together with --start-date or --end-date')

    if args.start_date and not args.end_date or args.end_date and not args.start_date:
        parser.error('Cannot use --start-date without --end-date')

    if not args.days_ago and (not args.start_date or not args.end_date):
        parser.error('Must use either --days-ago or --start-date together with --end-date')

    if args.end_date and args.start_date:
        try:
            datetime.datetime.strptime(args.end_date, '%Y-%m-%d')
            datetime.datetime.strptime(args.start_date, '%Y-%m-%d')
        except ValueError:
            parser.error('Incorrect end date format, should be YYYY-MM-DD')

    if args.days_ago:
        start_date = (datetime.date.today() -
                      datetime.timedelta(days=int(args.days_ago))).isoformat()
        # end_date tomorrow to include today's reports
        end_date = (datetime.date.today() + datetime.timedelta(days=1)).isoformat()
    elif args.end_date and args.start_date:
        end_date = args.end_date
        start_date = args.start_date

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-move-old-status-reports-to-history')

    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'Removing status reports')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)

    logger.info('(START) Move old status reports to history'
                ' with start_date=%s and end_date=%s', start_date, end_date)

    moved_nr = move_old_status_reports_to_history(storage, start_date, end_date)

    logger.info('(DONE) Move old status reports to history:'
                ' moved %d status reports start_date=%s and end_date=%s',
                moved_nr, start_date, end_date)


if __name__ == '__main__':
    main()
