"""
Compute and send email notifications to subscribers.

There are two reporting modes:

  --daily    notify about companies that changed their status to WARNING since yesterday
  --monthly  notify about companies that changed their status to Investigate or
             Warning since last month

The notifications are emailed to project users who have selected to receive notifications.
"""

import argparse
import logging
import urllib.parse
from collections import namedtuple

from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.clients.sendgrid import setup_sendgrid_client
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.helpers.emails import get_email_template_for_locale
from bolfak.services.notifications import (
    companies_worse_since_last_month,
    companies_worse_since_last_week,
    companies_worse_since_yesterday,
)
from bolfak.services.reports import create_status_change_report
from bolfak.services.user_accounts import get_organisation_uuid
from bolfak.storage.qvarn.projects import PROJECT_STATES_NOT_CLOSED


logger = logging.getLogger(__name__)


Notification = namedtuple('Notification',
                          'recipient_email, scr_id, template, parameters')


def get_template_name(template, period, locale):
    return get_email_template_for_locale(f"{template}_{period}", locale)


def get_notifications(storage, period, base_url, dry_run=False, org_ids=None):
    qvarn = storage.qvarn
    if feature_active('core_mitt_id06'):
        base_url = urllib.parse.urljoin(base_url, 'bol')
    else:
        base_url = urllib.parse.urljoin(base_url, '/')
    logger.info('Computing status %s change report', period)

    if period == 'daily':
        report = companies_worse_since_yesterday(
            storage, org_ids=org_ids, project_state=PROJECT_STATES_NOT_CLOSED)
    elif period == 'weekly':
        report = companies_worse_since_last_week(
            storage, org_ids=org_ids, project_state=PROJECT_STATES_NOT_CLOSED)
    elif period == 'monthly':
        report = companies_worse_since_last_month(
            storage, org_ids=org_ids, project_state=PROJECT_STATES_NOT_CLOSED)
    else:
        raise ValueError('period should be daily/weekly/monthly, not %r' % period)

    scr_id = '<placeholder>'
    if not dry_run and report['users_to_notify']:
        scr_id = create_status_change_report(qvarn, report)['id']

    logger.info('Done computing status %s change report', period)

    for user in report['users_to_notify'].values():
        if feature_active('core_mitt_id06'):
            org_id = get_organisation_uuid(storage, user['user_org_id'])
        else:
            org_id = user['user_org_id']
        url = urllib.parse.urljoin(base_url, '#/companies/scr/{scr_id}/{user_org_id}'.format(
            scr_id=scr_id, user_org_id=org_id))
        yield Notification(
            recipient_email=user['user_info']['email'],
            scr_id=scr_id,
            template=get_template_name(
                'bol_notify_user_companies_status',
                period,
                user['user_info']['locale'],
            ),
            parameters=dict(
                firstName=user['user_info']['name'],
                companiesUri=url,
            )
        )


def send_notifications(sendgrid_client, notifications, dry_run):
    sent = total = 0
    for notification in notifications:
        logger.info('Sending email to %s with a link to status change report %s%s',
                    notification.recipient_email, notification.scr_id,
                    ' (dry run)' if dry_run else '')
        total += 1
        if not dry_run:
            try:
                sendgrid_client.send_email(
                    receiver=notification.recipient_email,
                    template=notification.template, parameters=notification.parameters)
            except Exception as e:
                logger.exception('Failed to send email: %s', e)
                continue
            sent += 1
        logger.info('Sent email to %s with a link to status change report %s%s',
                    notification.recipient_email, notification.scr_id,
                    ' (dry run)' if dry_run else '')
    logger.info('Done sending emails with links to status change reports: '
                'sent %d emails out of %d%s', sent, total,
                ' (dry run)' if dry_run else '')


def main():
    parser = argparse.ArgumentParser(description=__doc__,
                                     formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)
    parser.add_argument('-c', '--config', metavar='FILE', required=True,
                        help='app config file for Qvarn and Sendgrid connection details')
    parser.add_argument('--dry-run', action='store_true',
                        help='do not send any emails, just show what notifications would be sent')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--daily', action='store_const', dest='period', const='daily',
                       help='send daily notifications')
    group.add_argument('--weekly', action='store_const', dest='period', const='weekly',
                       help='send weekly notifications')
    group.add_argument('--monthly', action='store_const', dest='period', const='monthly',
                       help='send monthly notifications')
    args = parser.parse_args()

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-notify')
    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'sending notifications')
    bda = setup_bda_client(conf)

    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    if feature_active('user_account_api'):
        logger.info('Connecting to user-account-api...')
        user_account_api = setup_user_account_api_client(conf)
    else:
        user_account_api = None

    storage = setup_storage(qvarn=qvarn, bda=bda, core=core, user_account_api=user_account_api)
    sendgrid_client = setup_sendgrid_client(conf)

    logger.info('Start sending %s email notifications%s', args.period, ' (dry run)'
                if args.dry_run
                else '')

    base_url = conf.get('main', 'frontend_url')
    notifications = get_notifications(storage, args.period, base_url, args.dry_run)
    send_notifications(sendgrid_client, notifications, args.dry_run)

    logger.info('Done sending %s email notifications%s', args.period, ' (dry run)'
                if args.dry_run
                else '')


if __name__ == '__main__':
    main()
