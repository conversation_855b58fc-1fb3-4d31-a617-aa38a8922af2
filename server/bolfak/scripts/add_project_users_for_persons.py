"""
Add project users based on contact person email addresses.
"""

import argparse
import logging

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.project_users import (
    add_candidates_as_project_users,
    get_project_user_candidates,
    get_project_user_candidates_for_client,
)
from bolfak.storage.projects import get_projects
from bolfak.storage.qvarn.projects import PROJECT_STATES_NOT_CLOSED
from bolfak.storage.qvarn.suppliers import get_project_suppliers


logger = logging.getLogger(__name__)


def main(args=None):
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument('-c', '--config', metavar='FILE', required=True,
                        help='app config file for Qvarn connection details')
    args = parser.parse_args(args)

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-add-project-users-for-persons')

    qvarn = setup_qvarn_api(
        conf,
        reason_getter=lambda: 'adding project users for supplier contact mails')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    if feature_active('user_account_api'):
        logger.info('Connecting to user-account-api...')
        user_account_api = setup_user_account_api_client(conf)
    else:
        user_account_api = None

    storage = setup_storage(qvarn=qvarn, bda=bda, core=core, user_account_api=user_account_api)
    logger.info('Adding client and supplier contact persons as project users starting')

    project_instances = {
        project['id']: project
        for project in get_projects(storage, states=PROJECT_STATES_NOT_CLOSED)
    }
    logger.info('Adding client and supplier contact persons will process %s projects',
                len(project_instances))

    for project_id, project in project_instances.items():
        candidates = get_project_user_candidates_for_client(project)

        # Getting potential project users among supplier contacts.
        pa_form_enabled = project.get('pa_form_enabled')
        if pa_form_enabled:
            project_suppliers = get_project_suppliers(storage, project_id)
            candidates.extend(
                get_project_user_candidates(project_suppliers, project_id, pa_form_enabled)
            )

        add_candidates_as_project_users(storage, project_id, candidates)

    logger.info('Adding client and supplier contact persons as project users done')


if __name__ == '__main__':
    main()
