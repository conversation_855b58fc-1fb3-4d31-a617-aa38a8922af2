"""
    Supplier contact person_id is updated if new person_id and user account is found
    And the project user is deleted if no user account is found for the project user
"""

import argparse
import logging

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.exceptions import DataNotFoundError
from bolfak.services.user_accounts import get_user_accounts_by_email
from bolfak.storage.projects import get_projects, update_project
from bolfak.storage.qvarn.persons import get_user_account_person_id
from bolfak.storage.qvarn.projects import PROJECT_STATES_NOT_CLOSED
from bolfak.storage.qvarn.suppliers import get_project_suppliers
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.featureflags import feature_active
from bolfak.services.user_accounts import (
    get_user_account_by_id,
    get_user_account_for_person_and_org_including_missing
)
from bolfak.storage.project_users import _delete_project_user, get_project_users_for_project
from bolfak.storage.qvarn.suppliers import update_supplier


logger = logging.getLogger(__name__)


def main(args=None):
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument('-c', '--config', metavar='FILE', required=True,
                        help='app config files')
    args = parser.parse_args(args)

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-supplier_contacts_project_users_fixup')

    qvarn = setup_qvarn_api(
        conf,
        reason_getter=lambda: 'fixing mapping of supplier contacts and project users')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    if feature_active('user_account_api'):
        logger.info('Connecting to user-account-api...')
        user_account_api = setup_user_account_api_client(conf)
    else:
        user_account_api = None

    storage = setup_storage(qvarn=qvarn, bda=bda, core=core, user_account_api=user_account_api)
    logger.info('Supplier contacts and project users fixup starting')

    project_instances = {
        project['id']: project
        for project in get_projects(storage,
                                    states=PROJECT_STATES_NOT_CLOSED)
    }
    logger.info('Supplier contacts and project users fixup will process %s projects',
                len(project_instances))

    nr_updated_supplier_contacts = 0
    nr_updated_client_contacts = 0
    nr_deleted_users = 0

    for i, project in enumerate(project_instances.values()):
        project_id = project.get('id')
        client_contact_person_id = project.get('client_contact_person_id')
        client_contact_person_email = project.get('client_contact_person_email')
        project_responsible_org = project.get('project_responsible_org')

        if i % 100 == 0:
            logger.info(f'processing project {i+1}/{len(project_instances)}')
        if i == len(project_instances) - 1:
            logger.info(f'processing last project {i+1}/{len(project_instances)}')
        nr_updated_supplier_contacts += update_project_supplier_contacts(storage, project_id)

        # Check if we need to update the client contact person_id
        if client_contact_person_email:
            try:
                user_accounts = get_user_accounts_by_email(
                    storage,
                    email=client_contact_person_email,
                    org_id=project_responsible_org)
            except DataNotFoundError:
                user_accounts = None
            if not user_accounts:
                logger.error(f"No user account found for client contact person email: "
                             f"{client_contact_person_email} and org: "
                             f"{project_responsible_org}")
                project['client_contact_person_id'] = None
                update_project(storage, project_id, project)
                nr_updated_client_contacts += 1
            elif len(user_accounts) > 1:
                logger.error(f"Duplicate user accounts found for project "
                             f"user by email: {client_contact_person_email} and org: "
                             f"{project_responsible_org}")
            else:
                person_id_from_user_account = get_user_account_person_id(user_accounts[0])
                if person_id_from_user_account != client_contact_person_id:
                    logger.info(f'Updating project {project_id} with new client contact '
                                f'person_id: {person_id_from_user_account} '
                                f'for client contact_email: {client_contact_person_email}')

                    project['client_contact_person_id'] = person_id_from_user_account
                    update_project(storage, project_id, project)
                    nr_updated_client_contacts += 1

        # Delete project users that we don't find a user account contract for
        project_users = get_project_users_for_project(storage, project_id)

        for project_user in project_users:
            delete_project_user = False
            try:
                if feature_active('person_id_for_project_users'):
                    person_id = project_user["person_id"]
                    if (person_id is None
                        or person_id == "null"
                        or get_user_account_for_person_and_org_including_missing(
                            storage, person_id, project_user["represented_company_id"]) is None):
                        delete_project_user = True
                else:
                    user_account_id = project_user["user_account_id"]
                    if (user_account_id is None
                            or user_account_id == "null"
                            or get_user_account_by_id(storage, user_account_id) is None):
                        delete_project_user = True

                if delete_project_user:
                    _delete_project_user(storage, project_user["id"])
                    logger.info(
                        f'Deleted project user {project_user["id"]} for project {project_id}'
                    )
                    nr_deleted_users += 1

            except Exception as e:
                logger.error(
                    'Unexpected error while processing project user %s: %s',
                    project_user.get('id', 'unknown'),
                    e,
                )

    logger.info(
        f'Supplier contacts and project users fixup finished, '
        f'updated {nr_updated_supplier_contacts} supplier contacts, '
        f'updated {nr_updated_client_contacts} client contacts and '
        f'deleted {nr_deleted_users} project users'
    )


def update_project_supplier_contacts(storage, project):
    """
        Update supplier contact person_id if new person_id and user account is found
        for the supplier contact email and supplier org.

        Returns number of updated supplier contacts.
    """
    project_suppliers = get_project_suppliers(storage, project)
    nr_updated_supplier_contacts = 0

    for supplier in project_suppliers:
        supplier_contacts = supplier['supplier_contacts']

        updated = False
        for supplier_contact in supplier_contacts:

            supplier_contact_person_id = supplier_contact.get(
                'supplier_contact_person_id', None)

            supplier_contact_email = supplier_contact.get('supplier_contact_email', None)
            org_id = supplier['supplier_org_id']
            if supplier_contact_email:
                try:
                    user_accounts = get_user_accounts_by_email(
                        storage, email=str(supplier_contact_email), org_id=org_id)
                except DataNotFoundError as e:
                    logger.error('%s - Skipping!', e)
                    continue
                if not user_accounts:
                    logger.error(f"No user account found for supplier contact by email: "
                                 f"{supplier_contact_email} and org: {org_id}")

                    if supplier_contact_person_id:
                        logger.info(f"Clearing out the person_id {supplier_contact_person_id}")
                        updated = True
                        supplier_contact['supplier_contact_person_id'] = None
                elif len(user_accounts) > 1:
                    logger.error(f"Duplicate user accounts found for supplier contact by email: "
                                 f"{supplier_contact_email} and org: {org_id}")
                else:
                    user_account = user_accounts[0]
                    person_id = get_user_account_person_id(user_account)
                    if user_account and person_id != supplier_contact_person_id:
                        # Found a new user account for the person,
                        # thus update the supplier contact person_id
                        supplier_contact['supplier_contact_person_id'] = person_id
                        updated = True
                        logger.info(
                            f'Will update supplier contact person_id for supplier {supplier["id"]}'
                            f'from person_id {supplier_contact_person_id} to {person_id}')
                        nr_updated_supplier_contacts += 1

        if updated:
            update_supplier(storage, supplier['id'], {
                            'supplier_contacts': supplier_contacts})

    return nr_updated_supplier_contacts


if __name__ == '__main__':
    main()
