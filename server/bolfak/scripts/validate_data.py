#!/usr/bin/env python3
"""
Validate the consistency of Qvarn data.
"""

import argparse
import logging
from typing import List

import sys

from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import (
    get_organisation_contract_type,
    get_person_organisation_contract_type,
    set_config,
)
from bolfak.featureflags import feature_active
from bolfak.services.resources import R
from bolfak.storage.company import get_orgs, search_orgs
from bolfak.storage.qvarn.contracts import BOLFAK_USER_CONTRACT_TYPE
from bolfak.storage.qvarn.persons import get_persons_all
from bolfak.storage.qvarn.projects import get_multiple_projects, get_project_ids
from bolfak.storage.qvarn.suppliers import get_all_suppliers_ids, get_multiple_suppliers
from bolfak.utils import lazy


logger = logging.getLogger(__name__)


class Validator(object):

    scopes_needed_to_check = [
        'uapi_orgs_get',
        'uapi_orgs_id_get',
        'uapi_persons_get',
        'uapi_persons_id_get',
        'uapi_projects_get',
        'uapi_projects_id_get',
        'uapi_bol_suppliers_get',
        'uapi_bol_suppliers_id_get',
        'uapi_contracts_get',
        'uapi_contracts_id_get',
        'uapi_contracts_search_id_get',
    ]
    scopes_needed_to_delete = [
        'uapi_orgs_id_delete',
        'uapi_persons_id_delete',
        'uapi_projects_id_delete',
        'uapi_bol_suppliers_id_delete',
        'uapi_contracts_id_delete',
    ]

    def __init__(self, storage):
        self.storage = storage
        self.qvarn = storage.qvarn
        self.bad_resources = {}
        self.interesting_contract_types = [
            get_person_organisation_contract_type(),
            get_organisation_contract_type(),
            BOLFAK_USER_CONTRACT_TYPE,
        ]

    @lazy
    def org_ids(self) -> List[str]:
        logger.info('Listing orgs...')
        return search_orgs(self.storage)

    @lazy
    def orgs(self):
        logger.info('Loading %d orgs...', len(self.org_ids))
        return get_orgs(self.storage, self.org_ids)

    @lazy
    def person_ids(self):
        logger.info('Listing persons...')
        return get_persons_all(self.qvarn)

    @lazy
    def project_ids(self):
        logger.info('Listing projects...')
        return get_project_ids(self.storage)

    @lazy
    def projects(self):
        logger.info('Loading %d projects...', len(self.project_ids))
        return get_multiple_projects(self.storage, self.project_ids)

    @lazy
    def project_by_id(self):
        return {p['id']: p for p in self.projects}

    @lazy
    def supplier_ids(self):
        return get_all_suppliers_ids(self.storage)

    @lazy
    def suppliers(self):
        logger.info('Loading %d suppliers...', len(self.supplier_ids))
        return get_multiple_suppliers(self.storage, self.supplier_ids)

    @lazy
    def contract_ids(self):
        logger.info('Listing contracts...')
        return self.qvarn.get_list('contracts')

    @lazy
    def contracts(self):
        # you do not want this, it takes an hour on beta
        raise NotImplementedError("you do not want to do this, it takes *ages*")
        logger.info('Loading %d contracts...', len(self.contract_ids))
        return self.qvarn.get_multiple('contracts', self.contract_ids)

    @lazy
    def interesting_contract_ids(self):
        logger.info('Searching for interesting contracts...')
        return self.qvarn.search('contracts', contract_type__any=self.interesting_contract_types)

    @lazy
    def interesting_contracts(self):
        logger.info('Loading %d contracts...', len(self.interesting_contract_ids))
        return self.qvarn.get_multiple('contracts', self.interesting_contract_ids)

    def project_name(self, project):
        if isinstance(project, str):
            project = self.project_by_id[project]
        try:
            return project['names'][0]
        except Exception as e:
            return repr(e)

    def mark_bad(self, rtype, rid):
        self.bad_resources.setdefault(rtype, set()).add(rid)

    def validate_projects(self):
        org_ids = set(self.org_ids)
        logger.info('Validating %d projects...', len(org_ids))
        for project in self.projects:
            # we might check that project['names'] is not empty
            # we might check that state is one of the valid values
            # we might check that start_date/end_date are ISO-8601
            # we might check that start_date <= end_date
            if (project['project_responsible_org']
                    and project['project_responsible_org'] not in org_ids):
                self.mark_bad('projects', project['id'])
                logger.warning(
                    'Project %s (%s) has project_responsible_org %s that does not exist',
                    project['id'], self.project_name(project), project['project_responsible_org'])
        logger.info('Validated %d projects; %d were bad', len(self.projects),
                    len(self.bad_resources.get('projects', ())))

    def validate_suppliers(self):
        org_ids = set(self.org_ids)
        supplier_ids = set(self.supplier_ids)
        project_ids = set(self.project_ids)
        person_ids = set(self.person_ids)
        suppliers = self.suppliers
        logger.info('Validating %d suppliers...', len(suppliers))
        for supplier in suppliers:
            # Things we could also check:
            # - contract_start_date/contract_end_date are valid ISO-8601 dates
            # - contract_start_date is before contract_end_date
            # - materialized_path[:-1] matches the materialized_path of the parent supplier
            # - parent_org_id matches the supplier_org_id of the parent supplier
            # - supplier_type is one of the valid values
            # - supplier_role is one of the valid values
            # - I'm sure we have constraints about supplier_type matching the parent supplier
            # - I'm sure we have constraints about supplier_role and depth in the supplier tree
            if supplier['project_resource_id'] not in project_ids:
                self.mark_bad(R.SUPPLIERS, supplier['id'])
                logger.warning(
                    'Supplier %s has project_resource_id %s that does not exist',
                    supplier['id'], supplier['project_resource_id'])
                # no point checking further, trying to log the project name will cause failures
                continue
            if supplier['supplier_org_id'] not in org_ids:
                self.mark_bad(R.SUPPLIERS, supplier['id'])
                logger.warning(
                    'Supplier %s for project %s (%s) has supplier_org_id %s that does not exist',
                    supplier['id'], supplier['project_resource_id'],
                    self.project_name(supplier['project_resource_id']), supplier['supplier_org_id'])
            if (supplier['parent_supplier_id']
                    and supplier['parent_supplier_id'] not in supplier_ids):
                self.mark_bad(R.SUPPLIERS, supplier['id'])
                logger.warning(
                    'Supplier %s for project %s (%s) has parent_supplier_id %s that does not exist',
                    supplier['id'], supplier['project_resource_id'],
                    self.project_name(supplier['project_resource_id']),
                    supplier['parent_supplier_id'])
            for pid in supplier['materialized_path']:
                if pid not in org_ids:
                    self.mark_bad(R.SUPPLIERS, supplier['id'])
                    logger.warning(
                        'Supplier %s for project %s (%s) has materialized_path element %s'
                        ' that does not exist',
                        supplier['id'], supplier['project_resource_id'],
                        self.project_name(supplier['project_resource_id']), pid)
            if (supplier['parent_org_id'] and supplier['parent_org_id'] not in org_ids):
                self.mark_bad(R.SUPPLIERS, supplier['id'])
                logger.warning(
                    'Supplier %s for project %s (%s) has parent_org_id %s that does not exist',
                    supplier['id'], supplier['project_resource_id'],
                    self.project_name(supplier['project_resource_id']),
                    supplier['parent_org_id'])
            for contact in supplier['supplier_contacts']:
                if (contact['supplier_contact_person_id'] and
                        contact['supplier_contact_person_id'] not in person_ids):
                    self.mark_bad(R.SUPPLIERS, supplier['id'])
                    logger.warning(
                        'Supplier %s for project %s (%s) has supplier_contact_person_id %s'
                        ' that does not exist',
                        supplier['id'], supplier['project_resource_id'],
                        self.project_name(supplier['project_resource_id']),
                        contact['supplier_contact_person_id'])

        logger.info('Validated %d suppliers; %d were bad', len(self.suppliers),
                    len(self.bad_resources.get(R.SUPPLIERS, ())))

    def validate_contracts(self):
        ids = {
            'contract': set(self.contract_ids),
            'org': set(self.org_ids),
            'person': set(self.person_ids),
            'project': set(self.project_ids),
        }
        other = set()
        contracts = self.interesting_contracts
        logger.info('Validating %d contracts...', len(contracts))
        for contract in contracts:
            for party in contract['contract_parties']:
                if not party['type']:
                    # These exist!  I've no idea why
                    continue
                if party['type'] not in ids:
                    other.add(party['type'])
                    continue
                if party['resource_id'] not in ids[party['type']]:
                    self.mark_bad('contracts', contract['id'])
                    logger.warning(
                        'Contract %s of type %s refers to a %s %s that does not exist',
                        contract['id'], contract['contract_type'],
                        party['type'], party['resource_id'])

        logger.info('Validated %d interesting contracts (out of %d total contracts); %d were bad',
                    len(self.interesting_contract_ids), len(self.contract_ids),
                    len(self.bad_resources.get('contracts', ())))
        if other:
            logger.info('Did not verify these contract party types: %s', ', '.join(sorted(other)))

    # we might also validate reports: do they point to existing orgs?
    # we might also validate orgs: do they have unique gov_org_ids etc?
    # we might also validate persons: do they have names?


class MyFormatter(logging.Formatter):
    def format(self, record):
        msg = super().format(record)
        # Tweak the formatting of warning messages printed to the console
        if record.name == __name__ and record.levelno == logging.WARNING:
            msg = '- ' + msg[:1].lower() + msg[1:]
        return msg


def adjust_logging_config():
    # bolfak.cfg in our Kubernetes pods sets up JSON logging to stdout at DEBUG level, which is
    # painful to read for people who run this script manually via kubectl exec
    root = logging.getLogger()
    del root.handlers[:]
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(MyFormatter())
    root.addHandler(handler)

    # suppress DEBUG messages
    root.setLevel(logging.INFO)
    # urllib3 logs too much at level INFO
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    # requests vendors its own copy of urllib3
    logging.getLogger('requests').setLevel(logging.WARNING)


def main():
    parser = argparse.ArgumentParser(description=__doc__,
                                     formatter_class=argparse.RawDescriptionHelpFormatter)
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)
    parser.add_argument('-c', '--config', metavar='FILE', default='/etc/bolfak/bolfak.cfg',
                        help='app config file for Qvarn connection details')
    parser.add_argument('--delete-bad', action='store_true', default=False,
                        help='delete bad resources (USE WITH CARE)')
    args = parser.parse_args()

    scopes = set(Validator.scopes_needed_to_check)
    if args.delete_bad:
        scopes.update(Validator.scopes_needed_to_delete)

    conf = set_config(args.config)
    adjust_logging_config()
    # override the scopes set in the config file, since they don't
    # necessarily contain everything that this script needs (no
    # uapi_contracts_get in docker/bolfak.cfg)
    conf.set('qvarn', 'scope', ' '.join(sorted(scopes)))
    setup_azure_ai(conf, 'bol-validate-data')
    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'checking data consistency')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)

    validator = Validator(storage)
    validator.validate_projects()
    validator.validate_suppliers()
    validator.validate_contracts()

    if args.delete_bad:
        for rtype, ids in validator.bad_resources.items():
            if ids:
                logger.info('Deleting bad %s: %s', rtype, ' '.join(sorted(ids)))
                qvarn.delete_multiple(rtype, ids)


if __name__ == '__main__':
    main()
