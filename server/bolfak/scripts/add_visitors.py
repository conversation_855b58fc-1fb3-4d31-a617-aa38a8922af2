import argparse
import datetime
import logging
from datetime import timedelta

from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.clients.stamp import setup_stamp_client
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.stamp import process_stamps


logger = logging.getLogger(__name__)


def main(argv=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)
    parser.add_argument(
        '--date',
        type=datetime.datetime.fromisoformat,
        metavar='Date of visit',
        help='Add visitors by visiting date.')
    parser.add_argument('--days-ago', metavar='N', type=str,
                        help='multiple comma-separated values allowed, e.g. --days-ago 1,2,3')
    parser.add_argument('-c', '--config', metavar='FILE',
                        help='app config file (default: %(default)s)',
                        default='/etc/bolfak/bolfak.cfg')

    args = parser.parse_args(argv)

    if args.days_ago and args.date:
        parser.error('Cannot use --date together with --days-ago')

    if args.days_ago is None and args.date is None:
        parser.error('Must use either --days-ago or --date')

    visit_dates = []
    if args.days_ago is not None:
        for day in args.days_ago.split(","):
            visit_dates.append(datetime.datetime.utcnow() - timedelta(days=int(day)))
    elif args.date:
        visit_dates.append(args.date)

    conf = set_config(args.config)

    setup_azure_ai(conf, 'bol-add-visitors')

    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'Adding visitors')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)
    stamp_client = setup_stamp_client(conf)

    for visit_date in visit_dates:
        logger.info(f'Visitors add starting to add sites by date {visit_date}')
        process_stamps(storage, stamp_client, visit_date)
        logger.info('Visitors add successfully finished adding visitors')


if __name__ == '__main__':
    main()
