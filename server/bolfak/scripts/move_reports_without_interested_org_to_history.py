"""
Move all reports for suppliers which are no longer monitored by the interested_org.
"""

import argparse
import datetime
import logging

from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.statusreports import get_interested_for_all_orgs
from bolfak.storage.statusreports import (
    get_status_reports_intrstd_cmpny_id_to_cmpny_id,
    move_reports_without_intrstd_org_to_history,
)


logger = logging.getLogger(__name__)


def main(argv=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)

    parser.add_argument(
        '-c', '--config', metavar='FILE',
        help='app config file (default: %(default)s)',
        default='/etc/bolfak/bolfak.cfg'
    )
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)

    parser.add_argument('--days-ago', metavar='N', type=int,
                        help='Cover reports that are created at the earliest N days ago.')

    parser.add_argument(
        '--start-date', metavar='YYYY-MM-DD',
        help='start date for the report',
    )

    parser.add_argument(
        '--end-date', metavar='YYYY-MM-DD',
        help="end date for the report",)
    args = parser.parse_args(argv)

    if args.days_ago and (args.start_date or args.end_date):
        parser.error('Cannot use --days-ago together with --start-date or --end-date')

    if args.start_date and not args.end_date or args.end_date and not args.start_date:
        parser.error('Cannot use --start-date without --end-date')

    if not args.days_ago and (not args.start_date or not args.end_date):
        parser.error('Must use either --days-ago or --start-date together with --end-date')

    if args.end_date and args.start_date:
        try:
            datetime.datetime.strptime(args.end_date, '%Y-%m-%d')
            datetime.datetime.strptime(args.start_date, '%Y-%m-%d')
        except ValueError:
            parser.error('Incorrect end date format, should be YYYY-MM-DD')

    if args.days_ago:
        start_date = (datetime.date.today() -
                      datetime.timedelta(days=int(args.days_ago))).isoformat()
        # end_date tomorrow to include today's reports
        end_date = (datetime.date.today() + datetime.timedelta(days=1)).isoformat()
    elif args.end_date and args.start_date:
        end_date = args.end_date
        start_date = args.start_date

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-move-reports-without-interested-org-to-history')

    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'Removing status reports')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core)

    logger.info('(START) Move reports without interested org to history'
                ' for status reports with start_date: %s and end_date %s', start_date, end_date)

    res = get_status_reports_intrstd_cmpny_id_to_cmpny_id(storage, start_date, end_date)

    # Set with unique identifiers constructed by tuples (interested_company_id, company_id)
    # e.g.: ('f392-20ecc5948857-1a0e6590', 'f392-31453de1bae1-6b60c5c1')
    # This set thus contain the interested_company_id to company_id pair
    # that has a status report in BDA.
    pairs_with_status_reports = set()

    for pair in res:
        pairs_with_status_reports.add((pair['interested_company_id'], pair['company_id']))

    interested_indiscriminate = get_interested_for_all_orgs(storage)

    # A Set with identifiers constructed by tuples(interested_company_id, company_id)
    # This set is constructed of the current project trees state.
    pairs_in_curr_projects_state = set()

    for interested_company_id, company_ids in interested_indiscriminate.items():
        for id in company_ids:
            pairs_in_curr_projects_state.add((interested_company_id, id))

    # Set difference should give us the status reports that should be moved,
    # i.e. the reports of the suppliers which are no longer monitored by the interested_org
    to_be_moved = pairs_with_status_reports - pairs_in_curr_projects_state

    # Restructure data to fit into the API call.
    intrstd_cmpny_ids_to_cmpny_ids = [
        {
            'interested_company_id': interested_company_id,
            'company_id': company_id
        }
        for interested_company_id, company_id in to_be_moved
    ]
    nr_moved = move_reports_without_intrstd_org_to_history(storage, intrstd_cmpny_ids_to_cmpny_ids)

    logger.info('(DONE) Move reports without interested org to history:'
                ' moved %s selected status reports', nr_moved)


if __name__ == '__main__':
    main()
