import argparse
import logging
from typing import List, Optional

import bolfak.celery  # noqa, just to check if environment vars are missing
from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.contract import setup_contract_client
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.statusreports import (
    filter_interested_with_active_bda_cs_account,
    filter_interested_with_active_cs_account,
    filter_interested_with_not_monitored_orgs,
    get_interested_for_all_orgs,
    queue_add_orgs_to_monitored_list,
)


logger = logging.getLogger(__name__)


def main(argv: Optional[List[str]] = None) -> None:
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)

    parser.add_argument('-c', '--config', metavar='FILE',
                        help='app config file (default: %(default)s)',
                        default='/etc/bolfak/monitoring.cfg')
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)

    args = parser.parse_args(argv)

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-add-to-monitoring')

    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'Adding suppliers to CS monitoring')
    bda = setup_bda_client(conf)

    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    contract_client = setup_contract_client(conf)
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core, contract_api=contract_client)

    logger.info('Add to monitoring is starting...')
    interested = get_interested_for_all_orgs(storage)
    interested = filter_interested_with_active_cs_account(storage, interested,
                                                          job_id='add_to_monitoring')
    interested = filter_interested_with_active_bda_cs_account(storage, interested)
    interested = filter_interested_with_not_monitored_orgs(storage, conf, interested)

    # Log some statistics
    flat_orgs = set()
    for interested_org_id, org_ids in interested.items():
        flat_orgs.update(org_ids)
    logger.info('Add to monitoring will add %d unique companies to monitoring' % len(flat_orgs))

    for interested_org_id, org_ids in interested.items():
        queue_add_orgs_to_monitored_list(qvarn, org_ids, interested_org_id)
    logger.info('Add to monitoring done adding unmonitored companies to monitoring')


if __name__ == '__main__':
    main()
