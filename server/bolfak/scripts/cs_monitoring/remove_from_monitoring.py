import argparse
import logging
from typing import List, Optional

import bolfak.celery  # noqa, just to check if environment vars are missing
from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.contract import setup_contract_client
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.statusreports import (
    filter_interested_with_active_bda_cs_account,
    filter_interested_with_active_cs_account,
    find_monitored_orgs_removed_from_project_tree,
    get_interested_for_all_orgs,
    queue_remove_orgs_from_monitored_list,
)


logger = logging.getLogger(__name__)


def main(argv: Optional[List[str]] = None) -> None:
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)

    parser.add_argument('-c', '--config', metavar='FILE',
                        help='app config file (default: %(default)s)',
                        default='/etc/bolfak/monitoring.cfg')
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)

    args = parser.parse_args(argv)

    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-remove-from-monitoring')

    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'Removing suppliers from CS monitoring')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    contract_client = setup_contract_client(conf)
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core, contract_api=contract_client)

    logger.info('Remove from monitoring is starting...')

    interested_correct = get_interested_for_all_orgs(storage)
    interested_indiscriminate = get_interested_for_all_orgs(
        storage, indiscrimiate_projects=True, indiscriminate_suppliers=True)

    interested_correct_with_cs = filter_interested_with_active_cs_account(
        storage, interested_correct, job_id='remove_from_monitoring')
    interested_indiscriminate_with_cs = filter_interested_with_active_cs_account(
        storage, interested_indiscriminate, job_id='remove_from_monitoring')

    interested_correct_with_cs = filter_interested_with_active_bda_cs_account(
        storage, interested_correct_with_cs)
    interested_indiscriminate_with_cs = filter_interested_with_active_bda_cs_account(
        storage, interested_indiscriminate_with_cs)

    interested_to_remove = find_monitored_orgs_removed_from_project_tree(
        storage, conf, interested_correct_with_cs, interested_indiscriminate_with_cs)

    for interested_org_id, org_ids in interested_to_remove.items():
        queue_remove_orgs_from_monitored_list(qvarn, org_ids, interested_org_id)
    logger.info('Remove from monitoring done removing companies from monitoring')


if __name__ == '__main__':
    main()
