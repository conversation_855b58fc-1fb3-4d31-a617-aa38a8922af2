"""
Queue a job to fetch company status from data providers.

You should run this script from cron once a month (or at whichever
periodicity you prefer).

If you wish to update the status of some companies without waiting
a month you can run this script manually and specify one or more
company registration numbers in a file, one number per line.  Here's
an example ORGIDS_FILE::

    556012-9206
    556104-3539
    556341-0421

This script needs a configuration file with Qvarn credentials, e.g. ::

    [qvarn]
    base_url = https://qvarn.example.com
    client_id = @!XXXX.XXXX.XXXX.XXXX!XXXX!XXXX.XXXX!XXXX!XXXX.XXXX
    client_secret = XXXXXXXXXXXXXXXX
    threads = 10
    scope =
        uapi_orgs_get,
        uapi_orgs_search_id_get,
        uapi_bol_suppliers_get,
        uapi_bol_suppliers_search_id_get,
        uapi_jobs_get,
        uapi_jobs_id_delete,
        uapi_jobs_id_get,
        uapi_jobs_id_put,
        uapi_jobs_post,
        uapi_jobs_search_id_get,

You should also be running the celery worker so these jobs will actually be
processed.
"""

import argparse
import logging
from typing import List, Set

from bolfak import __version__
from bolfak.azure_ai import setup_azure_ai
from bolfak.celery import tasks
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.contract import setup_contract_client
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.config import set_config
from bolfak.featureflags import feature_active
from bolfak.services.companies import get_org_id
from bolfak.services.statusreports import (
    filter_interested_with_active_cs_account,
    filter_interested_with_outdated_reports,
    get_interested_for_all_orgs,
)


logger = logging.getLogger(__name__)


UPDATE_REPORTS_BATCH_SIZE = 10
UPDATE_EMPTAX_BATCH_SIZE = 1


def parse_gov_ids(storage, gov_ids) -> list:
    gov_org_ids = gov_ids.split()
    org_ids = []
    for gov_org_id in gov_org_ids:
        org_id = get_org_id(storage, gov_org_id)
        if org_id:
            org_ids.append(org_id)
        elif gov_org_id:
            logger.error("can't find %r gov org id in database", gov_org_id)
    return org_ids


def filter_interested_with_outdated_reports_swe(storage, older_than_days):
    # Include visitors for whitelisted interested_orgs when updating Swedish reports
    interested = get_interested_for_all_orgs(storage, indiscriminate_suppliers=True)
    interested = filter_interested_with_active_cs_account(storage, interested)
    interested = filter_interested_with_outdated_reports(storage,
                                                         interested,
                                                         older_than_days=older_than_days)
    return interested


def parse_gov_ids_from_file(storage, filename) -> list:
    org_ids = []
    with open(filename) as f:
        for line in f:
            gov_org_id = line.strip()
            if not gov_org_id:
                continue

            org_id = get_org_id(storage, gov_org_id)
            if org_id:
                org_ids.append(org_id)
            elif gov_org_id:
                logger.error("can't find %r gov org id in database", gov_org_id)
    return org_ids


def main(argv=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description=__doc__)
    parser.add_argument('gov_ids_f', metavar='GOV_IDS_FILE', nargs='?',
                        help='file with gov org ids')
    parser.add_argument('--interested-org-id', metavar='QVARN ID', default='',
                        help='company id of interested company'),
    parser.add_argument('--gov-ids', metavar='GOV_IDS', default='',
                        help='space separated string of company gov ids'),
    parser.add_argument('--all', action='store_true',
                        help='DEPRECATED: Update all companies with outdated reports'
                             ' (default if ORGIDS_FILE not provided)')
    # When the --changed is absent, default is used, when --changed is given
    # w/o value, then const is used, otherwise given value is used.
    parser.add_argument('--changed', metavar='N', nargs='?', type=int, const=1,
                        default=False, choices=range(1, 201),
                        help='Update swedish companies that have changed'
                        ' in [1-200] day(s) in the past. Default is 1 day.')
    parser.add_argument('--outdated', metavar='N', nargs='?', type=int, const=30,
                        default=False,
                        help='Update foreign companies that have outdated reports -'
                        ' reports older than N days. Default is 30 days.')
    parser.add_argument('--incomplete', action='store_true',
                        help='Update all companies with status=incomplete')
    parser.add_argument('--update-cs-swe', action='store_true',
                        help='Update reports for given companies that support CS SWE')
    parser.add_argument('--update-cs-swe-batched', action='store_true',
                        help='Update reports for all companies that support CS SWE')
    parser.add_argument('--update-cs-swe-batched-emptax-orgs',
                        metavar='N', nargs='?', type=int, const=30, default=False,
                        help='Update reports for employet tax registered \
                            and AB companies that support CS SWE')
    parser.add_argument('--missing', action='store_true',
                        help='Update all companies with missing reports')
    parser.add_argument('-c', '--config', metavar='FILE',
                        help='app config file (default: %(default)s)',
                        default='/etc/bolfak/statusreports.cfg')
    parser.add_argument('--version', action='version', version="%(prog)s version " + __version__)

    args = parser.parse_args(argv)

    if sum([bool(args.incomplete), bool(args.all), bool(args.changed),
            bool(args.missing), bool(args.update_cs_swe),
            bool(args.update_cs_swe_batched)]) > 1:
        parser.error(
            'Specify only one of: --all, --changed, --outdated, '
            '--missing, --incomplete, --update-cs-swe'
        )
    elif sum([bool(args.gov_ids_f), bool(args.gov_ids)]) > 1:
        parser.error(
            'Specify only one of: --gov-ids GOV_IDS or GOV_IDS_FILE'
        )
    elif (1 == sum([bool(args.incomplete), bool(args.all),
                    bool(args.changed), bool(args.missing)])
          and
          1 == sum([bool(args.gov_ids_f), bool(args.gov_ids)])):
        parser.error(
            'Only mode --update-cs-swe is compatible with'
            ' --gov-ids GOV_IDS or GOV_IDS_FILE'
        )

    if 0 == sum([bool(args.gov_ids_f), bool(args.gov_ids),
                 bool(args.incomplete), bool(args.all), bool(args.changed),
                 bool(args.outdated), bool(args.missing),
                 bool(args.update_cs_swe), bool(args.update_cs_swe_batched),
                 bool(args.update_cs_swe_batched_emptax_orgs)]):
        args.all = True

    interested_org_id = args.interested_org_id
    conf = set_config(args.config)
    setup_azure_ai(conf, 'bol-statusreports')
    qvarn = setup_qvarn_api(conf, reason_getter=lambda: 'generating company reports')
    bda = setup_bda_client(conf)
    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(conf)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None
    contract_client = setup_contract_client(conf)
    storage = setup_storage(qvarn=qvarn, bda=bda, core=core, contract_api=contract_client)

    if args.all:
        logger.info('statusreports: Creating update all task')
        task_id = tasks.update_all_statusreports_task.delay().id
        logger.info('statusreports: Done creating update all task: %s', task_id)
    elif args.changed:
        logger.info('statusreports: Creating update changed since %s days task',
                    args.changed)
        task_id = tasks.update_swedish_changed_statusreports_task.delay(args.changed).id
        logger.info('statusreports: Done creating update changed since %s days task: %s',
                    args.changed, task_id)
    elif args.outdated:
        logger.info('statusreports: Creating update outdated (%s days) job', args.outdated)
        task_id = tasks.update_outdated_foreign_statusreports_task.delay(args.outdated).id
        logger.info('statusreports: Done creating update outdated (%s days) task: %s',
                    args.outdated, task_id)
    elif args.missing:
        logger.info('statusreports: Creating create missing task')
        task_id = tasks.update_missing_statusreports_task.delay().id
        logger.info('statusreports: Done creating create missing task: %s', task_id)
    elif args.incomplete:
        logger.info('statusreports: Creating update incomplete task')
        task_id = tasks.update_incomplete_statusreports_task.delay().id
        logger.info('statusreports: Done creating update incomplete task: %s', task_id)
    elif args.update_cs_swe:
        org_ids = []
        if args.gov_ids:
            org_ids = parse_gov_ids(storage, args.gov_ids)
        elif args.gov_ids_f:
            org_ids = parse_gov_ids_from_file(storage, args.gov_ids)

        if org_ids:
            logger.info('statusreports: Creating update CS reports for given orgs task')
            task_id = tasks.update_cs_swe_statusreports_task.delay(org_ids).id
            logger.info(
                'statusreports: Done creating update CS reports for given orgs task: %s',
                task_id,
            )
        else:
            logger.warning(
                'statusreports: Refusing to create status reports task'
                ' to update CS reports for given orgs as GOV_IDS were not given'
            )

    elif args.update_cs_swe_batched:
        logger.info('statusreports: Creating update all CS reports in batches task')

        interested = get_interested_for_all_orgs(storage, indiscrimiate_projects=True)
        interested = filter_interested_with_active_cs_account(storage, interested,
                                                              job_id='job creation')
        all_org_ids_set: Set[str] = set()
        for interested_org_id, org_ids_set in interested.items():
            all_org_ids_set.update(org_ids_set)
        # We will be batching by supplier org_id rather than by interested_org_id
        all_org_ids: List[str] = sorted(all_org_ids_set)
        logger.info(
            'statusreports: Creating update all CS reports in batches task:'
            ' found a total of %d companies for a total of %d interested',
            len(all_org_ids),
            len(interested),
        )
        # [1..105] will fit into 11 batches of size <= 10
        batch_size = UPDATE_REPORTS_BATCH_SIZE
        org_ids_batches = []
        for i in range(0, len(all_org_ids), batch_size):
            org_ids_batches.append(all_org_ids[i:i+batch_size])

        org_ids_batches_len = len(org_ids_batches)

        logger.info('statusreports: Creating update all CS reports in batches task:'
                    ' splitting task into %d batch jobs (batch size %d)',
                    len(org_ids_batches), batch_size)
        for n, org_ids_batch in enumerate(org_ids_batches):

            # set all org_ids in batches for the task
            task_id = tasks.update_cs_swe_batched_statusreports_task.delay(
                org_ids_batch, n, org_ids_batches_len
            ).id
            logger.info(
                'statusreports: Done creating update all CS reports in batches task:'
                ' batch %d of %d is task %s',
                n,
                org_ids_batches_len,
                task_id,
            )
    elif args.update_cs_swe_batched_emptax_orgs:
        logger.info('statusreports: Creating update all CS ab/emptax reports tasks (batched)')

        older_than_days = args.update_cs_swe_batched_emptax_orgs

        # We do the filtering here to reduce the number of creation of celery tasks
        interested = filter_interested_with_outdated_reports_swe(storage, older_than_days)

        batch_size = UPDATE_EMPTAX_BATCH_SIZE
        total_batches = len(interested) // batch_size
        if (len(interested) % batch_size != 0):
            total_batches += 1

        batch_nr = 0
        interested_org_ids = list(interested)
        for start in range(0, len(interested), batch_size):
            batch_nr += 1

            keys = interested_org_ids[start:start+batch_size]
            intrstd_batch = {k: interested[k] for k in keys}

            interested_batch_serializable = {
                intrstd_org_id: org_ids for intrstd_org_id, org_ids in intrstd_batch.items()
            }

            tasks.update_cs_swe_statusreports_with_emptax_task.delay(
                interested_batch_serializable, batch_nr, total_batches)

        logger.info('statusreports: Done creating update all CS ab/emptax reports tasks (batched)')

    elif args.gov_ids:
        org_ids = parse_gov_ids(storage, args.gov_ids)
        logger.info('statusreports: Creating update given orgs task')
        task_id = tasks.update_statusreports_orgs_task.delay(org_ids, interested_org_id).id
        logger.info('statusreports: Done creating update given orgs task: %s',
                    task_id)
    elif args.gov_ids_f:
        org_ids = parse_gov_ids_from_file(storage, args.gov_ids_f)
        logger.info('statusreports: Creating update given orgs file task')
        task_id = tasks.update_statusreports_orgs_file_task.delay(org_ids, interested_org_id).id
        logger.info('statusreports: Done creating update given orgs file task: %s',
                    task_id)
    else:
        logger.info('statusreports: No status report update job created - no valid parameters')

    # Log completion
    logger.info('statusreports: Done creating status report update job(s)')


if __name__ == '__main__':
    main()
