import argparse
import importlib.resources
import logging
import string
import sys
import threading
import uuid
from typing import Optional

import beaker.middleware
import bottle
import newrelic.agent
from stv.auth.authentication_plugin import AuthenticationPlugin
from stv.azure.helpers import LoggerOptions
from stv.data.clients.sp_framework_utils.sp_framework_utils_client import (
    setup_sp_framework_utils_client,
)
from stv.helpers.ui_language import get_lang

import bolfak
from bolfak.azure_ai import setup_azure_function_tracing
from bolfak.bottle import BolfakBottle, CustomAzureBottle
from bolfak.clients.bda import BdaClient, setup_bda_client
from bolfak.clients.company_api import setup_company_api_client
from bolfak.clients.autoaccount import setup_autoaccount_client
from bolfak.clients.company_qvarn_poc import setup_cqpoc_client
from bolfak.clients.composite import setup_storage
from bolfak.clients.contract import setup_contract_client
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.gluu import GLUU_AUTHORIZE_PATH, GLUU_TOKEN_PATH
from bolfak.clients.lima_api import setup_lima_api_client
from bolfak.clients.qvarn import qvarn_retry_loop, setup_qvarn_api
from bolfak.clients.sendgrid import setup_sendgrid_client
from bolfak.clients.stamp import setup_stamp_client
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.clients.redis import setup_redis_client
from bolfak.config import get_config, get_visitor_whitelist_org_ids, set_config
from bolfak.featureflags import feature_active, get_scopes
from bolfak.i18n import locale_detector
from bolfak.models import (
    ACCESS_TOKEN_SESSION_KEY,
    AUTH_INFO_SESSION_KEY,
    CREDITSAFE_CONTRACT_ACTIVE,
    CREDITSAFE_CONTRACT_ACTIVE_SP,
    AuthInfo,
)
from bolfak.services.authentication import (
    GLUU_USER_SCOPES,
    get_authenticated_person_id,
    get_representation_for_org,
)
from bolfak.utils import refresh_access_token, token_expired
from bolfak.views import (
    apidocs,
    archived_reports,
    authentication,
    bulkimport,
    cards,
    companies,
    config_js,
    autoaccount,
    notifications,
    preannouncements,
    project_tree,
    projects,
    search,
    staticfiles,
    status,
    status_reports,
    subscription,
    version,
    supplier_comments,
)


logger = logging.getLogger(__name__)

bottle.TEMPLATE_PATH.extend([
    str(importlib.resources.files('bolfak') / 'templates'),
])

# Configure bottle application
application = bottle.app()


# List of applications to mount to API_APP
API_APPS = [
    authentication.app,
    companies.app,
    config_js.app,
    status.app,
    status_reports.app,
    version.app,
    supplier_comments.app,
]


def setup_sessions(app, config):
    timeout = config.getint('sessions', 'timeout', fallback=14400)
    session_opts = {
        'session.auto': True,
        'session.type': config.get('sessions', 'type', fallback='memory'),
        # NB: when session.type is 'redis', we use beaker_redis and it wants a session.dsn
        # when sessio.type is 'ext:redis', we use beaker.ext.redisnm, and it wants a session.url
        'session.dsn': config.get('sessions', 'dsn', fallback=None),
        'session.url': config.get('sessions', 'dsn', fallback=None),
        'session.data_dir': config.get('sessions', 'data_dir', fallback=None),
        'session.key': config.get('sessions', 'cookie_name', fallback='session'),
        'session.timeout': timeout,
        'session.ttl': config.getint('sessions', 'ttl', fallback=timeout),
        'session.hkey_prefix': config.get('sessions', 'hkey_prefix', fallback='session'),
        'session.httponly': config.getboolean('sessions', 'httponly'),
        'session.encrypt_key': config.get('sessions', 'encrypt_key', fallback=None),
        'session.validate_key': config.get('sessions', 'validate_key', fallback=None),
        'session.secure': config.getboolean('sessions', 'secure'),
        'session.cookie_path': config.get('sessions', 'cookie_path', fallback='/'),
        'session.data_serializer': 'json',
        'session.samesite': config.get('sessions', 'samesite', fallback='Lax'),
    }

    cookie_domain = config.get('sessions', 'cookie_domain', fallback='')
    if cookie_domain:
        session_opts['session.cookie_domain'] = cookie_domain
    return beaker.middleware.SessionMiddleware(app, session_opts)


def session_getter():
    return bottle.request.session


def token_getter() -> str:
    session = session_getter()
    token = session.get(ACCESS_TOKEN_SESSION_KEY)
    if token and token_expired(token):
        refresh_access_token(session)
    return session.get(ACCESS_TOKEN_SESSION_KEY)


def reason_getter():
    try:
        reason = bottle.request.qvarn_reason
    except AttributeError:
        reason = ''
    return reason


def setup_authentication_plugin(config):
    return AuthenticationPlugin(
        gluu_server=config.get('gluu', 'base_url'),
        authentication_client_id=config.get('qvarn', 'client_id'),
        authentication_client_secret=config.get('qvarn', 'client_secret'),
        authentication_scopes=get_scopes(config.get('qvarn', 'scope')) + GLUU_USER_SCOPES,
        require_valid_ssl=config.getboolean('qvarn', 'verify_requests'),
        refresh_token_grace_period=60,
        frontend_url=config.get('main', 'frontend_url'),
        backend_url=config.get('main', 'backend_url'),
        routes_prefix="",
        login_initial_route="/authentication/login",
        login_return_route="/authentication/login-complete",
        logout_route_when_initiated_externally="/authentication/cleanup-on-externally-initiated-logout",  # noqa
        logout_initial_route="/authentication/logout",
        logout_return_route="/authentication/logout-complete",
        logger=logger,
        session_getter=session_getter,
        access_token_session_key=ACCESS_TOKEN_SESSION_KEY,
        ui_language_getter=locale_detector.detect_locale,
        auth_token_endpoint=GLUU_TOKEN_PATH,
        auth_authorize_endpoint=GLUU_AUTHORIZE_PATH
    )


def setup_auth_info_api(config):
    return setup_sp_framework_utils_client(
        api_url=config.get('main', 'sp_framework_api_url'),
        gluu_server=config.get('gluu', 'base_url'),
        client_id=config.get('qvarn', 'client_id'),
        client_secret=config.get('qvarn', 'client_secret'),
        user_access_token_getter=token_getter,
        session_getter=session_getter,
        refresh_token_grace_period_in_seconds=60,
        verify_tls=config.getboolean('main', 'sp_framework_api_verify_tls', fallback=True),
    )


def parse_active_org_id_from_path(path):
    path_parts = path.split('/')
    if len(path_parts) > 1:
        value = path_parts[1]
        # Check if object id is Qvarn compatible id.
        if set(value) - set(string.hexdigits + '-'):
            return None
        parts = value.split('-')
        if list(map(len, parts)) != [4, 32, 8]:
            return None
        return value
    return None


def core_auth_info() -> Optional[AuthInfo]:
    session = session_getter()
    auth_info = session.get(AUTH_INFO_SESSION_KEY)
    if auth_info:
        return AuthInfo.deserialize(auth_info)
    return None


def serve_auth_info():
    try:
        config = get_config()
        auth_api = setup_auth_info_api(config)
        org_id = parse_active_org_id_from_path(bottle.request.path)
        auth_info = auth_api.collect_ui_info(organisation_id=org_id)

        has_permission = False
        can_represent_org = False
        is_main_user = False
        user_profile = auth_info.get('userProfile')
        represented_org_info = {}

        if user_profile:
            auth_info['userProfile']['isAdmin'] = auth_info['otherUiInfo']['isAdmin']
            auth_info['userProfile']['person_id'] = auth_info['userProfile']['personId']
            auth_info['userProfile']['preferred_language_code'] = \
                auth_info['userProfile']['preferedLanguageCode']
            auth_info['userProfile']['full_name'] = auth_info['userProfile']['fullName']
            auth_info['userProfile']['email'] = auth_info['userProfile']['username']
            auth_info['userProfile']['phone'] = auth_info['userProfile']['phoneNumber']
            # BOL-2579: temp workaround - give fake permisions for local admin user
            auth_info['userProfile']['global_permissions'] = ['bolagsfakta_admin'] \
                if user_profile.get('email') == '<EMAIL>' else []

            # Rename creditsafe_account to creditsafe_contract in user_profile['representations']
            for representation in user_profile.get('representations', []):
                has_active_creditsafe_contract = representation.get(CREDITSAFE_CONTRACT_ACTIVE_SP)
                if has_active_creditsafe_contract is not None:
                    representation[CREDITSAFE_CONTRACT_ACTIVE] = has_active_creditsafe_contract
                    del representation[CREDITSAFE_CONTRACT_ACTIVE_SP]

            representation = get_representation_for_org(user_profile, org_id)

            if representation:
                can_represent_org = True
                has_permission = "instructor_user" in representation['permissions']
                is_main_user = representation.get('role') == "main"
                represented_org_info = representation

        additional_info = {}
        additional_info['userIsFullyRegistered'] = auth_info['otherUiInfo']['isFullyRegistered']
        additional_info['userHasInstructorUserPermissionInThisOrg'] = has_permission
        additional_info['userCanRepresentThisOrg'] = can_represent_org
        additional_info['represented_org_info'] = represented_org_info
        additional_info['userIsMainUserOfThisOrg'] = is_main_user
        additional_info['uiLanguage'] = get_lang() if not None else locale_detector.detect_locale()

        auth_info['additionalInfo'] = additional_info

        return auth_info

    except Exception:
        logging.error('Failed to serve auth info', exc_info=True)


def setup_request_session():
    if bottle.request.path == '/status':
        # /api/status requests are used as Kubernetes health checks.  There are
        # many of them and they're stateless, so creating a new session would
        # just stress Redis for no reason.  Checking authentication info would
        # also involve additional HTTP requests to Service Portal plus logspam.
        # Let's short-circuit all of that
        bottle.request.session = {}
        bottle.request.auth_info = {}
        bottle.request.user_id = None
        newrelic.agent.add_custom_parameter('user_id', None)
        newrelic.agent.add_custom_parameter('session_id', None)
        return
    bottle.request.session = session = bottle.request.environ['beaker.session']
    if feature_active('core_mitt_id06'):
        auth_info = core_auth_info()
    else:
        auth_info = serve_auth_info()
    bottle.request.auth_info = auth_info
    user_id = get_authenticated_person_id(bottle.request)
    bottle.request.user_id = user_id
    newrelic.agent.add_custom_parameter('user_id', user_id)
    newrelic.agent.add_custom_parameter('session_id', session.id)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--version', action='version',
                        version="%(prog)s version {}".format(bolfak.__version__))
    parser.add_argument(
        '--config',
        metavar='FILE',
        required=True,
        help='use FILE as configuration file')
    return parser.parse_args()


def merge_applications(target_app, config, plugins=[], app_hooks=None):
    """Merge routes from all apps into one target_app, optionally applying plugins and
    setting up hooks.

    This is needed because of the way Bottle handles plugins. Plugins are per-application
    and each route refers to its parent application to figure out which plugins apply to it.

    When applications are merged using app1.merge(app2) routes from app2 are added to app1
    BUT the routes still keep reference to app2 when applying plugins. So if we want plugins
    to apply to all application we need to install them individually to each app.

    Unfortunately this approach doesn't work when plugins set up routes in their setup()
    as those routes would be set up for each application before merging.

    That is why we take a plugin, apply it once to dest_app and then manually add
    it to a list of application plugins for each app we merge into the dest_app.

    Please suggest a better solution.
    """
    apps = API_APPS.copy()
    if config.getboolean('main', 'debug', fallback=False):
        apps.extend([apidocs.app, staticfiles.app])

    if config.getboolean('main', 'swagger_api', fallback=False):
        from bolfak.views import swagger
        apps.append(swagger.app)

    if config.getboolean('main', 'insecure_testdata_api', fallback=False):
        from bolfak.views import testdata
        apps.append(testdata.app)

    if config.getboolean('main', 'insecure_test_api', fallback=False):
        from bolfak.views import testapi
        apps.append(testapi.app)

    if feature_active('search'):
        apps.append(search.app)

    if feature_active('projects'):
        apps.extend([project_tree.app, projects.app, bulkimport.app])

    if feature_active('pre_announcements'):
        apps.extend([preannouncements.app, cards.app])

    if feature_active('create_and_activate_cs_accounts'):
        apps.append(autoaccount.app)

    if feature_active('notifications'):
        apps.append(notifications.app)

    if feature_active('archived_reports'):
        apps.extend([subscription.app, archived_reports.app])

    if feature_active("project_supplier_comments"):
        apps.append(supplier_comments.app)

    for app in apps:
        app.configure(config)
        if plugins:
            # Hack-on-a-hack: insert the new plugins in the front of the list, because
            # "the new plugins" includes the authentication plugin, and we want
            # it to get the chance to refresh the Gluu access token in the
            # session before i18n plugin tries to use it to load the user's
            # preferred language from Qvarn.  (See BOL-1556)
            app.plugins[:0] = plugins
            app.reset()

        target_app.merge(app)

    if app_hooks is not None:
        # Add any other application hooks.
        # This is needed because we wrap Bottle app in
        # a WSGI app so we can't add these later.
        for hook_name, hook_fn in app_hooks:
            target_app.hook(hook_name)(hook_fn)


def setup_application(args=None, app_hooks=None, bda: Optional[BdaClient] = None):
    """Set up the main application.

    app_hooks kwarg allows specifying additional top-level app hooks
    in the form of [(hook_name, hook_fn), (hook_name, hook_fn), ..]
    """
    # Configuration
    config = get_config()

    if feature_active('on_azure'):
        api_app = CustomAzureBottle(
            config,
            application_insights_name='bol',
            logger_options=LoggerOptions(
                exclude_paths=[r'^/status$'],
            ),
        )
        setup_azure_function_tracing()
        logger.info('Initialized Application Insights')
    else:
        api_app = BolfakBottle(config)

    logger.info('Setting up application...')

    # Log the visitor whitelist org_ids
    whitelist = get_visitor_whitelist_org_ids(config)
    logger.info('Visitor whitelist org_ids: %s', whitelist)

    docker_tag = get_config().get('main', 'docker_tag', fallback=None)
    logger.info('BOL version %s (docker tag %s)...', bolfak.__version__, docker_tag)

    api_base_path = config.get('main', 'base_path')

    logger.info('Connecting to Qvarn...')
    qvarn = setup_qvarn_api(config, extra_token_getter=token_getter, reason_getter=reason_getter,
                            lazy_qvarn_startup=feature_active('lazy_qvarn_startup'))
    if feature_active('lazy_qvarn_startup'):
        def _connect():
            gluu_base_url = config.get('gluu', 'base_url')
            try:
                qvarn_retry_loop(qvarn.client._requests._check_access_token, gluu_base_url)
            except Exception:
                logger.error('Failed to connect to Gluu at %s, giving up', gluu_base_url,
                             exc_info=True)
            else:
                logger.info('Qvarn connection ready')
        threading.Thread(target=_connect, name='QvarnConnectionThread').start()
    else:
        logger.info('Qvarn connection ready')

    if feature_active('bda_client'):
        logger.info('Connecting to bol-data-api...')
        bda = bda or setup_bda_client(config,
                                      extra_token_getter=token_getter, reason_getter=reason_getter)
    else:
        bda = None

    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(config)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    if feature_active('user_account_api'):
        logger.info('Connecting to user-account-api...')
        user_account_api = setup_user_account_api_client(config)
    else:
        user_account_api = None

    if feature_active('cqpoc_client'):
        logger.info('Setting up Company Qvarn POC API...')
        cqpoc_client = setup_cqpoc_client(config, reason_getter=reason_getter,
                                          extra_token_getter=token_getter)
    else:
        logger.info('Company Qvarn POC client disabled by feature flag')
        cqpoc_client = None

    sendgrid_client = setup_sendgrid_client(config)

    if feature_active('visitors'):
        logger.info('Setting up STAMP...')
        stamp_client = setup_stamp_client(config)
    else:
        logger.info('STAMP disabled by feature flag')
        stamp_client = None

    if feature_active('archived_reports'):
        logger.info('Setting up LimaAPI...')
        lima_api_client = setup_lima_api_client(
            config,
            user_access_token_getter=token_getter,
            session_getter=session_getter
        )

        logger.info('Setting up CompanyAPI...')
        company_api_client = setup_company_api_client(
            config,
            user_access_token_getter=token_getter,
            session_getter=session_getter
        )

    if feature_active('contract_api_creditsafe_contract'):
        logger.info('Setting up Contract API for Creditsafe account...')
        contract_client = setup_contract_client(config)
    else:
        logger.info('Contract API for Creditsafe account is disabled by feature flag')
        contract_client = None

    redis = setup_redis_client(config)

    storage = setup_storage(
        qvarn=qvarn,
        bda=bda,
        core=core,
        user_account_api=user_account_api,
        contract_api=contract_client,
        redis=redis,
    )

    if feature_active('create_and_activate_cs_accounts'):
        logger.info('Setting up Creditsafe Autoaccount...')
        autoaccount_client = setup_autoaccount_client(storage, config)
    else:
        logger.info('Creditsafe Autoaccount client is disabled by feature flag')
        autoaccount_client = None

    def setup_request():
        bottle.request.request_id = str(uuid.uuid4())
        bottle.request.qvarn_reason = None
        bottle.request.qvarn = qvarn
        bottle.request.bda = bda
        bottle.request.storage = storage
        bottle.request.sendgrid_client = sendgrid_client
        bottle.request.stamp_client = stamp_client
        bottle.request.cqpoc_client = cqpoc_client
        bottle.request.autoaccount_client = autoaccount_client
        if (bottle.request.method, bottle.request.path) != ('GET', '/status'):
            # We don't want to log anything for healtchecks, or we'll flood our logs (see BOL-4702)
            logger.debug('started processing %s %s', bottle.request.method, bottle.request.path)

        if feature_active('archived_reports'):
            bottle.request.lima_api_client = lima_api_client
            bottle.request.company_api_client = company_api_client

    def wsgi_errors_to_stderr():
        # XXX: With uWSGI bottle.request['wsgi.errors'] is io.TextIOWrapper who buffers all writes
        #      and error mesages appears much later in logs and some writes can be lost.
        #      See: https://github.com/bottlepy/bottle/pull/903
        #           https://jira.tilaajavastuu.fi/browse/BOL-313
        bottle.request['wsgi.errors'] = sys.stderr

    def add_cache_headers():
        # Tell browsers our API responses must not be cached.
        bottle.response.set_header('Cache-Control', 'no-store, must-revalidate')
        bottle.response.set_header('Pragma', 'no-cache')
        if (bottle.request.method, bottle.request.path) != ('GET', '/status'):
            # We don't want to log anything for healtchecks, or we'll flood our logs (see BOL-4702)
            logger.debug('finished processing %s %s', bottle.request.method, bottle.request.path)

    def add_cors_headers():
        allowed_origins = []
        for origin_config_key in ['frontend_url', 'standalone_url']:
            allowed_origins.append(config.get(
                'main', origin_config_key, fallback='').strip().rstrip('/'))
        origin = bottle.request.headers.get('Origin')
        bottle.response.set_header(
            'Access-Control-Allow-Origin',
            origin if origin in allowed_origins else ''
        )
        bottle.response.set_header('Access-Control-Allow-Methods', '*')
        bottle.response.set_header(
            'Access-Control-Allow-Headers',
            'authorization, request-id, traceparent, x-csrf-token, content-type'
        )
        bottle.response.set_header(
            'Access-Control-Expose-Headers',
            'x-csrf-token'
        )
        bottle.response.set_header('Access-Control-Allow-Credentials', 'true')
        if bottle.request.method == 'OPTIONS':
            bottle.response.status = 200
            return

    # Setup apps
    logger.info('Setting up app and plugins...')

    api_app.hook('before_request')(setup_request)
    api_app.hook('before_request')(setup_request_session)
    api_app.hook('before_request')(wsgi_errors_to_stderr)
    api_app.hook('after_request')(add_cache_headers)
    if feature_active('core_mitt_id06'):
        api_app.hook('after_request')(add_cors_headers)

    application.catchall = config.getboolean('bottle', 'catchall', fallback=True)

    plugins = []
    if not feature_active('core_mitt_id06'):
        auth_plugin = setup_authentication_plugin(config)
        api_app.install(auth_plugin)
        plugins.append(auth_plugin)
    merge_applications(api_app, config, plugins=plugins, app_hooks=app_hooks)

    # Setup middlewares
    logger.info('Setting up middleware...')
    api_app = setup_sessions(api_app, config)

    application.mount(api_base_path, api_app)

    logger.info('Application ready')

    return application


def initialize_wsgi_application():
    args = parse_args()
    set_config(args.config)
    global application
    application = setup_application(args)


try:
    import uwsgi  # noqa - to check if we're running under uwsgi
except ImportError:
    logger.info('not running on uwsgi')
else:
    logger.info('running on uwsgi')
    initialize_wsgi_application()
