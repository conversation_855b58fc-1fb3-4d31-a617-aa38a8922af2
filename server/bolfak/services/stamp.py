import logging

from requests.exceptions import HTTP<PERSON>rror, JSONDecodeError

from bolfak.config import MAX_ORG_IDS_PER_QUERY, get_visitor_whitelist_org_ids
from bolfak.exceptions import DataDuplicatesFoundError, DataNotFoundError, DataSubscriptionError
from bolfak.featureflags import feature_active
from bolfak.models import LINKED, PA_STATUS_CONFIRMED, RAW, VISITOR, obfuscate_id
from bolfak.services.project_tree import get_company_id
from bolfak.services.projects import get_tax_id
from bolfak.services.project_users import get_project_users
from bolfak.services.statusreports import start_project_status_update
from bolfak.storage.company import get_org
from bolfak.storage.core.company import subscribe_company_by_internal_id
from bolfak.storage.preannouncements import get_preannouncements
from bolfak.storage.projects import get_projects
from bolfak.storage.qvarn.projects import ACTIVE, get_project, get_responsible_company_id
from bolfak.storage.qvarn.stamp import update_project_stamps
from bolfak.storage.qvarn.suppliers import create_supplier_visitor, get_project_suppliers

from typing import Dict, Any, Set


logger = logging.getLogger(__name__)


def normalize_site_id(site_id):
    return site_id.upper().strip()


def get_stamps_from_client(stamp, project_tax_ids, date, country='SWE'):
    sites = [
        {
            'site_id': normalize_site_id(tax_id['project']),
            # TODO: STAMP uses SE. Investigate it
            'country_code': 'SE'
        } for tax_id in project_tax_ids
    ]
    if feature_active('stamp_workaround_one_site_at_a_time'):
        logger.info(f'Stamp client will retrieve visitors for {len(sites)} sites, one by one...')
        result: Dict[str, Any] = {'date': None, 'sites': {}}
        for site in sites:
            try:
                response = stamp.get_companies([site], date).json()
            except HTTPError as e:
                logger.warning('Failed to fetch stamp data for %s: %s: %s',
                               site, e, e.response.text)
                continue
            except JSONDecodeError as e:
                logger.error('Failed to parse stamp data for %s: %s: %s',
                             site, e, response)
                continue

            if 'date' in response:
                result['date'] = response['date']
            assert isinstance(result['sites'], dict)
            result['sites'].update(response.get('sites', {}))
    else:
        logger.info(f'Stamp client will retrieve visitors for {len(sites)} sites...')
        result = stamp.get_companies(sites, date).json()
    logger.info(f'Stamp client retrieved visitors for {len(sites)} sites.')
    logger.info(f'Stamp client retrieved visitors for {len(result.get("sites", []))} sites.')
    return result


def get_stamp_project_ids(projects, filter_project_tax_ids=None):
    """BOL does no support project['country']."""
    logger.info(f'Stamps computing project tax_id for {len(projects)} project(s)')
    projects_tax_ids = []
    for n, project in enumerate(projects):
        tax_id = get_tax_id(project)
        if tax_id:
            tax_id = tax_id.strip()
            if len(tax_id) == 13 and tax_id[:2] == 'PL':
                projects_tax_ids.append(
                    {
                        'project': tax_id,
                        'country': 'SWE',
                    }
                )
    if filter_project_tax_ids:
        filtered_project_tax_ids = [
            project_tax_id for project_tax_id in projects_tax_ids
            if project_tax_id['project'] in filter_project_tax_ids
        ]
        projects_tax_ids = filtered_project_tax_ids
    logger.info(f'Stamps computed project tax_id for {len(projects_tax_ids)} project(s)')
    return projects_tax_ids


def get_bol_stamps(storage, *, stamps=None, projects=None):
    """Process stamps and return bol_stamps that match project/company ids.

    Returns
    {
        <project-db-id>:
        {
            <company-db-id-1>,
            <company-db-id-2>,
            <company-db-id-3>,
        }
    }
    Skips projects and companies that are not found.

    TODO: return projects and companies not found (not just log these).
    """
    if not all([stamps, projects]):
        return {}
    project_stamps = stamps['sites']
    projects_by_tax_id = {
        normalize_site_id(get_tax_id(p)): p['id']
        for p in projects if get_tax_id(p)
    }
    bol_stamps: Dict[str, Set[str]] = {}
    for project_tax_id, companies in project_stamps.items():
        project_tax_id = normalize_site_id(project_tax_id)
        project_id = projects_by_tax_id.get(project_tax_id)
        if project_id is None:
            logger.error('Failed to find stamp project tax_id: %s',
                         project_tax_id)
            continue
        # Replace domain ids with DB-internal ids
        bol_stamps[project_id] = set()
        for company_meta in companies:
            gov_org_id = company_meta['company_registration_number']
            country = company_meta['country_code']
            try:
                company = get_org(storage, gov_org_id=gov_org_id, country=country)
            except (DataDuplicatesFoundError, DataNotFoundError):
                logger.error('Failed to find stamp company: %s',
                             obfuscate_id(gov_org_id))
                continue
            bol_stamps[project_id].add(company['id'])
    return bol_stamps


def get_preannoucements_batches(storage, for_supplier_ids, *,
                                batch_size=MAX_ORG_IDS_PER_QUERY,
                                progress_callback=None):
    """Return batches of preannoucements for suppliers as a generator.

    Parameters:
    - for_supplier_ids - any of for_supplier_ids
    - batch_size - at most for_suppliers to get preannoucements in one batch
    - progress_callback - a function that takes (batch_start, batch_size, total_for_suppliers)

    """
    for start in range(0, len(for_supplier_ids), batch_size):
        batch = for_supplier_ids[start: start + batch_size]
        if progress_callback:
            progress_callback(start, len(batch), len(for_supplier_ids))
        yield get_preannouncements(storage, for_supplier_ids=batch)


def get_supply_chain_company_ids(storage, project):
    """Company ids that should not treated as visistors in the project """

    suppliers = get_project_suppliers(storage,
                                      project['id'],
                                      supplier_types=[LINKED])

    # Existing LINKED company ids

    # Count as supply chain:
    #  - suppliers that don't have PA
    #  - suppliers that have PA an PA.satus == PA_STATUS_CONFIRMED
    for_supplier_ids = [s['id'] for s in suppliers]

    # BOL-4864: get PAs in batches to avoid to long queries causing 400 bad requests in production
    pas = []
    for batch in get_preannoucements_batches(storage, for_supplier_ids):
        pas.extend(batch)

    pa_statuses = {pa['for_supplier_id']: pa['status'] for pa in pas}

    supply_chain_comp_ids = []
    supply_chain_comp_ids.append(get_responsible_company_id(project))
    for s in suppliers:
        if (s['id'] in pa_statuses and pa_statuses[s['id']] != PA_STATUS_CONFIRMED):
            continue
        supply_chain_comp_ids.append(get_company_id(s))

    return supply_chain_comp_ids


def get_visitor_company_ids(storage, project):
    # Existing VISITOR company ids
    visitors = get_project_suppliers(storage,
                                     project['id'],
                                     supplier_types=[VISITOR])
    visitor_comp_ids = [get_company_id(v) for v in visitors]

    return visitor_comp_ids


def create_supplier_visitor_with_subscription(storage, project, supplier_company_id, visitor_type):
    if feature_active('core_mitt_id06'):
        try:
            subscribe_company_by_internal_id(storage, supplier_company_id)
            logger.info('Supplier company %s has been subscribed', supplier_company_id)
        except DataSubscriptionError as e:
            logger.error('Supplier company %s subscription has failed: %s', supplier_company_id, e)

    create_supplier_visitor(storage, project, supplier_company_id, visitor_type=visitor_type)


def _fetch_reports_for_new_visitors(storage, project, new_visitor_company_ids):
    """Fetch reports for new visitors when whitelisted orgs have permission."""
    # Get visitor whitelist
    visitor_whitelist = set(get_visitor_whitelist_org_ids())

    if not visitor_whitelist:
        return

    # Get project users to find orgs with permission
    project_users = get_project_users(storage, project['id'])
    project_user_org_ids = {
        pu['project_user']['represented_company_id'] for pu in project_users
    }

    # Find whitelisted orgs that have project users (can_view_visitors permission)
    whitelisted_with_permission = visitor_whitelist & project_user_org_ids

    if whitelisted_with_permission:
        # Fetch reports for new visitors on behalf of these orgs
        start_project_status_update(storage, new_visitor_company_ids, project)


def create_visitors(storage, project, new_visitor_company_ids):
    if new_visitor_company_ids:
        logger.info(f'Visitors creating visitor nodes for project {project["id"]}...')
    for c_id in new_visitor_company_ids:
        create_supplier_visitor_with_subscription(storage, project, c_id, visitor_type=RAW)


def process_stamps_project(storage, project_id, stamp_comp_ids, date):
    """
    Create visitors/update visiting times for project_id given stamp_company_ids
    """
    project = get_project(storage, project_id)
    supply_chain_comp_ids = get_supply_chain_company_ids(storage, project)
    visitor_comp_ids = get_visitor_company_ids(storage, project)
    new_visitor_comp_ids = list(
        set(stamp_comp_ids)
        .difference(set(supply_chain_comp_ids))
        .difference(set(visitor_comp_ids))
    )

    # Create new visitor nodes
    create_visitors(storage, project, new_visitor_comp_ids)

    # Fetch reports for new visitors when whitelisted orgs have can_view_visitors permission
    if new_visitor_comp_ids:
        _fetch_reports_for_new_visitors(storage, project, new_visitor_comp_ids)

    # Update visitor nodes with stamps
    # do not update visitor which companies has linked supplier in the project
    updatable_visitor_comp_ids = list(
        set(visitor_comp_ids + new_visitor_comp_ids)
        .difference(set(supply_chain_comp_ids))
        .intersection(set(stamp_comp_ids))
    )

    if not updatable_visitor_comp_ids:
        return updatable_visitor_comp_ids

    logger.info(f'Visitors updating visited times for project {project_id}...')

    update_project_stamps(storage, project_id, date,
                          company_ids=updatable_visitor_comp_ids)
    return updatable_visitor_comp_ids


def process_stamps(storage, stamp_client, date, filter_project_tax_ids=None):
    """Create visitors/update visit time from stamp_client on date."""
    logger.info('Visitors processing starting...')

    projects = get_projects(storage, states=[ACTIVE])
    if not projects:
        logger.info(f'Visitors found no {ACTIVE} projects, will stop')
        return []

    stamp_project_ids = get_stamp_project_ids(projects, filter_project_tax_ids)
    if not stamp_project_ids:
        message = ('Visitors found no projects with appropriate tax ids, will stop; '
                   f'checked {len(projects)} projects')
        message += (f', matched against {len(filter_project_tax_ids)} given tax_ids)'
                    if filter_project_tax_ids is not None else '')
        logger.info(message)
        return []

    logger.info(f'Visitors retrieving stamps for {len(stamp_project_ids)} sites...')
    raw_stamps = get_stamps_from_client(stamp_client, stamp_project_ids, date)

    n_sites_with_visitors = len(raw_stamps['sites']) if raw_stamps else 0
    logger.info(f'Visitors matching {n_sites_with_visitors} sites that had stamps...')
    bol_stamps = get_bol_stamps(storage, stamps=raw_stamps, projects=projects)
    updated = []
    not_updated = []
    for project_id, stamp_company_ids in bol_stamps.items():
        logger.info(f'Visitors updating started for project {project_id}...')

        updated_visitor_comp_ids = process_stamps_project(storage, project_id,
                                                          stamp_company_ids,
                                                          date)
        if not updated_visitor_comp_ids:
            logger.info('Visitors updating found only legitimate attendee stamps '
                        f'in project {project_id}')
            not_updated.append(project_id)
            continue

        updated.append({'project_id': project_id,
                        'company_ids': updated_visitor_comp_ids})
        logger.info(f'Visitors updating updated/created {len(updated_visitor_comp_ids)} '
                    f'visitors in project {project_id}')

        logger.info(f'Visitors updating done for project {project_id}')
    logger.info('Visitors processing done: '
                f'processed {len(stamp_project_ids)} projects, '
                f'retrieved {n_sites_with_visitors} sites that had visitors, '
                f'prepared to update {len(bol_stamps)} projects, '
                f'updated stamps for {len(updated)} projects, '
                f'found only legitimate attendees in {len(not_updated)} projects.')
    return updated
