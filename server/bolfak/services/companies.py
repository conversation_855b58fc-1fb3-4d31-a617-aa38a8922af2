import datetime
import importlib
import logging
from collections import defaultdict
from itertools import groupby
from typing import Collection, Dict, List, Optional, Set, Tuple, TypedDict, cast

from bottle import FormsDict
from stdnum.exceptions import ValidationError
from unidecode import unidecode

import bolfak.clients.company_api as company_api
from bolfak.clients.company_qvarn_poc import CompanyQvarnPocClient
from bolfak.clients.composite import Storage
from bolfak.config import (
    REPORT_PROVIDER_CONTRACT_TYPE_CREDITSAFE,
    get_service_provider,
)
from bolfak.exceptions import DataDuplicatesFoundError, DataNotFoundError
from bolfak.featureflags import feature_active
from bolfak.models import (
    GOV_ORG_ID_VALIDATOR_MODULE,
    LINKED,
    MAIN_CONTRACTOR_ROLE,
    SUPERVISOR_ROLE,
    SUPPLIER_ROLE,
    USER_ROLE_BASIC,
    USER_ROLE_MAIN,
    STATUS_OK,
    STATUSES,
    UNLINKED,
    alpha2_country_code,
    alpha3_country_code,
    get_gov_org_id,
    get_org_name,
    get_org_vat,
    normalize_dash,
    normalize_swedish_id,
    normalize_whitespace,
    obfuscate_id,
)
from bolfak.services.helpers.filters import gov_org_id_exact_match_exclude_f_tax
from bolfak.services.helpers.reports import encode_status_db_repr
from bolfak.services.project_users import get_user_project_id_list_for_contract
from bolfak.services.projects import (
    get_project_id,
    get_project_ids_of_active_org_id,
    get_project_internal_id,
    get_project_statuses,
)
from bolfak.services.qvarn.report_accesses import create_report_access, get_active_report_access_ids
from bolfak.services.reports import (
    DEFAULT_STATUS,
    NoReportError,
    companies_have_reports,
    get_latest_company_report_status,
)
from bolfak.services.subscription import get_subscription
from bolfak.services.user_accounts import (
    get_user_account_for_person_and_org,
    org_person_to_user_account,
    user_account_has_bol_permission,
)
from bolfak.services.users import get_person_official_name, get_user_email, is_person_registered
from bolfak.storage.company import get_org, get_orgs, search_orgs, get_orgs_by_gov_org_id
from bolfak.storage.core.persons import search_org_person
from bolfak.storage.models import InternalCompanyInfo
from bolfak.storage.persons import get_organisation_person
from bolfak.storage.projects import get_non_closed_projects
from bolfak.storage.qvarn.company import (
    create_org,
    delete_org,
)
from bolfak.storage.qvarn.models import (
    QvarnOrgResultDict,
    QvarnContractResultDict,
    QvarnSupplierResultDict,
)
from bolfak.storage.qvarn.persons import search_person_ids
from bolfak.storage.qvarn.projects import (
    CLOSED,
    EMPTY_PROJECT_STATUS,
    PROJECT_STATES_NOT_CLOSED,
    TAX_ID_TYPE,
    get_multiple_projects,
    get_project_ids,
)
from bolfak.storage.qvarn.suppliers import (
    get_all_supplier_org_ids,
    get_orgs_suppliers,
    get_projects_suppliers,
    get_suppliers_for_company_list,
    get_supplier_project_ids,
)
from bolfak.storage.supplier_comments import fetch_project_comments_batch
from bolfak.models import Comment
from bolfak.views.common import name_key, sorted_by_name


class GovOrgId(TypedDict):
    country: str
    gov_org_id: str
    org_id_type: str


class CompanyUserInfo(TypedDict):
    full_name: Optional[str]
    email: str
    contract_id: Optional[str]
    person_id: str
    is_active: bool
    has_bol_permission: bool


logger = logging.getLogger(__name__)


def get_project_comments_batch(
    storage: Storage, project_ids: List[str], org_id: str
) -> dict[str, List[Comment]]:
    """Fetch project comments for multiple projects with batching and error handling.
    """
    if not project_ids:
        return {}

    comments_by_project = {}
    batch_size = 100  # Maximum limit per BDA call

    # Process project IDs in batches of 100
    for start in range(0, len(project_ids), batch_size):
        batch_project_ids = project_ids[start:start + batch_size]

        try:
            # Make the batch API call via storage layer
            response_data = fetch_project_comments_batch(storage, batch_project_ids, org_id)
            batch_comments = response_data['resources']

            # Process each project's comments in the batch
            for project_id in batch_project_ids:
                project_comments_data = batch_comments.get(project_id, [])
                # Convert to Comment objects
                comments_by_project[project_id] = [
                    Comment(**comment_dict) for comment_dict in project_comments_data
                ]

        except Exception as e:
            # Handle BDA failures gracefully
            logger.warning(
                f"Failed to fetch comments for batch {batch_project_ids}: {e}. "
                f"Returning empty results for this batch."
            )
            # Return empty results for this batch
            for project_id in batch_project_ids:
                comments_by_project[project_id] = []

    return comments_by_project


def get_org_id(storage, gov_org_id, service_provider=None, country=None) -> Optional[str]:
    org_ids = search_orgs(storage, gov_org_ids=[gov_org_id],
                          service_provider=service_provider, country=country)
    return org_ids[0] if len(org_ids) else None


def get_org_exclude_f_tax(storage, gov_org_id, service_provider=None, country=None):
    result = get_orgs(storage,
                      gov_org_ids=[gov_org_id],
                      service_provider=service_provider,
                      country=country)
    orgs_no_f_tax_match = [org for org in result if
                           gov_org_id_exact_match_exclude_f_tax(org, gov_org_id)]

    if len(orgs_no_f_tax_match) > 1:
        raise DataDuplicatesFoundError(
            'More than one org found for gov_org_id=%s, country=%s, service_provider=%s' % (
                obfuscate_id(gov_org_id), country, service_provider))
    if len(orgs_no_f_tax_match) == 0:
        raise DataNotFoundError(
            'No org found for gov_org_id=%s, country=%s, service_provider=%s' % (
                obfuscate_id(gov_org_id), country, service_provider))

    return orgs_no_f_tax_match[0]


def normalize_gov_org_id(gov_org_id: str, country: str) -> str:
    """Normalize gov_id for supported countries"""

    country_a2 = alpha2_country_code(country)
    validator_module_name = GOV_ORG_ID_VALIDATOR_MODULE.get(country_a2)
    if not validator_module_name:
        logger.warning('Normalize gov_org_id: unsupported country=%s' %
                       country)
        return gov_org_id

    normalizer_module = importlib.import_module(validator_module_name)

    try:
        normalizer_module.validate(gov_org_id)
    except ValidationError as e:
        logger.warning('Normalize gov_org_id: not possible for gov_org_id=%s country=%s: %s',
                       gov_org_id, country, e)
    else:
        # XXX Some validators, like DK vatnum validators don't have .format()
        try:
            gov_org_id = normalizer_module.format(gov_org_id)
        except AttributeError:
            return gov_org_id

    return gov_org_id


def _get_project_counts_by_company(suppliers):
    """Returns a dict of project counts per company.

    Return as dict:
    {
        'company_id': 42,
        ...
    }
    """
    project_sets_per_company = defaultdict(set)
    for supplier in suppliers:
        project_sets_per_company[supplier['supplier_org_id']].add(supplier['project_resource_id'])
    return {org_id: len(project_set) for org_id, project_set in project_sets_per_company.items()}


def status_and_name_sort_key(company):
    return (STATUSES.index(company['company_status']), name_key(company['name'].lower()))


def get_overall_status(company, default_status=None):
    return (
        company['status']['overall_status']
        if company.get('status') and company['status'].get('overall_status')
        else default_status)


def overall_status_and_name_sort_key(company):
    return (STATUSES.index(get_overall_status(company, STATUS_OK)),
            name_key(company['name'].lower()))


def _filter_by_keyword(entries, keyword):
    keyword = keyword.lower().strip()
    return [x for x in entries if (
        x.get('company_id') and keyword in x.get('company_id').lower() or
        x.get('vat_number') and keyword in x.get('vat_number').lower() or
        x.get('name') and keyword in x.get('name').lower()
    )]


def _filter_by_status(entries, status):
    if status.startswith('not:'):
        status = status[4:]
        return [x for x in entries if x['company_status'] != status]
    else:
        return [x for x in entries if x['company_status'] == status]


def get_all_bolagsfakta_org_ids(qvarn):
    return get_all_supplier_org_ids(qvarn)


def search_companies(company_api_client, country, free_text):
    response = company_api.search(company_api_client, country, free_text)

    orgs = response['results']
    too_many_results = response['too_many_results']

    return {
        'results': [
            {
                'terminated': org['terminated'],
                'is_reliable_partner': org['is_reliable_partner'],
                'rala': org['rala'],
                'gov_org_ids': org['gov_org_ids'],
                'country': alpha3_country_code(org['country']),
                'name': get_org_name(org),
                'has_subsidiaries': org['has_subsidiaries'],
            }
            for org in orgs
        ],
        'too_many_results': too_many_results,
    }


def _get_companies__bolswe(
    storage: Storage,
    user_contract: QvarnContractResultDict,
    active_org_id: str,
    is_admin: bool,
    user_role: Optional[str],
    query: FormsDict,
) -> List[InternalCompanyInfo]:
    # Can always view own organisation
    company_ids = {active_org_id}

    suppliers = _get_company_list_suppliers(
        storage, query, user_contract, active_org_id, is_admin, user_role
    )

    for supplier in suppliers:
        company_ids.add(supplier['supplier_org_id'])

    if is_admin:
        # add all project responsible companies
        company_ids |= {
            p['project_responsible_org']
            for p in get_non_closed_projects(storage)
        }

    companies: List[InternalCompanyInfo] = get_orgs(storage, company_ids)

    found_companies_ids = [company['id'] for company in companies]
    not_found_comp_ids = set(company_ids).difference(set(found_companies_ids))
    if not_found_comp_ids:
        logger.warning(
            f"Company list: not found companies ids: {', '.join(list(not_found_comp_ids))}"
        )

    projects_by_company = _get_project_counts_by_company(suppliers)
    reports_available_by_company = companies_have_reports(storage, company_ids)
    for company in companies:
        report_status = get_company_report_status(storage, company['id'], active_org_id)
        company['company_status'] = report_status or DEFAULT_STATUS
        company['report_available'] = reports_available_by_company.get(company['id'], False)
        company['project_count'] = projects_by_company.get(company['id'], 0)
    return companies


def _get_companies__bolswe_bda(
    storage: Storage,
    cqpoc_client: CompanyQvarnPocClient,
    active_org_id: str,
    is_admin: bool,
    user_role: Optional[str],
    user_contract: Optional[QvarnContractResultDict],
    search: str,
    limit: int = 0,
    offset: int = 0,
) -> List[InternalCompanyInfo]:
    user_projects_ids = None
    if user_role == 'basic' and not is_admin:
        user_projects_ids = get_user_project_id_list_for_contract(
            storage, user_contract, active_org_id, state=PROJECT_STATES_NOT_CLOSED
        )
    params = {
        'user_active_org_id': active_org_id,
        'user_active_org_role': user_role,
        'user_is_admin': is_admin,
        'user_projects_ids': user_projects_ids,
        'filter.search': search,
        'limit': limit,
        'offset': offset,
    }
    if feature_active('block_project_client'):
        params['ff_block_project_client'] = True
    resp = storage.bda.post('/companies/list', json=params)
    bda_companies_by_id = {c['id']: c for c in resp.json()['resources']}
    company_ids = list(bda_companies_by_id)
    if feature_active('cqpoc_get_companies'):
        companies = cqpoc_client.get_multiple_companies(company_ids)
    else:
        companies = get_orgs(storage, company_ids)

    # Some companies can disappear from DB this must not happen in PROD
    companies = [company for company in companies
                 if company and 'error' not in company]
    found_companies_ids = [company['id'] for company in companies]

    not_found_comp_ids = set(company_ids).difference(set(found_companies_ids))
    if not_found_comp_ids:
        logger.error(
            f"BDA company list: not found companies ids: {', '.join(list(not_found_comp_ids))}"
        )

    for company in companies:
        company_bda = bda_companies_by_id[company['id']]
        company['company_status'] = company_bda['latest_report_status'] or DEFAULT_STATUS
        company['report_available'] = company_bda['report_available']
        company['project_count'] = company_bda['project_count']

    sorted_companies = sorted(
        companies,
        key=lambda x: (encode_status_db_repr(x['company_status']), x['names'])
    )

    return sorted_companies


def get_company_list(
    storage: Storage,
    query: FormsDict,
    # BOLSWE args
    user_contract,
    active_org_id,
    is_admin=False,
    user_role=None,
    cqpoc_client=None,
    # TODO: Deprecate BOLFIN arg in https://id06.atlassian.net/browse/BOL-6416
    company_api_client=None,
    offset=None,
    limit=None,
):
    if feature_active('bda_company_list'):
        companies = _get_companies__bolswe_bda(
            storage,
            cqpoc_client,
            active_org_id,
            is_admin,
            user_role,
            user_contract,
            query.search,
            offset,
            limit,
        )

    # Even with bda speedup enabled use this slow function for basic users as
    # bda can not check projects via user_contract_id.
    elif feature_active('projects'):
        companies = _get_companies__bolswe(
            storage, user_contract, active_org_id, is_admin, user_role, query
        )

    else:
        raise RuntimeError(
            "In order to get project list projects or archived_reports "
            "feature flag must be enabled."
        )

    company_list = [
        {
            'id': company['id'],
            'company_id': get_gov_org_id(company),
            'vat_number': get_org_vat(company),
            'country': alpha3_country_code(company['country']),
            'name': get_org_name(company) if 'names' in company else '',
            'company_status': company['company_status'],
            'report_available': company['report_available'],
            'project_count': company['project_count'],
            'has_combined_report': company.get('has_combined_report'),
        }
        for company in companies
    ]

    if query.status:
        company_list = _filter_by_status(company_list, query.status)

    if query.search:
        company_list = _filter_by_keyword(company_list, query.search)

    return company_list


def _get_company_list_suppliers(
    storage: Storage,
    query: Dict,
    user_contract: QvarnContractResultDict,
    active_org_id: str,
    is_admin=False,
    user_role=None,
) -> List[Dict]:
    """Get all suppliers for active user.

    Don't filter."""

    if is_admin:
        user_project_ids = get_project_ids(storage, state=PROJECT_STATES_NOT_CLOSED)
    else:
        user_project_ids = get_project_ids_of_active_org_id(
            storage, user_contract, active_org_id, user_role
        )

    suppliers = get_suppliers_for_company_list(storage,
                                               query,
                                               user_project_ids,
                                               types=[LINKED, UNLINKED])
    return suppliers


def _get_company_related_project_ids_and_suppliers(
    storage: Storage,
    org: Dict,
    user_contract: QvarnContractResultDict,
    active_org_id: str,
    is_admin: bool = False,
    user_role: Optional[str] = None,
) -> Tuple[Collection[str], List[QvarnSupplierResultDict]]:
    if user_role not in (USER_ROLE_MAIN, USER_ROLE_BASIC) and not is_admin:
        # If user is not basic, main or admin, don't show any projects
        return set(), []

    if is_admin:
        project_ids = {
            supplier["project_resource_id"]
            for supplier in get_orgs_suppliers(
                storage, [org["id"]], select=["project_resource_id"]
            )
        }
    else:
        project_ids = get_company_related_project_ids_for_org(
            storage,
            org["id"],
            user_contract,
            active_org_id,
            user_role == USER_ROLE_MAIN,
        )

    suppliers = get_projects_suppliers(storage, list(project_ids))
    return project_ids, suppliers


def get_company_related_project_ids_for_org(
    storage: Storage,
    org_id: str,
    user_contract: QvarnContractResultDict,
    active_org_id: str,
    is_main_user: bool,
) -> Set[str]:
    """Get project ids where org_id is a supplier, and the user should see them based on their
    role, organisation and contract. Returns a set of project ids.
    Arguments:
    storage -- Storage object
    org_id -- organisation id to get projects for
    user_contract -- user's contract
    active_org_id -- user's active organisation id
    is_main_user -- whether the user is a main user of their organisation
    ."""
    # We want to return the projects where the user's org is
    # (responsible, supervisor or main contractor)
    # or
    # (the user's org is a supersupplier of the supplier and it's a PA project).
    # In addition, we only want to return projects where the user is a project member
    # if the user is not a main user of their organisation.
    org_suppliers = get_orgs_suppliers(
        storage, [org_id], select=["project_resource_id", "materialized_path"]
    )
    project_ids_where_user_is_supervisor_or_main_contractor = set(
        get_supplier_project_ids(
            storage,
            active_org_id,
            supplier_roles=[MAIN_CONTRACTOR_ROLE, SUPERVISOR_ROLE],
        )
    )
    project_ids_where_user_is_responsible_org = set(
        get_project_ids(storage, org_id=active_org_id, state=PROJECT_STATES_NOT_CLOSED)
    )
    project_ids_where_user_is_responsible_org_supervisor_or_main_contractor = (
        project_ids_where_user_is_supervisor_or_main_contractor
        | project_ids_where_user_is_responsible_org
    )
    pa_project_ids_where_user_is_supplier = set(
        get_supplier_project_ids(
            storage,
            active_org_id,
            supplier_roles=[SUPPLIER_ROLE],
            supplier_types=[LINKED],
            pa_form_enabled=True,
        )
        if feature_active("pre_announcements")
        else []
    )
    project_ids = {
        supplier["project_resource_id"]
        for supplier in org_suppliers
        if (
            supplier["project_resource_id"]
            in project_ids_where_user_is_responsible_org_supervisor_or_main_contractor
        )
        or (
            supplier["project_resource_id"] in pa_project_ids_where_user_is_supplier
            and supplier["materialized_path"] is not None
            and active_org_id in supplier["materialized_path"]
        )
    }
    if not is_main_user:
        # If not main user, only include projects the user belongs to
        user_project_ids = get_user_project_id_list_for_contract(
            storage, user_contract, active_org_id
        )
        logger.debug(
            "User is not main user, hiding the following projects: %s",
            project_ids - set(user_project_ids),
        )
        project_ids = project_ids & set(user_project_ids)

    return project_ids


def get_company_related_projects(
    storage: Storage,
    org: Dict,
    user_contract: QvarnContractResultDict,
    active_org_id: str,
    is_admin: bool = False,
    user_role: Optional[str] = None,
) -> List[Dict]:
    project_ids, suppliers = _get_company_related_project_ids_and_suppliers(
        storage, org, user_contract, active_org_id, is_admin, user_role
    )
    # user should always see final status of projects they are members of (See BOL-647)
    statuses = get_project_statuses(
        storage,
        project_ids,
        interested_org_id=active_org_id,
        suppliers=suppliers,
    )

    projects = get_multiple_projects(storage, project_ids)

    def project_status(project):
        # Hide project_status if added_client has not been confirmed, show otherwise
        if (project['project_creator_role'] == MAIN_CONTRACTOR_ROLE
                and not project['added_client_confirmed']
                and project['project_responsible_org'] == org['id']):
            status = None
        else:
            status = statuses.get(project['id'], EMPTY_PROJECT_STATUS)
        return status

    def should_include_project(project):
        # The added client should only see the related project
        # if it has not been blocked to see and
        # if the client has already confirmed the project
        if feature_active('block_project_client') and project.get(
            # Added client when project created by the main contractor
            'project_creator_role') == MAIN_CONTRACTOR_ROLE and project.get(
                # and the client is the responsible org
                'project_responsible_org') == active_org_id:
            return project.get(
                'added_client_confirmed') and project.get(
                'added_client_can_view')
        else:
            return True

    return sorted_by_name([
        {
            'id': project['id'],
            'name': project['names'][0],
            'project_id': get_project_internal_id(storage, project, active_org_id, is_admin,
                                                  suppliers=suppliers),
            'tax_id': get_project_id(project, id_type=TAX_ID_TYPE),
            'status': project_status(project),
            'start_date': project.get('start_date'),
            'end_date': project.get('end_date'),
        } for project in projects if (project.get('state') != CLOSED and
                                      should_include_project(project))
    ], 'name')


def get_subscription_time(request, active_org_id):
    subscription = get_subscription(request, active_org_id, active_only=True)
    subscription_time = (
        subscription['terms_of_service'][0]['acceptance_time']
        if subscription else None
    )
    return subscription_time


def get_company_archived_reports(request, active_org_id, country, gov_org_id):
    response = company_api.get_archived_reports_by_company(
        request.company_api_client,
        active_org_id,
        alpha2_country_code(country),
        gov_org_id,
    )
    subscription_time = get_subscription_time(request, active_org_id)
    reports = [
        {
            'access_id': r['access_id'],
            'archive_id': r['archive_id'],
            'access_time': r['access_time'],
            'status': (
                r['status']
                if subscription_time and r['access_time'] >= subscription_time
                else 'hidden'
            )
        }
        for r in response['reports']
    ]
    # BOL-3511 filter out repeated entries in archived reports table
    # created by opening reports in different languages
    # 1. Most recent archive entries are interested
    reports = sorted(reports, key=lambda r: (r['archive_id'], r['access_time']), reverse=True)
    # 2. Remove non-unique and earlier accessed archived reports entries
    reports = [list(item)[0] for _, item in
               groupby(reports, key=lambda r: (r['archive_id'], r['status']))]

    return reports


def get_company_report_status(storage, org_id, active_org_id):
    try:
        report = get_latest_company_report_status(storage, org_id, active_org_id)
    except NoReportError:
        pass
    else:
        return report


# Sync functions below
def create_qvarn_company(storage, registration_no, name, country, *,
                         vat_number=None) -> QvarnOrgResultDict:
    country_code = alpha2_country_code(country)

    gov_org_ids: Optional[List[GovOrgId]] = None

    if registration_no:
        gov_org_ids = [{
            'country': country_code,
            'gov_org_id': registration_no,
            'org_id_type': 'registration_number',
        }]

    if vat_number is not None:
        if gov_org_ids is None:
            gov_org_ids = []
        gov_org_ids.append({
            'country': country_code,
            'gov_org_id': vat_number,
            'org_id_type': 'vat_number',
        })

    return create_org(
        storage,
        name=name,
        country_code=country_code,
        gov_org_ids=gov_org_ids
    )


def find_company_by_reg_no(storage, reg_no):
    reg_no = normalize_swedish_id(reg_no)

    return get_org(
        storage,
        gov_org_id=reg_no,
    )


def find_companies_by_reg_no_prefix(storage, reg_no_prefix, country_code=None, limit=10):
    prefix = normalize_whitespace(reg_no_prefix)
    prefix = normalize_dash(prefix)

    params = {'limit': limit}
    if country_code is not None:
        params = {'country': country_code}

        if country_code.lower() == 'se':
            prefix = normalize_swedish_id(prefix)
    params['gov_org_id'] = prefix
    companies = get_orgs_by_gov_org_id(storage, **params)
    # Do not match on org_id_type f-tax. BOL-4576.
    companies_no_f_tax_match = [c for c in companies if _gov_org_id_match_exclude_f_tax(c, prefix)]

    return companies_no_f_tax_match


def _gov_org_id_match_exclude_f_tax(company, prefix):
    gov_org_ids = company['gov_org_ids']
    prefix = prefix.lower()

    for gov_org_id in gov_org_ids:
        id_type = gov_org_id.get('org_id_type')
        id = gov_org_id.get('gov_org_id')

        if id_type in ['registration_number', 'vat_number'] and \
                id and id.lower().startswith(prefix):
            return True
    return False


# TODO: move this to ./qvarn once we have an API endpoint to call in db
# https://jira.tilaajavastuu.fi/browse/BOL-2903
# @storage_dispatch
def get_company_info(storage, company_ids):
    """Get information on companies.

    Parameters:

        `company_ids`: a list of ids.

    <WIP>: TODO: review contract of a future external call

    HTTP response format:
    result={
        'data': [
            {
                'status': OK,
                'response': {}
            },
            {
                'status': ERROR,
                'response': 'error_message'
        ]
    }
    response is a dict of Qvarn-like company dicts like:
    {
        <company_id_1>: {
            'id': '<company_id_1>',
            'gov_org_ids': [
                {
                    'gov_org_id': '<gov_org_id>',
                    'id_type': 'registration_number/vat_number/f-tax',
                    'country': '<country_alpha3>'
                },
                {
                    'gov_org_id': '<gov_org_id>',
                    'id_type': 'registration_number/vat_number/f-tax',
                    'country': '<country_alpha3>'
                },
                <...>
            ]
            'names': [
                '<company name 1>',
                '<company name 2>',
            ]
        },
        <company_id_2>: {},
        <...>
    }

    </WIP>: TODO: review contract of a future external call
    """
    companies = get_orgs(storage, company_ids)

    result = []
    result_ids = []
    for company in companies:
        result.append({
            'id': company['id'],
            'company_id': get_gov_org_id(company),
            'vat_number': get_org_vat(company),
            'country': alpha3_country_code(company['country']),
            'name': get_org_name(company) if 'names' in company else '',
        })
        result_ids.append(company['id'])

    mismatch_company_id = set(company_ids).difference(result_ids)
    if mismatch_company_id:
        logger.warning('Company information not received on requested company_ids: %s',
                       mismatch_company_id)
    return result


def get_one_company_info(storage, company_id):
    if not company_id:
        return {}
    infos = get_company_info(storage, [company_id])
    result = {}
    if infos:
        result = infos[0]
    return result


def create_company_contract_with_active_cs(qvarn, org_id, state):
    if feature_active('core_mitt_id06'):
        logger.warning('credisafe contract is not available on core_mitt_id06 BOL')
        return None
    service_provider = get_service_provider()
    response = qvarn.create('contracts', {
        'type': 'contract',
        'contract_type': REPORT_PROVIDER_CONTRACT_TYPE_CREDITSAFE,
        'contract_parties': [
            {
                'type': 'person',
                'role': 'user',
                'resource_id': org_id,
            }
        ],
        'service_provider': service_provider,
        'contract_state': state
    })

    return response


def sync_qvarn_company(storage, registration_no, name, country, *,
                       vat_number=None, has_active_cs_contract=None):
    try:
        company = find_company_by_reg_no(storage, registration_no)
    except (DataDuplicatesFoundError, DataNotFoundError):
        company = create_qvarn_company(storage, registration_no, name,
                                       country, vat_number=vat_number)
    else:
        delete_org(storage, company['id'])
        company = create_qvarn_company(storage, registration_no, name,
                                       country, vat_number=vat_number)
    return company


def clean_gov_org_id(gov_org_id):
    gov_org_id = normalize_dash(gov_org_id)
    return unidecode(gov_org_id).strip()


def get_company_user_info_by_email(storage, org, email) -> Optional[CompanyUserInfo]:
    if feature_active('core_mitt_id06'):
        return get_company_user_info_by_email_from_core(storage, org['organisation_uuid'], email)
    else:
        return legacy_get_company_user_info_by_email(storage, org['id'], email)


def get_company_user_info_by_email_from_core(storage, org_id, email) -> Optional[CompanyUserInfo]:
    if org_id is None:
        return None
    org_person = search_org_person(storage, org_id, email)
    if org_person is None:
        return None
    user_account = org_person_to_user_account(org_person, org_id)
    company_user_info: CompanyUserInfo = {
        'full_name': org_person.person.full_name,
        'email': cast(str, org_person.professional_email),
        'contract_id': None,
        'person_id': org_person.person.uuid,
        'is_active': True,  # You can only search for active users in Core
        'has_bol_permission': user_account_has_bol_permission(user_account, org_id),
    }
    return company_user_info


def legacy_get_company_user_info_by_email(storage, org_id, email):
    # We may have more than one person with the same email address
    # The code below checks each of the found persons if they have
    # a user account with the service provider and organisation
    # we're searching for.

    person_ids = search_person_ids(storage, email=email)
    for person_id in person_ids:
        try:
            user_account = get_user_account_for_person_and_org(storage, person_id, org_id)
        except (DataNotFoundError, DataDuplicatesFoundError):
            continue
        organisation_person = get_organisation_person(
            storage, org_id, person_id, get_private=True)

        if organisation_person is not None:
            official_name = get_person_official_name(organisation_person['person'])
            is_registered = is_person_registered(organisation_person['person'])
            return {
                'full_name': official_name,
                'email': get_user_email(storage, organisation_person['person']),
                'contract_id': user_account['id'],
                'person_id': person_id,
                'is_active': is_registered,
                'has_bol_permission': user_account_has_bol_permission(user_account, org_id),
            }
    else:
        return None


def get_report_access_ids_within_subscription(request, active_org_id, country,
                                              gov_org_id, archive_id, subscription):
    subscription_time = subscription['terms_of_service'][0]['acceptance_time']
    report_access_ids = get_active_report_access_ids(
        request.storage,
        archive_id,
        active_org_id,
        subscription_time,
        country=country,
        gov_org_id=gov_org_id,
    )
    return report_access_ids


def create_report_access_for_archive_id(request, active_org_id, person_id,
                                        country, gov_org_id, archive_id,
                                        language, template_version=None):
    org_ids = search_orgs(request.storage, gov_org_ids=[gov_org_id], country=country)
    org_id = org_ids[0] if len(org_ids) == 1 else None

    access_time = datetime.datetime.utcnow().replace(microsecond=0).isoformat() + '+00:00'
    gov_org_ids = [{
        'country': country,
        'gov_org_id': gov_org_id,
        'org_id_type': 'registration_number',
    }]

    create_report_access(
        request.qvarn, access_time, archive_id, active_org_id, person_id,
        org_id, gov_org_ids, language, template_version
    )


def get_company_report_by_gov_org_id(request, active_org_id, person_id,
                                     country, gov_org_id, language='FI',
                                     register_access=True):
    try:
        response = company_api.get_latest_report(request.company_api_client,
                                                 active_org_id,
                                                 country,
                                                 gov_org_id,
                                                 language=language,
                                                 register_access=register_access)
    except Exception:
        logger.exception('Failed to get report for %s/%s and language %s',
                         country, gov_org_id, language)
        return None

    archive_id = response['report']['archive_id']

    try:
        response = company_api.get_latest_report_pdf(
            request.company_api_client,
            active_org_id,
            country,
            gov_org_id,
            language,
            register_access=register_access,
        )
    except Exception:
        logger.exception('Failed to get report for %s/%s and language %s',
                         country, gov_org_id, language)
        return None

    if country not in ['FI', 'EE']:
        return response.content

    # If we have a subscription, and there are no report accesses for this report
    # that were accessed _after_ the current subscription, create a new report access
    subscription = get_subscription(request, active_org_id, active_only=True)
    if not subscription:
        return response.content

    if not register_access:
        return response.content

    if not get_report_access_ids_within_subscription(request, active_org_id, country,
                                                     gov_org_id, archive_id, subscription):
        # Try to match to a Qvarn orgs resource
        create_report_access_for_archive_id(request, active_org_id, person_id,
                                            country, gov_org_id, archive_id, language)
    return response.content


def get_company_web_report_by_gov_org_id(request, active_org_id, person_id,
                                         country, gov_org_id, language='FI',
                                         template_version=None,
                                         register_access=True):
    try:
        response = company_api.get_latest_report(request.company_api_client,
                                                 active_org_id, country,
                                                 gov_org_id, language=language,
                                                 template_version=template_version,
                                                 register_access=register_access)
    except Exception:
        logger.exception('Failed to get report for %s/%s and language %s',
                         country, gov_org_id, language)
        return None

    archive_id = response['report'][0]['archive_id']

    if country not in ['FI', 'EE']:
        return response

    # If we have a subscription, and there are no report accesses for this report
    # that were accessed _after_ the current subscription, create a new report access
    subscription = get_subscription(request, active_org_id, active_only=True)
    if not subscription:
        return response

    if not register_access:
        return response

    if not get_report_access_ids_within_subscription(request, active_org_id, country,
                                                     gov_org_id, archive_id, subscription):
        # Try to match to a Qvarn orgs resource
        create_report_access_for_archive_id(request, active_org_id, person_id,
                                            country, gov_org_id, archive_id,
                                            language=language,
                                            template_version=template_version)
    return response
