import functools
import logging
import os
import pathlib
from typing import Dict, List, Optional

from celery.app import shared_task
from celery.signals import worker_process_init, worker_ready, worker_shutdown
from opentelemetry import trace
from requests.exceptions import ReadTimeout

from bolfak.azure_ai import setup_azure_ai, trace_span
from bolfak.clients.bda import setup_bda_client
from bolfak.clients.composite import Storage, setup_storage
from bolfak.clients.contract import setup_contract_client
from bolfak.clients.core import setup_core_sysapi_client
from bolfak.clients.qvarn import setup_qvarn_api
from bolfak.clients.redis import setup_redis_client
from bolfak.clients.sendgrid import setup_sendgrid_client
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.config import get_config, get_visitor_whitelist_org_ids
from bolfak.featureflags import feature_active
from bolfak.services.bulkimport import CeleryBulkImportJob, do_bulk_import, get_bulkimport_job
from bolfak.services.preannouncements_notify import do_send_notifications
from bolfak.services.project_users import do_notify_client_added_to_project
from bolfak.services.statusreports import (
    StatusReportsTasksHelpers,
    filter_emptax_and_ab_companies,
    filter_interested_with_active_bda_cs_account,
    filter_interested_with_active_cs_account,
    filter_interested_with_changed_reports,
    filter_interested_with_cs_monitored_orgs,
    filter_interested_with_foreign_support,
    filter_interested_with_incomplete_reports,
    filter_interested_with_missing_reports,
    filter_interested_with_outdated_reports,
    get_interested_for_all_orgs,
    get_interested_for_org,
    get_interested_for_orgs,
)
from bolfak.statusreports.creditsafe_report_monitoring import CSReportMonitoring
from bolfak.storage.preannouncements import get_preannouncement
from bolfak.storage.qvarn.projects import get_project


logger = logging.getLogger(__name__)


# Each celery worker has its own database connection, reused between tasks,
# initialized by init_worker().
storage: Storage = None  # type: ignore

# Same deal with the sendgrid client
sendgrid_client = None

# Avoid adding the same global log handlers etc. if init_worker() gets retried a few times
azure_init_called = False


# NB: an exception in the worker_process_init handler doesn't abort worker startup, instead
# the worker runs with the globals left uninitialized, causing errors during task handling.
# Thus we retry until all the external service connections succeed.
# (The dispatch_uid is a workaround for a celery bug, needed for our test
# suite, and must match the one used in the worker_process_init.disconnect()
# call in conftest.py)
@worker_process_init.connect(retry=True, dispatch_uid='init_worker')
def init_worker(**kwargs):
    config = get_config()  # should've been initialized by bolfak.celery.InitStep

    global azure_init_called
    if not azure_init_called:
        setup_azure_ai(config, 'bol-celery-worker', script_tracing=False)
        azure_init_called = True

    logging.info('Connecting to Qvarn...')
    # XXX this is a bad reason, but we're migrating away from Qvarn so we don't care
    qvarn = setup_qvarn_api(config, reason_getter=lambda: 'processing background tasks')

    logger.info('Connecting to bol-data-api...')
    bda = setup_bda_client(config)

    logger.info('Connecting to Contract API client...')
    contract_api = setup_contract_client(config)

    logger.info('Connecting to User account API client...')
    user_account_api = setup_user_account_api_client(config)

    if feature_active('core_mitt_id06'):
        logger.info('Connecting to Core SystemAPI...')
        core = setup_core_sysapi_client(config)
    else:
        logger.info('Not connecting to Core SystemAPI - feature disabled')
        core = None

    redis = setup_redis_client(config)

    global storage
    storage = setup_storage(
        qvarn=qvarn,
        bda=bda,
        core=core,
        user_account_api=user_account_api,
        contract_api=contract_api,
        redis=redis
    )

    global sendgrid_client
    sendgrid_client = setup_sendgrid_client(config)


@worker_ready.connect
def on_worker_ready(**kwargs):
    readiness_file = os.getenv('BOL_CELERY_READINESS_FILE')
    if readiness_file:
        pathlib.Path(readiness_file).touch()


@worker_shutdown.connect
def on_worker_shutdown(**kwargs):
    readiness_file = os.getenv('BOL_CELERY_READINESS_FILE')
    if readiness_file:
        pathlib.Path(readiness_file).unlink(missing_ok=True)


def bolfak_task(*args, **kw):
    """Decorator for functions implementing Celery tasks.

    Equivalent to celery.app.shared_task(), and also sets up Azure Application
    Insights tracing.
    """

    def decorator(f):

        @functools.wraps(f)
        def wrapper(*args, **kwargs):
            with trace_span(f.__qualname__, trace.SpanKind.SERVER):
                return f(*args, **kwargs)

        return shared_task(**kw)(wrapper)

    if len(args) == 1 and callable(args[0]):
        return decorator(args[0])
    return decorator


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def bulk_import_task(job_id: str):
    logger.info('Started processing bulk import job %s', job_id)
    config = get_config()
    job = get_bulkimport_job(storage, job_id)
    assert isinstance(job, CeleryBulkImportJob)
    job._update_status(status='in_progress')
    try:
        do_bulk_import(storage, job, config)
    except:  # noqa: yes, I want to explicitly catch ALL exceptions
        job._update_status(status='failed')
        logger.warning('Failed processing bulk import job %s', job_id)
        raise
    else:
        job._update_status(status='done')
        logger.info('Finished processing bulk import job %s', job_id)


@bolfak_task
def send_pa_notifications_task(pa_id: str, notification_type: str):
    pa = get_preannouncement(storage, pa_id)
    do_send_notifications(storage, pa, sendgrid_client, notification_type)


@bolfak_task
def notify_client_added_to_project_task(project_id: str, email: str, preferred_language: str):
    project = get_project(storage, project_id)
    do_notify_client_added_to_project(storage, sendgrid_client, project, email, preferred_language)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_all_statusreports_task():
    """DEPRECATED. Update status reports that are older than 1 day."""
    logger.info('statusreports: Updating all interested companies')
    config = get_config()
    interested = get_interested_for_all_orgs(storage)
    interested = filter_interested_with_outdated_reports(storage,
                                                         interested,
                                                         older_than_days=1)
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    providers += StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update(storage, config, interested, providers)
    logger.info('statusreports: Done updating %s all interested companies;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_incomplete_statusreports_task():
    logger.info('statusreports: Updating interested companies with status=incomplete')
    config = get_config()
    interested = get_interested_for_all_orgs(storage)
    interested = filter_interested_with_incomplete_reports(storage, interested)
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    providers += StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update(storage, config, interested, providers)
    logger.info('statusreports: Done updating %s interested companies with status=incomplete;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_swedish_changed_statusreports_task(last_days: int) -> None:
    """Update status reports that have changed according to Creditsafe SWE."""
    logger.info('statusreports: Updating swedish companies that changed'
                ' during the %s last days', last_days)
    config = get_config()
    visitor_whitelist = get_visitor_whitelist_org_ids(config)
    if visitor_whitelist:
        indiscriminate_suppliers = True
    else:
        indiscriminate_suppliers = False
    # Include visitors for whitelisted interested_orgs when updating Swedish reports
    interested = get_interested_for_all_orgs(
        storage, indiscriminate_suppliers=indiscriminate_suppliers)
    interested = filter_interested_with_active_cs_account(storage, interested)
    interested = filter_interested_with_active_bda_cs_account(storage, interested)
    interested = filter_interested_with_cs_monitored_orgs(config,
                                                          storage,
                                                          interested)
    interested = filter_interested_with_changed_reports(config,
                                                        storage,
                                                        interested,
                                                        last_days=last_days)
    providers = StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update(storage, config, interested, providers)
    logger.info('statusreports: Done updating changed swedish reports for %s interested companies;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_missing_statusreports_task():
    logger.info('statusreports: Updating companies with missing reports')
    config = get_config()
    interested = get_interested_for_all_orgs(storage)
    interested = filter_interested_with_missing_reports(storage, interested)
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    providers += StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update(storage, config, interested, providers)
    logger.info('statusreports: Done updating %s interested companies with missing reports;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_outdated_foreign_statusreports_task(older_than_days=None):
    """Update foreign status reports if they are older than days_ago from Creditsafe."""
    logger.info('statusreports: Updating outdated foreign reports for companies')
    config = get_config()
    interested = get_interested_for_all_orgs(storage)
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    interested = filter_interested_with_foreign_support(config,
                                                        storage,
                                                        interested,
                                                        providers)
    interested = filter_interested_with_active_cs_account(storage, interested)
    interested = filter_interested_with_outdated_reports(storage,
                                                         interested,
                                                         older_than_days=older_than_days)
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    stats = StatusReportsTasksHelpers.update(
        storage,
        config,
        interested,
        providers,
        older_than_days=older_than_days,
    )
    logger.info('statusreports: Done updating %s outdated foreign reports for interested companies;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_cs_swe_statusreports_task(org_ids: List[str]):
    logger.info('statusreports: Updating CS SWE reports for given orgs')
    config = get_config()
    providers = StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update_statusreports_for_orgs(
        storage, storage.contract_api, get_config(), org_ids, providers, interested_org_id=None,
        filter_with_cs_monitored_orgs=True)
    logger.info('statusreports: Done updating CS SWE reports for %s given companies;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_cs_swe_batched_statusreports_task(org_ids: List[str], batch_idx, total_batches):
    logger.info('statusreports: Updating CS SWE reports for given orgs in batches')
    config = get_config()
    providers = StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update_statusreports_for_orgs(
        storage,
        storage.contract_api,
        get_config(),
        org_ids,
        providers,
    )
    logger.info('statusreports: Done updating CS SWE reports for %s given companies'
                ' (batch %s of %s); updated %s out of %s total reports in the batch',
                stats.total_companies, batch_idx, total_batches,
                stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_statusreports_orgs_task(
    org_ids: List[str],
    interested_org_id: Optional[str] = None,
    skip_existing_reports: bool = False,
    older_than_days: Optional[int] = None,
):
    """Update status reports for given org ids."""
    logger.info('statusreports: Updating status reports for given org ids')
    config = get_config()
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    providers += StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update_statusreports_for_orgs(
        storage, storage.contract_api, get_config(), org_ids, providers,
        interested_org_id=interested_org_id,
        skip_existing_reports=skip_existing_reports,
        older_than_days=older_than_days,
    )
    logger.info('statusreports: Done updating %s reports for given org ids;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_statusreports_orgs_file_task(org_ids: List[str], interested_org_id=None):
    """Update status reports for given org ids from file"""
    logger.info('statusreports: Updating status reports for given org ids from file')
    config = get_config()
    providers = StatusReportsTasksHelpers.get_foreign_providers(config)
    providers += StatusReportsTasksHelpers.get_swedish_providers(config)
    stats = StatusReportsTasksHelpers.update_statusreports_for_orgs(
        storage, storage.contract_api, get_config(), org_ids, providers,
        interested_org_id=interested_org_id,
    )
    logger.info('statusreports: Done updating %s reports for given org ids from file;'
                ' updated %s out of %s total reports',
                stats.total_companies, stats.saved_reports, stats.total_reports)


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def add_to_monitoring_task(org_ids: List[str], interested_org_id: str):
    logger.info('Extra logs: getting interested mapping to orgs...')
    if interested_org_id:
        interested = get_interested_for_org(storage, interested_org_id, org_ids)
    else:
        interested = get_interested_for_orgs(storage, org_ids)
    total = sum(map(len, interested.values()))
    logger.info('Computed total %d orgs for %d interested companies', total, len(interested))
    if not total:
        logger.info('Not adding any companies to monitoring')
        return
    logger.info('Adding companies to monitoring')
    monitoring = CSReportMonitoring(storage)
    result = monitoring.add_orgs_to_monitored_list(interested)
    logger.info('Done adding companies to monitoring.\n'
                'Added companies: %d, failed to add companies: %d, skipped: %d.',
                len(result.succeeded), len(result.failed), len(result.skipped))


@bolfak_task
def remove_from_monitoring_task(org_ids: List[str], interested_org_id: str):
    if not interested_org_id:
        logger.warning('Remove from monitoring must have interested_org_id')
        return
    interested = {interested_org_id: org_ids}
    logger.info('Computed total %d orgs for %d interested companies', len(org_ids), 1)
    logger.info('Removing companies from monitoring')
    monitoring = CSReportMonitoring(storage)
    result = monitoring.remove_orgs_from_monitored_list(interested)
    logger.info('Done removing companies from monitoring.\n'
                'Removed companies: %d, failed to remove companies: %d, skipped: %d.',
                len(result.succeeded), len(result.failed), len(result.skipped))


@bolfak_task(acks_late=True, task_reject_on_worker_lost=True,
             autoretry_for=(ReadTimeout,), max_retries=3, retry_backoff=True)
def update_cs_swe_statusreports_with_emptax_task(interested: Dict[str, str],
                                                 batch_idx: int, batches_total: int):
    logger.info('statusreports: Updating CS ab/emptax reports task'
                f' (batch {batch_idx} out of {batches_total})')

    config = get_config()
    info_provider = StatusReportsTasksHelpers.get_info_provider(config)

    interested_filtered = {}
    orgs_ids_to_process_nr = 0
    for interested_org_id, org_ids in interested.items():
        filtered_org_ids = filter_emptax_and_ab_companies(
            storage=storage,
            org_ids=org_ids,
            config=config,
            info_provider=info_provider,
            interested_org_id=interested_org_id,
        )
        interested_filtered[interested_org_id] = filtered_org_ids
        orgs_ids_to_process_nr += len(filtered_org_ids)

    logger.info('statusreports: UpdatingCS ab/emptax reports task '
                f'will process a total of {len(interested_filtered)} interested_org_ids '
                f'with combined total of {orgs_ids_to_process_nr} org_ids')

    providers = StatusReportsTasksHelpers.get_swedish_providers(config)
    StatusReportsTasksHelpers.update(
        storage,
        config,
        interested_filtered,
        providers,
    )
    logger.info('statusreports: Done updating CS ab/emptax reports task'
                f' (batch {batch_idx} out of {batches_total})')
