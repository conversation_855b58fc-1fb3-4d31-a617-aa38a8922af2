import copy
import datetime
import difflib
import functools
import json
import logging.config
import os
import sys
from typing import Iterator, List, Optional, Tuple
from unittest import mock

import bottle
import celery.app
import dateutil.parser
import pytest
import requests
import requests_mock
import vcr as vcrpy
import webtest
from celery.signals import worker_process_init
from Crypto.PublicKey import RSA
from freezegun.api import FakeDate, FakeDatetime, FrozenDateTimeFactory, convert_to_timezone_naive
from lxml import etree
from requests_mock import Mocker
from vcr.util import read_body

import bolfak.application
import bolfak.config
from bolfak.application import application, setup_application
from bolfak.celery import app as bolfak_celery
from bolfak.celery.tasks import init_worker
from bolfak.clients.autoaccount import _ResultWrapper, setup_autoaccount_client
from bolfak.clients.bda import BdaClient
from bolfak.clients.composite import Storage
from bolfak.clients.contract import setup_contract_client
from bolfak.clients.core import CoreSystemAPIClient, get_core_base_url
from bolfak.clients.sendgrid import setup_sendgrid_client
from bolfak.clients.stamp import setup_stamp_client
from bolfak.clients.user_account_api import setup_user_account_api_client
from bolfak.featureflags import feature_active
from bolfak.fixtures import factories
from bolfak.i18n import get_i18n_plugin
from bolfak.models import (
    STATUS_OK,
    STATUS_STOP,
    SUPERVISOR_ROLE,
    USER_ROLE_MAIN,
    AdditionalInfo,
    AuthInfo,
    CorePerson,
    CoreOrganisation,
    OrgInfo,
    OrganisationPerson,
    OrganisationRecord,
    TopMenuParameters,
    UserProfile,
    UserRole,
)
from bolfak.services.cryptography import symmetric_encrypt
from bolfak.services.statusreports import (
    JOB_MODE_ALL,
    JOB_MODE_CHANGED,
    JOB_MODE_CS_SWE,
    JOB_MODE_CS_SWE_BATCHED,
    JOB_MODE_INCOMPLETE,
    JOB_MODE_MISSING,
    JOB_MODE_OUTDATED,
)
from bolfak.services.user_accounts import get_user_account_person_id
from bolfak.statusreports.connect import ConnectReportProvider
from bolfak.statusreports.creditsafe import Creditsafe, CreditsafeInfoProvider, CreditsafeV2
from bolfak.storage.qvarn.projects import delete_project
from bolfak.testing import utils
from bolfak.testing.bda import BolDataApi, get_bda_dsn
from bolfak.testing.company_api import get_companies_by_fetched_reports, get_latest_report
from bolfak.testing.contract_api import ContractApi
from bolfak.testing.lima_api import get_license
from bolfak.testing.qvarn.base import QvarnIdenticalApi, get_qvarn_api_client, parse_data_api_params
from bolfak.testing.qvarn.realqvarn import RealQvarn
from bolfak.testing.services import ensure_core_sysapi_service, ensure_postgresql_service
from bolfak.testing.statusreports import get_creditsafe_response_v2, get_provider_config
from bolfak.testing.user_account_api import UserAccountApi
from fixtures.core_mock import MOCK_ORGS


# qvarn messes up logging configuration as an import-time side effect, evil!!!
if True:  # prevent isort from reordering these imports
    from qvarn.testing import get_jwt_token
logging.getLogger().handlers = []


# https://docs.celeryproject.org/en/stable/userguide/testing.html#pytest-plugin
pytest_plugins = ("celery.contrib.pytest", )


logging.config.dictConfig({
    'version': 1,
    'incremental': True,
    'root': {
        # explicit is better than implicit
        'level': logging.WARNING,
    },
    'loggers': {
        __name__: {
            'level': logging.INFO,
        },
        'suds': {
            'level': logging.INFO,
        },
        'suds.umx.typed': {
            # I don't know what suds.umx.typed: WARNING: attribute (segment-code) type, not-found
            # means, but we get quite a few!
            'level': logging.ERROR,
        },
        'qvarnclient.qvarnapi': {
            # shut up resource creation log entries, they make test failures unbearable
            'level': logging.WARNING,
        },
        'bolfak.services.resources': {
            # shut up bol_supplier autodetection log entries
            'level': logging.WARNING,
        },
        'rq.worker': {
            'level': logging.WARNING,
        },
    },
})


logger = logging.getLogger(__name__)


MAIN_ORG_REG_NO = 'TC-MAIN'
ADMIN_ORG_REG_NO = 'TC-ADMIN'


# We have a couple of encrypted blobs in test_creditsafe_monitoring.py that rely on this
# being 'secret_terces'.
CS_ACCOUNT_STORAGE_SECRET_KEY = 'secret_terces'


CONFIG = {
    'main': {
        'base_path': '/api',
        'static_path': '/api/static',
        'host': '0.0.0.0',
        'port': '7002',
        'insecure_testdata_api': True,
        'insecure_test': True,
        'frontend_url': 'http://test/',
        'standalone_url': 'http://standalone-test/',
        'backend_url': 'http://test/api/',
        'service_portal_url': 'http://service_portal/',
        'service_provider': 'id06',
        'service_key': 'report_pro',
        'content_config_name': 'id06-fs3.yaml',
        'person_org_contract_type': 'tilaajavastuu_account',
        'org_contract_type': 'tilaajavastuu_subscription',
        'default_storage': 'qvarn',
        'locale_dir': 'locale/id06',
        'swagger_api': True,
        'company_registry_url': 'http://companyapi.example.com/api/',
        'companies_url': 'https://companies-example.id06.se/',
        'change_user_details_url': 'https://spalpha.id06.se/#/profile/basic-details',
    },
    'auth': {
        'enabled': 'false',
    },
    'bol-data-api': {
        'base_url': 'https://bol-data-api/',
        'client_id': 'test_client_id',
        'client_secret': 'verysecret',
    },
    'user-account-api': {
        'base_url': 'https://user-account-api/',
        'client_id': 'test_client_id',
        'client_secret': 'verysecret',
        'verify_requests': 'false',
        'scope': ', '.join([
            'user_account_read',
            'user_account_create',
            'user_account_delete',
            'user_account_update',
        ]),
    },
    'celery': {
    },
    'stamp': {
        'base_url': 'https://stampdata-example.org',
        'verify_requests': True,
        'scopes': ', '.join([
            'stampdata_companies_whitelist_get',
            'stampdata_events_post',
            'stampdata_event_batches_id_status_get',
            'stampdata_visitor_companies_post',
        ]),
    },
    'contract': {
        'base_url': 'https://contract-example.org',
        'verify_requests': True,
        'scopes': ', '.join([
            'contract_creditsafe_account_read',
            'contract_creditsafe_account_create',
            'contract_creditsafe_account_delete',
            'contract_creditsafe_account_update',
        ]),
    },
    'company-qvarn-poc': {
        'base_url': 'https://cqpoc.example.com',
        'client_id': 'test_client_id',
        'client_secret': 'verysecret',
        'verify_requests': True,
        'scope': ', '.join([
            'uapi_orgs_multiple_post',
        ])
    },
    'core-system-api': {
        'base_url': get_core_base_url(),
        'client_id': 'test_client_id',
        'client_secret': 'verysecret',
        'verify_requests': True,
        'scope': ', '.join([])
    },
    'qvarn': {
        'verify_requests': 'false',
        'base_url': 'https://qvarn.example.com',
        'client_id': 'test_client_id',
        'client_secret': 'verysecret',
        'scope': 'scope1,scope2,scope3',
        'extended_project_fields': 'true',
        'url_for_orgs': 'https://qvarnlike-orgs.example',
        'url_for_cards': 'https://qvarnlike-cards.example',
        'url_for_persons': 'https://qvarnlike-persons.example',
        'url_for_bol_suppliers': 'https://qvarnlike-suppliers.example',
        'url_for_projects': 'https://qvarnlike-projects.example',
        'url_for_reports': 'https://qvarnlike-reports.example',
    },
    'gluu': {
        'base_url': 'https://gluu.example.com',
        'end_session_support': 'false',
    },
    'sessions': {
        'type': 'memory',
        'cookie_name': 'session',
        'cookie_domain': 'localhost',
        'cookie_path': '/api/',
        'httponly': 'true',
        'secure': 'false',
    },
    'statusreports': {
        'bulkimport_data_provider': 'bolfak.statusreports.sandbox.SandboxInfoProvider',
        'report_swedish_data_provider': 'bolfak.statusreports.noop.NoopProvider',
        'report_foreign_data_provider': 'bolfak.statusreports.noop.NoopProvider'
    },
    'statusreports.sandbox': {
    },
    'statusreports.bisnode': {
        'user_id': '',
        'user_password': '',
        'customer_code': '',
        'customer_code_owner': '',
        'language': '',
        'from_country': '',
        'to_country': '',
    },
    'statusreports.skatteverket': {
    },
    'statusreports.creditsafe': {
        'symmetric_secret_key': CS_ACCOUNT_STORAGE_SECRET_KEY,
    },
    'feature-flags': {
        'import_sole_traders': False,
        'pre_announcements': False,
        'contract_api_creditsafe_contract': True,
        'create_and_activate_cs_accounts': False,
    },
    'sendgrid': {
        'sendgrid_sender': '',
        'sendgrid_api_key': '',
    },
    'autoaccount': {
        'autoaccount_use_testing_org_gov_org_id': True,
        'autoaccount_url': '',
        'autoaccount_username': '',
        'autoaccount_password': '',
        'autoaccount_email': '<EMAIL>',
        'autoaccount_request_package': '',
        'autoaccount_testing_org_gov_org_id': '**********',
        'autoaccount_id06_gov_org_id': '',
    },
    'monitoring': {
        'visitor_whitelist_org_ids': ''
    }
}


class MockResponse:
    def __init__(self, json):
        self._json = json

    @property
    def text(self):
        return json.dumps(self._json)

    def json(self):
        return self._json


def _csrf_session_hook():
    if not bottle.request.environ.get('HTTP_X_TEST_SKIP_CSRF_HELPERS'):
        bottle.request.environ['beaker.session'].update({
            'csrf_token': 'VaLiD-cSrF-tOkEn',
        })


@pytest.fixture(scope='session', autouse=True)
def neutralize_azure_ai():
    # AzureBottle() as well as our own setup_azure_ai() pay attention to the
    # APPLICATIONINSIGHTS_CONNECTION_STRING envvar.  We don't want to
    # accidentally start submitting logs to Azure Application Insights if
    # anyone runs the test suite while having that environment variable set.
    with pytest.MonkeyPatch.context() as mp:
        if 'APPLICATIONINSIGHTS_CONNECTION_STRING' in os.environ:
            mp.delenv('APPLICATIONINSIGHTS_CONNECTION_STRING')
        yield


@pytest.fixture
def config():
    bolfak.config.set_config(_override_config(CONFIG, {'bottle.catchall': False}))
    yield bolfak.config.get_config()
    bolfak.config.reset_config()


@pytest.fixture
def config_locale_id06(config):
    config.set('main', 'locale_dir', 'locale/id06')


@pytest.fixture
def config_locale_vg(config):
    config.set('main', 'locale_dir', 'locale/vg')


@pytest.fixture
def i18n_reset():
    get_i18n_plugin(reset=True)


class FakeADM(object):

    user_info = None
    person_id = None

    force_cookie_language_over_profile = False
    ui_language_changed_recently = None

    def __init__(self, *, shared_cookie_domain, cookie_secure, frontend_url, **kw):
        self.shared_cookie_domain = shared_cookie_domain
        self.cookie_secure = cookie_secure
        self.frontend_url = frontend_url
        self.default_org_for_current_session = None
        self.card_supplier_context_enabled = False

    def clear_cache(self):
        pass

    def get_represented_org_info(self, org_id):
        pass


def mock_login(storage, mocker, org, *, person='Thomas Anderson', person_email=None,
               admin=False, role=USER_ROLE_MAIN):
    user_contract = None
    if org:
        if isinstance(org, str):
            create_organisation = feature_active('core_mitt_id06')
            org = factories.get_or_create_org(storage, org, create_organisation=create_organisation)
        if isinstance(person, str):
            if not person_email:
                person_email = person.lower().replace(' ', '.') + '@example.com'
            person = factories.get_or_create_person(storage, person, email=person_email,
                                                    org_uuid=org.get('organisation_uuid'))
        user_contract = factories.get_or_create_user_account(
            storage,
            person,
            org,
            email=person_email
        )
        profile = factories.create_mock_user_profile(org, person, admin=admin, user_role=role)
        mocker.patch('bolfak.services.authentication.get_authenticated_person_id',
                     return_value=person['id'])
        mocker.patch('bolfak.services.authentication.get_active_org_id_from_request',
                     return_value=org['id'])
        person_id = person['id']
    else:
        profile = None
        person_id = None
    mocker.patch('bolfak.services.authentication.get_authenticated_user', return_value=profile)
    mocker.patch.dict('bolfak.services.roles.Role.DEPENDENCIES', {
        'profile': mock.Mock(return_value=profile),
        'active_person_id': mock.Mock(return_value=person_id),
    })

    if feature_active('core_mitt_id06'):
        user_contract['person_id'] = person_id

    return user_contract


@pytest.fixture
def login_as(storage, mocker, config):
    def _login_fn(org, person='Test McTestFace', admin=False, role=USER_ROLE_MAIN):
        return mock_login(storage, mocker, org, person=person, admin=admin, role=role)
    return _login_fn


class TestApp(webtest.TestApp):

    def __init__(self, storage, mocker, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.storage = storage
        self.qvarn = storage.qvarn
        self.mocker = mocker
        self.active_org_id = None
        self.active_person_id = None

    def login(self, org, person='Thomas Anderson', *, admin=False, person_email=None,
              role=USER_ROLE_MAIN):
        if isinstance(org, str):
            create_organisation = feature_active('core_mitt_id06')
            org = factories.get_or_create_org(self.storage, org,
                                              create_organisation=create_organisation)
        self.active_org_id = org['id']
        contract = mock_login(self.storage, self.mocker, org, person=person,
                              person_email=person_email, admin=admin, role=role)
        self.active_person_id = get_user_account_person_id(contract)
        bottle.request.session = {
            'id_token': utils.generate_dummy_jwt_token(),
            'access_token': utils.generate_dummy_jwt_token(),
        }
        return contract

    def _generate_org_url(self, url, org_id=None):
        """Takes an URL of style /api/some-endpoint and converts it to
        /api/org_id/some-endoint.
        """
        if org_id is None:
            org_id = self.active_org_id
        if org_id is None:
            raise ValueError('Cannot generate org url without signed-in user or org_id')

        if not url.startswith('/api/'):
            raise ValueError('Cannot handle URLs that do not start with /api/')
        url_suffix = url[len('/api/'):]
        return '/api/%s/%s' % (org_id, url_suffix)

    def get_with_org(self, url, *args, org_id=None, **kwargs):
        url = self._generate_org_url(url, org_id=org_id)
        return self.get(url, *args, **kwargs)

    def post_with_org(self, url, *args, org_id=None, **kwargs):
        url = self._generate_org_url(url, org_id=org_id)
        return self.post(url, *args, **kwargs)

    def post_json_with_org(self, url, *args, org_id=None, **kwargs):
        url = self._generate_org_url(url, org_id=org_id)
        return self.post_json(url, *args, **kwargs)

    def logout(self):
        self.mocker.patch('bolfak.services.authentication.get_authenticated_user',
                          return_value=None)


def _App(request, config, mocker, storage,
         sendgrid_client, contract_client_empty=None, autoaccount_client_empty=None):
    auth_info_ret_val = {
        'additionalInfo': {
            'represented_org_info': {},
        },
        'userProfile': {'personId': '1234', 'phoneNumber': '123456'},
        'otherUiInfo': {
            'isSignedIn': False,
            'isAdmin': False,
        }
    }
    mocker.patch('bolfak.application.setup_qvarn_api',
                 return_value=storage.qvarn)
    mocker.patch('bolfak.application.serve_auth_info',
                 return_value=auth_info_ret_val)
    mocker.patch('bolfak.application.setup_sendgrid_client',
                 return_value=sendgrid_client)
    mocker.patch('bolfak.application.setup_contract_client',
                 return_value=contract_client_empty)
    if autoaccount_client_empty is not None:
        mocker.patch('bolfak.application.setup_autoaccount_client',
                     return_value=autoaccount_client_empty)

    def factory(config_overrides=None, additional_app_hooks=None):
        if additional_app_hooks is None:
            additional_app_hooks = []
        config.read_dict(_override_config({}, config_overrides))

        app_hooks = [
            ('before_request', _csrf_session_hook),
        ]
        setup_application(
            app_hooks=app_hooks + additional_app_hooks,
            bda=storage.bda,
        )
        app = TestApp(storage, mocker, application, extra_environ={
            'HTTP_X_CSRF_TOKEN': 'VaLiD-cSrF-tOkEn',
        })
        # Always run tests under English language.
        app.set_cookie('used_ui_language', 'en')
        return app

    def finalize():
        # Reset bottle request.
        bottle.request = bottle.LocalRequest()

    request.addfinalizer(finalize)

    return factory


@pytest.fixture
def App(request, config, mocker, storage, sendgrid_client, contract_client):
    return _App(request, config, mocker, storage, sendgrid_client, contract_client())


@pytest.fixture
def AppNoCScontract(request, config, mocker, storage, sendgrid_client, contract_client_empty):
    return _App(request, config, mocker, storage, sendgrid_client, contract_client_empty)


@pytest.fixture
def AppWithRealQvarn(request, config, mocker, storage_realqvarn,
                     sendgrid_client, contract_client):
    return _App(request, config, mocker, storage_realqvarn, sendgrid_client, contract_client())


@pytest.fixture
def AppWithPA(request, config, mocker, storage, set_feature_flags,
              sendgrid_client, contract_client):
    set_feature_flags({'pre_announcements': True})

    return _App(request, config, mocker, storage, sendgrid_client, contract_client())


@pytest.fixture
def AppWithFeatureFlags(
    request,
    config,
    mocker,
    storage,
    set_feature_flags,
    sendgrid_client,
    contract_client,
):
    def factory(feature_flags):
        set_feature_flags(feature_flags)
        return _App(
            request, config, mocker, storage, sendgrid_client, contract_client()
        )()

    return factory


@pytest.fixture
def AppWithAutoaccount(request, config, mocker, storage, set_feature_flags,
                       sendgrid_client, contract_client, autoaccount_client_empty):
    set_feature_flags({'create_and_activate_cs_accounts': True})

    return _App(request, config, mocker, storage,
                sendgrid_client, contract_client(), autoaccount_client_empty)


@pytest.fixture
def AppWithCore(request,
                config,
                mocker,
                storage,
                set_feature_flags,
                set_feature_flags_for_bda,
                sendgrid_client):
    set_feature_flags({'core_mitt_id06': True, 'pre_announcements': True})
    set_feature_flags_for_bda({'core': True})

    return _App(request, config, mocker, storage, sendgrid_client)


def _app(App, storage):
    application = App()
    core_enabled = feature_active('core_mitt_id06')
    org = factories.get_or_create_org(storage, 'Trafikverket',
                                      create_organisation=core_enabled)
    application.login(org)
    return application


@pytest.fixture
def app(App, storage, config_locale_id06, i18n_reset):
    return _app(App, storage)


@pytest.fixture
def app_with_pa(AppWithPA, storage):
    return _app(AppWithPA, storage)


@pytest.fixture
def app_with_core(AppWithCore, storage):
    return _app(AppWithCore, storage)


@pytest.fixture(params=[False, True], ids=['app_without_core', 'app_with_core'])
def app_with_and_without_core(app, app_with_core, request, set_feature_flags,
                              set_feature_flags_for_bda):
    set_feature_flags({'core_mitt_id06': request.param})
    set_feature_flags_for_bda({'core': request.param})

    return app_with_core if request.param else app


@pytest.fixture
def app_with_auto(AppWithAutoaccount, storage):
    return _app(AppWithAutoaccount, storage)


@pytest.fixture
def app_realqvarn(AppWithRealQvarn, realqvarn, storage):
    storage.qvarn = realqvarn
    return _app(AppWithRealQvarn, storage)


@pytest.fixture
def app_no_cs_contract(AppNoCScontract, storage):
    return _app(AppNoCScontract, storage)


@pytest.fixture(autouse=True)
def lima_mocks(mocker):
    # disable lima api client setup as we will override its interface functions
    # with static responses
    mocker.patch('bolfak.application.setup_lima_api_client', return_value=None)

    # mock lima_api interface functions to some static responses
    mocker.patch('bolfak.clients.lima_api.get_license',
                 return_value=get_license())


@pytest.fixture(autouse=True)
def company_api_mocks(mocker):
    # disable company api client setup as we will override its interface functions
    # with static responses
    mocker.patch('bolfak.application.setup_company_api_client', return_value=None)

    # mock company_api interface functions to some static responses
    mocker.patch('bolfak.clients.company_api.get_latest_report',
                 return_value=get_latest_report())
    mocker.patch('bolfak.clients.company_api.get_companies_by_fetched_reports',
                 return_value=get_companies_by_fetched_reports())


@pytest.fixture
def app_bolfin(App, storage, set_feature_flags, mocker, config_locale_vg, i18n_reset):
    set_feature_flags({'archived_reports': True})
    return _app(App, storage)


@pytest.fixture
def set_feature_flags(config):
    def factory(flags, reset=False):
        if reset:
            config['feature-flags'].clear()
        config.read_dict({'feature-flags': flags})
    return factory


@pytest.fixture
def set_feature_flags_for_bda(bda_server: BolDataApi):
    old_config = bda_server.get_config() or {}

    def factory(flags):
        new_config = _override_config(old_config, {'feature-flags': flags})
        bda_server.set_config(new_config)

    yield factory
    bda_server.set_config(old_config)


@pytest.fixture
def feature_flag(request, config) -> None:
    """Helper fixture for @pytest.mark.feature_flags(...)

    Usage:

        @pytest.mark.feature_flags(
            # Enable 'bda_company_list' and disable 'pagination'.
            '+bda_company_list,-pagination',
            # Disable 'pagination'.
            '-pagination',
            # Do not set any feature flags.
            '',
        )
        def test(feature_flag):
            ...

    '+' is optional and if feature flag does not have + or -, then
    default is to enable given feature flag.

    """
    flags = {}
    given_flags: str = request.param
    value_symbols = {
        '+': True,
        '-': False,
    }
    for flag in given_flags.split(','):
        flag = flag.strip()
        if flag == '':
            continue
        if flag.startswith(('+', '-')):
            value = flag[0]
            flag = flag[1:]
        else:
            value = '+'
        flags[flag] = value_symbols[value]
    config.update({'feature-flags': flags})


@pytest.fixture
def pagination_off(set_feature_flags):
    set_feature_flags({'extended_report': True})


@pytest.fixture
def ff_add_project_client_on(set_feature_flags):
    set_feature_flags({'add_project_client': True})


@pytest.fixture
def ff_block_project_client_on(set_feature_flags):
    set_feature_flags({'block_project_client': True})


@pytest.fixture
def ff_visitors_on(set_feature_flags):
    set_feature_flags({'visitors': True})


@pytest.fixture
def core_mitt_id06(set_feature_flags, set_feature_flags_for_bda):
    set_feature_flags({'core_mitt_id06': True})
    set_feature_flags_for_bda({
        'core': True,
    })


@pytest.fixture
def mitt_id06_authorized_request(core_mitt_id06, mocker):
    bottle_mock = mocker.patch('bolfak.services.authentication.bottle')
    session = dict()
    bottle_mock.request.session = session
    session['auth_info'] = AuthInfo(
        user_profile=UserProfile(
            is_admin=False,
            person_id='person-uuid',
            preferred_language_code='sv',
            first_name='Test',
            last_name='User',
            full_name='Test User',
            email='<EMAIL>',
            phone='123456',
            representations=[],
        ),
        additional_info=AdditionalInfo(
            represented_org_info=OrgInfo(
                organisation_id='org-uuid',
                organisation_name='Best service',
                global_permissions=[],
                user_role=UserRole.BASIC_USER,
                has_active_creditsafe_account=True,
                has_pending_creditsafe_account=False,
                has_active_bol_product=True,
            )
        ),
        top_menu_parameters=TopMenuParameters(
            selected_represented_organization_id='org-uuid',
            all_representation_codes=['org-uuid']
        ),
        selected_org_id='org-record-uuid'
    )
    # Simulate before request_hook
    bottle_mock.request.auth_info = session['auth_info']
    yield bottle_mock.request


def _override_config(config, overrides=None):
    """A nice helper that helps to override config values.

    Returns:
        A deep copy of ``config`` with overriden values taken from ``overrides``.

    Example:

        Override existing value:

            >>> config = {'a': {'b': 1}}
            >>> _override_config(config, {'a.b': 2})
            {'a': {'b': 2}}

        Override missing value:

            >>> config = {}
            >>> _override_config(config, {'a.b': 3})
            {'a': {'b': 3}}

    """
    overrides = overrides or {}
    config = copy.deepcopy(config)
    for keys, value in overrides.items():
        node = config
        keys = keys.split('.')
        for k in keys[:-1]:
            if k not in node:
                node[k] = {}
            node = node[k]
        node[keys[-1]] = value
    return config


@pytest.fixture(scope='session')
def session_mock_requests():
    # We add real_http=True in order to be able to have nested mockers
    # or to pass requests to things like VCR.
    with requests_mock.Mocker(real_http=True, case_sensitive=True) as m:
        yield m


@pytest.fixture
def mock_requests(session_mock_requests):
    # NB: the default value of case_sensitive=False lowercases all query strings
    # breaking our mock implementation of UserAccountApi.
    with requests_mock.Mocker(real_http=True, case_sensitive=True) as m:
        yield m


@pytest.fixture
def admin_user(app, storage):
    org = factories.get_or_create_org(storage, 'Trafikverket')
    return app.login(org, admin=True)


@pytest.fixture(scope='session')
def pretender(request, session_mock_requests: Mocker) -> Iterator[QvarnIdenticalApi]:
    # TODO: `pretender` should be renamed to `qvarnapi`
    bda_dsn = get_bda_dsn(request.config, os.environ)
    qvarn_dsn = request.config.getoption('--qvarn-api')
    qvarn_dsn_params = parse_data_api_params(qvarn_dsn)
    qvarn_api_client_getter = functools.partial(
        get_qvarn_api_client,
        qvarn_dsn_params,
        session_mock_requests,
        'https://qvarn',
        bda_dsn=bda_dsn,
    )
    if qvarn_dsn_params.engine == 'postgresql':
        with ensure_postgresql_service(str(qvarn_dsn_params.drop_fixture_prefix())):
            yield qvarn_api_client_getter()
    else:
        yield qvarn_api_client_getter()


@pytest.fixture
def qvarn(pretender: QvarnIdenticalApi):
    pretender.wipe()
    yield pretender.qvarn


@pytest.fixture(scope='session')
def bda_server(request, session_mock_requests: Mocker):
    base_url = 'https://bol-data-api'
    bda_dsn = get_bda_dsn(request.config, os.environ)
    with ensure_postgresql_service(bda_dsn, env_override_var_name='BOL_TEST_BDA_DSN'):
        yield BolDataApi(session_mock_requests, base_url, bda_dsn)


@pytest.fixture(scope='session')
def rsa_key():
    return RSA.generate(1024)


@pytest.fixture
def bda(bda_server: BolDataApi, rsa_key):
    bda_server.wipe()
    return _make_bda_client(bda_server, rsa_key)


@pytest.fixture(scope='session')
def _session_bda(bda_server: BolDataApi, rsa_key):
    return _make_bda_client(bda_server, rsa_key)


def _make_bda_client(bda_server: BolDataApi, rsa_key):
    issuer = ''
    scopes: List[str] = []
    token = get_jwt_token(rsa_key, issuer, scopes)

    client_id = ''
    client_secret = ''
    client = BdaClient(
        auth_url=bda_server.base_url,
        base_url=bda_server.base_url,
        client_id=client_id,
        client_secret=client_secret,
        scopes=scopes,
        verify_requests=False,
        max_workers=1,
        token=token,
    )

    return client


@pytest.fixture(scope='session')
def core_server():
    with ensure_core_sysapi_service():
        yield


@pytest.fixture
def core(core_server, rsa_key) -> CoreSystemAPIClient:
    base_url = get_core_base_url()
    return _make_core_client(base_url, rsa_key)


def _make_core_client(base_url, rsa_key):
    issuer = ''
    scopes: List[str] = []
    token = get_jwt_token(rsa_key, issuer, scopes)

    client = CoreSystemAPIClient(
        base_url=base_url,
        verify_requests=False,
        token=token,
    )
    return client


@pytest.fixture
def get_fixture_path():
    return utils.get_fixture_path


def _load_test_fixture(fixture_file_name, binary=False, encoding='utf-8'):
    fixture_path = utils.get_fixture_path(fixture_file_name)
    if binary:
        mode = 'rb'
        encoding = None
    else:
        mode = 'r'
    with open(fixture_path, mode, encoding=encoding) as f:
        return f.read()


@pytest.fixture
def load_test_fixture():
    return _load_test_fixture


@pytest.fixture
def patch_gluu(mock_requests):
    # This is needed for /api/status:
    mock_requests.post('/oxauth/restv1/token', json={'access_token': 'token'})
    # This is needed for user management, which is going away in the future:
    mock_requests.post('/oxauth/permission_registration.jsp', json={'ticket': 'ticket'})
    mock_requests.post('/oxauth/requester_permission.jsp', json={'rpt': 'rpt'})
    mock_requests.post('/identity/restv1/scim/v2/Users', json={'id': '1'})
    mock_requests.get('/identity/restv1/scim/v2/Users', json={'resources': []})


@pytest.fixture
def failing_gluu(mock_requests):
    mock_requests.post('/oxauth/restv1/token',
                       exc=requests.exceptions.ConnectTimeout)


def pytest_addoption(parser):
    parser.addoption(
        "--runslow", action="store_true",
        help="run slow tests that are skipped by default")
    parser.addoption(
        '--qvarn-api', metavar="DSN", default="bda",
        help=(
            "Qvarn API backend DSN, possible values: pretender, bda[+SQLALCHEMY_URL],"
            " qvarn[+SQLALCHEMY_URL]; default value: %(default)s"
        ))
    parser.addoption(
        '--bol-data-api', metavar="DSN",
        help="SQLAlchemy URL override for the BDA backend database")


def pytest_collection_modifyitems(config, items):
    if config.getoption('--runslow'):
        # --runslow given in cli: do not skip slow tests
        return
    skip_slow = pytest.mark.skip(reason="need --runslow option to run")
    for item in items:
        if "slow" in item.keywords:
            item.add_marker(skip_slow)


class XTermProgress:

    def __init__(self, stdout=sys.stdout):
        self.init(stdout)
        self.session = None
        self.reported = set()
        self.args = []

    def init(self, stdout):
        self.stdout = stdout
        self.enabled = os.environ.get('TERM', '').startswith('xterm') and stdout.isatty()

    def ready(self, session):
        self.session = session

    def set_args(self, args):
        self.args = list(args)

    @property
    def finished_tests(self):
        return len(self.reported)

    @property
    def total_tests(self):
        return self.session.testscollected

    @property
    def progress(self):
        if self.total_tests == 0:
            return '0%'
        else:
            return f'{100 * self.finished_tests / self.total_tests:.1f}%'

    def logreport(self, report):
        if self.enabled:
            self.reported.add(report.nodeid)
            message = f"{self.progress} - {' '.join(['pytest'] + self.args)}"
            self.stdout.write(f"\033]0;{message}\a")
            self.stdout.flush()


TERMINAL_PROGRESS = XTermProgress()


@pytest.hookimpl(trylast=True)
def pytest_configure(config):
    standard_reporter = config.pluginmanager.getplugin('terminalreporter')
    if hasattr(getattr(standard_reporter, '_tw', None), '_file'):
        # XXX: I was unable to find a blessed way of getting the real stdout
        # stream from pytest APIs
        TERMINAL_PROGRESS.init(standard_reporter._tw._file)
    if hasattr(config, 'invocation_params'):  # pytest 5.1 required
        TERMINAL_PROGRESS.set_args(config.invocation_params.args)

    # https://docs.pytest.org/en/latest/mark.html#registering-marks
    config.addinivalue_line(
        "markers", (
            "feature_flags(*flags): mark test to run multiple times "
            "with different set of given feature flags"
        )
    )


def pytest_generate_tests(metafunc):
    # Check if test function is decorated with:
    # @pytest.mark.feature_flags(...)
    feature_flags = metafunc.definition.get_closest_marker('feature_flags')
    if feature_flags:
        metafunc.parametrize('feature_flag', feature_flags.args, indirect=True)


def pytest_collection(session):
    TERMINAL_PROGRESS.ready(session)


def pytest_runtest_logreport(report):
    TERMINAL_PROGRESS.logreport(report)


def _is_xmlrpc_request(r):
    return r.headers.get('Content-Type', '').startswith('text/xml') and r.headers.get('Soapaction')


def _normalized_nsmap(orig_nsmap):
    preferred_prefixes = {
        'http://schemas.xmlsoap.org/soap/envelope/': 'SOAP-ENV',
        'http://www.dnbnordic.com/brg/NRGCompanyReportStandard/request': 'Bisnode',
        'https://webservice.creditsafe.se/getdata/': 'Creditsafe',
        'http://www.w3.org/2001/XMLSchema-instance': 'xsi',
        'https://webservice.creditsafe.se/CompanyMonitoring/': 'Companymonitoring',
    }
    nsmap = {}
    for k, v in orig_nsmap.items():
        if v in preferred_prefixes:
            nsmap[preferred_prefixes[v]] = v
    for k, v in orig_nsmap.items():
        if v not in preferred_prefixes and k not in nsmap:
            nsmap[k] = v
    return dict(sorted(nsmap.items()))


def _transform_xmlrpc_request(body):
    # Normalize namespace prefixes so we can compare two XML-RPC requests as strings.
    parser = etree.XMLParser(remove_blank_text=True)
    tree = etree.fromstring(body, parser)
    new_tree = etree.Element(tree.tag, tree.attrib, nsmap=_normalized_nsmap(tree.nsmap))
    new_tree.extend(tree)
    return etree.tostring(new_tree, pretty_print=True).decode('UTF-8')


def _diff(s1, s2):
    if s1 and not s1.endswith('\n') or s2 and not s2.endswith('\n'):
        # otherwise the diff becomes unreadable
        s1 += '\n'
        s2 += '\n'
    return '  ' + ''.join(difflib.ndiff(s1.splitlines(True),
                                        s2.splitlines(True))).replace('\n', '\n  ')


def vcr_body_matcher(r1, r2):
    b1 = read_body(r1)
    b2 = read_body(r2)
    if b1 == b2:
        return
    if b1 is None or b2 is None:
        if b2 == b'null':
            # welp, our YAMLs store nulls using the YAML standard `null` syntax, but stv-utils
            # monkey-patches PyYAML to load `null` as a string "null" so ¯\_(ツ)_/¯
            # https://git.vaultit.org/common/stv-utils/commit/850bb7a6e3e#306a91f01e4b33119cd0c65876428752c66a0da5
            return
        raise AssertionError('one request is missing its body, the other has it')
    if _is_xmlrpc_request(r1) and _is_xmlrpc_request(r2):
        rq1 = _transform_xmlrpc_request(b1)
        rq2 = _transform_xmlrpc_request(b2)
        if rq1 == rq2:
            return
    else:
        rq1 = b1.decode('UTF-8')
        rq2 = b2.decode('UTF-8')
    logger.debug("VCR mismatch:\n%s", _diff(rq1, rq2))
    # There are two kinds of matchers that are supported by VCR: one kind
    # returns True/False, the other kind returns None or raises AssertionError.
    raise AssertionError(_diff(rq1, rq2))


@pytest.fixture
def vcr():
    my_vcr = vcrpy.VCR(
        cassette_library_dir=utils.get_fixture_path('vcr'),
        # Raise error for any new requests not recorded previously.
        # See: https://vcrpy.readthedocs.io/en/latest/usage.html#record-modes
        # Use 'once' if want to record new cassette.
        # Use 'new_episodes' if want to modify existing cassettes to add new requests.
        # Use 'all' if want to re-create the cassette from scratch, discarding old recordings.
        record_mode='none',
        decode_compressed_response=True,
    )
    my_vcr.register_matcher('my_body', vcr_body_matcher)
    my_vcr.match_on = ['method', 'scheme', 'host', 'port', 'path', 'query', 'my_body']
    return my_vcr


@pytest.fixture
def freezetime(request, mocker):
    def unfreeze():
        FakeDate.dates_to_freeze.pop()
        FakeDate.tz_offsets.pop()

        FakeDatetime.times_to_freeze.pop()
        FakeDatetime.tz_offsets.pop()

    def freeze(time_to_freeze_str, tz_offset=0):
        if isinstance(time_to_freeze_str, datetime.datetime):
            time_to_freeze = time_to_freeze_str
        elif isinstance(time_to_freeze_str, datetime.date):
            time_to_freeze = datetime.datetime.combine(time_to_freeze_str, datetime.time())
        else:
            time_to_freeze = dateutil.parser.parse(time_to_freeze_str)

        time_to_freeze = convert_to_timezone_naive(time_to_freeze)
        time_to_freeze = FrozenDateTimeFactory(time_to_freeze)

        FakeDate.dates_to_freeze.append(time_to_freeze)
        FakeDate.tz_offsets.append(tz_offset)

        FakeDatetime.times_to_freeze.append(time_to_freeze)
        FakeDatetime.tz_offsets.append(tz_offset)

        mocker.patch('datetime.date', FakeDate)
        mocker.patch('datetime.datetime', FakeDatetime)

        request.addfinalizer(unfreeze)

    return freeze


@pytest.fixture(scope='session')
def realqvarn_session(session_mock_requests):
    return RealQvarn(session_mock_requests, 'https://session-realqvarn')


@pytest.fixture()
def realqvarn(session_mock_requests):
    return RealQvarn(session_mock_requests, 'https://realqvarn').qvarn


@pytest.fixture
def storage(qvarn, bda, core, user_account_api, contract_client, config):
    return Storage(qvarn=qvarn,
                   bda=bda,
                   core=core,
                   user_account_api=user_account_api,
                   contract_api=contract_client())


@pytest.fixture
def storage_realqvarn(realqvarn, bda, user_account_api, config):
    return Storage(qvarn=realqvarn, bda=bda, core=None, user_account_api=user_account_api)


@pytest.fixture
def admin_user_realqvarn(app_realqvarn, storage, realqvarn):
    storage.qvarn = realqvarn
    org = factories.get_or_create_org(storage, 'Trafikverket')
    return app_realqvarn.login(org, admin=True)


# Celery!  First let's make sure our tests don't accidentally try to post jobs
# into a real rabbitmq that might be running on localhost.

@pytest.fixture(scope='session', autouse=True)
def neutralize_celery():
    # make sure any tests not explicitly mocking celery tasks don't actually
    # try to talk to the real rabbitmq
    bolfak_celery.conf.update(
        broker_url='pyamqp://nosuchhost.example.com',
        broker_connection_retry=False,
        broker_connection_retry_on_startup=False,
        broker_connection_timeout=0.001,
        broker_transport_options=dict(max_retries=1),
    )
    # if any tests rely on celery_worker or celery_session_worker, make sure that worker
    # doesn't try to set up real database connections etc.
    # NB: we need dispatch_uid to work around a Celery bug; it must match the one used in
    # worker_process_init.connect() in tasks.py.
    worker_process_init.disconnect(init_worker, dispatch_uid='init_worker')
    with pytest.MonkeyPatch.context() as mp:
        for envvar in os.environ:
            if envvar.startswith('CELERY_'):
                mp.delenv(envvar)
        yield


# Some fixtures (celery_app, celery_worker) are provided by Celery's pytest
# plugin.  They can be configured by overriding some other standard Celery
# fixtures (celery_includes, celery_enable_logging, use_celery_app_trap).
# See https://docs.celeryproject.org/en/stable/userguide/testing.html.

@pytest.fixture(scope='session')
def celery_includes():
    return ['bolfak.celery.tasks']


@pytest.fixture(scope='session')
def celery_enable_logging():
    # TBH I'm not sure this does anything at all, at least I haven't noticed
    # any log entries from it
    return True


@pytest.fixture(scope='session')
def celery_config():
    return {
        'broker_connection_retry': False,
        'broker_connection_retry_on_startup': False,
    }


@pytest.fixture(scope='session')
def use_celery_app_trap():
    # making sure no test accidentally depends on bolfak_celery being the current/default app
    return True


# And these are our own fixtures.


@pytest.fixture
def celery_for_bulk_imports(celery_app, depends_on_current_app):
    # FWIW celery_app and depends_on_current_app are fixtures provided by
    # celery's pytest plugin.
    pass


@pytest.fixture(params=[True, False], ids=['celery', 'direct'])
def celery_for_sendgrid_enabled_and_disabled(
    set_feature_flags, celery_app, depends_on_current_app, request
):
    # FWIW celery_app and depends_on_current_app are fixtures provided by
    # celery's pytest plugin.
    set_feature_flags({'celery_for_sendgrid': request.param})
    return request.param


@pytest.fixture
def celery_for_monitoring_enabled(celery_app, depends_on_current_app):
    # FWIW celery_app and depends_on_current_app are fixtures provided by
    # celery's pytest plugin.
    pass


@pytest.fixture
def celery_for_status_reports_enabled(celery_app, depends_on_current_app):
    # FWIW celery_app and depends_on_current_app are fixtures provided by
    # celery's pytest plugin.
    pass


@pytest.fixture(params=[True, False])
def project_client_enabled_and_disabled(set_feature_flags, request):
    set_feature_flags({'add_project_client': request.param})
    return request.param


@pytest.fixture(params=[True, False], ids=['core_enabled', 'core_disabled'])
def core_enabled_and_disabled(set_feature_flags, set_feature_flags_for_bda, request):
    set_feature_flags({'core_mitt_id06': request.param})
    set_feature_flags_for_bda({
        'core': request.param,
    })
    return request.param


@pytest.fixture
def get_status_reports_jobs(mocker, celery_app, depends_on_current_app):
    jobs = []
    real_send_task = celery.app.Celery.send_task

    def send_task_wrapper(self, name, args=None, kwargs=None, **kw):
        print(f'{name}(*{args}, **{{{kwargs}}})')
        if name == 'bolfak.celery.tasks.update_all_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_ALL,
            )))
        elif name == 'bolfak.celery.tasks.update_swedish_changed_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            changed = args[0]
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_CHANGED,
                last_days=str(changed)
            )))
        elif name == 'bolfak.celery.tasks.update_outdated_foreign_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            older_than_days = args[0]
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_OUTDATED,
                older_than_days=str(older_than_days)
            )))
        elif name == 'bolfak.celery.tasks.update_missing_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_MISSING,
            )))
        elif name == 'bolfak.celery.tasks.update_incomplete_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_INCOMPLETE,
            )))
        elif name == 'bolfak.celery.tasks.update_cs_swe_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            org_ids = args[0]
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_CS_SWE,
                org_ids=' '.join(org_ids),
            )))
        elif name == 'bolfak.celery.tasks.update_cs_swe_batched_statusreports_task':
            # Let's approximate the old QvarnMQ job resource
            org_ids = args[0]
            jobs.append(mocker.Mock(parameters=dict(
                mode=JOB_MODE_CS_SWE_BATCHED,
                org_ids=' '.join(org_ids),
            )))
        elif name == 'bolfak.celery.tasks.update_cs_swe_statusreports_with_emptax_task':
            # Let's approximate the old QvarnMQ job resource
            interested = args[0]
            jobs.append(mocker.Mock(parameters=dict(
                interested=interested,
            )))
        elif (name == 'bolfak.celery.tasks.update_statusreports_orgs_task' or
              name == 'bolfak.celery.tasks.update_statusreports_orgs_file_task'):
            # Let's approximate the old QvarnMQ job resource
            org_ids = args[0]
            interested_org_id = args[1]
            parameters = dict(
                org_ids='\n'.join(org_ids),
            )
            if interested_org_id:
                parameters['interested_org_id'] = interested_org_id
            if 'skip_existing_reports' in kwargs:
                # NB: real QvarnMQ jobs never had a skip_existing_reports parameter,
                # but we need some way to pass the value of this parameter to unit tests
                parameters['skip_existing_reports'] = kwargs['skip_existing_reports']
            jobs.append(mocker.Mock(parameters=parameters))

        return real_send_task(self, name, args, kwargs, **kw)

    mocker.patch('celery.app.Celery.send_task', send_task_wrapper)

    def get_status_reports_jobs():
        return jobs

    return get_status_reports_jobs


@pytest.fixture()
def get_monitoring_jobs(mocker, celery_app, depends_on_current_app):
    jobs = []
    real_send_task = celery.app.Celery.send_task

    # I would prefer to monkey-patch bolfak.celery.tasks.{add_to,remove_from}_monitoring_task.delay
    # directly, but some celery proxy magic that @shared_task does breaks that (during mocker
    # cleanup).

    def send_task_wrapper(self, name, args=None, kwargs=None, **kw):
        print(f'{name}(*{args}, **{{{kwargs}}})')
        if name == 'bolfak.celery.tasks.add_to_monitoring_task':
            org_ids, interested_org_id = args
            # Let's approximate the old QvarnMQ job resource
            jobs.append(mocker.Mock(parameters=dict(
                mode='add',
                interested_org_id=interested_org_id,
                org_ids='\n'.join(sorted(org_ids)),
            )))
        elif name == 'bolfak.celery.tasks.remove_from_monitoring_task':
            org_ids, interested_org_id = args
            # Let's approximate the old QvarnMQ job resource
            jobs.append(mocker.Mock(parameters=dict(
                mode='remove',
                interested_org_id=interested_org_id,
                org_ids='\n'.join(sorted(org_ids)),
            )))
        return real_send_task(self, name, args, kwargs, **kw)

    mocker.patch('celery.app.Celery.send_task', send_task_wrapper)
    return lambda: jobs


# This runs the test (once) with a temporary celery worker running in a thread
# (which means you can monkey-patch things and the worker will see that).
@pytest.fixture
def celery_worker_enabled(
    set_feature_flags, celery_app, depends_on_current_app, celery_worker, config, storage, mocker,
    sendgrid_client, contract_client,
):
    # FWIW celery_worker is a fixture provided by celery's pytest plugin.
    set_feature_flags({
        'celery_for_sendgrid': True,
    })
    # celery worker initialization changes the global warning level to ERROR, augh!
    logging.getLogger().setLevel(logging.WARNING)
    storage.contract_api = contract_client()
    mocker.patch('bolfak.celery.tasks.storage', storage)
    mocker.patch('bolfak.celery.tasks.sendgrid_client', sendgrid_client)


@pytest.fixture(params=[True, False], ids=['bda', 'qvarn'])
def user_account_api_enabled_and_disabled(set_feature_flags, request):
    set_feature_flags({'user_account_api': request.param})
    return request.param


@pytest.fixture(params=[True, False], ids=['bda', 'qvarn'])
def bda_project_users_feature_flag_enabled_and_disabled(set_feature_flags, request):
    set_feature_flags({'bda_project_users': request.param})
    return request.param


@pytest.fixture(params=[True], ids=['person_id'])
def person_id_for_project_users_feature_flag_enabled_and_disabled(
    set_feature_flags, set_feature_flags_for_bda, request
):
    set_feature_flags({'person_id_for_project_users': request.param})
    set_feature_flags_for_bda({'person_id_for_project_users': request.param})
    return request.param


@pytest.fixture
def contract_client(config, mocker):
    def make_contract_client(credentials={'usename': 'signatory-user', 'password': 'verysecret'},
                             key=CS_ACCOUNT_STORAGE_SECRET_KEY):
        mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
        mocker.patch('bolfak.clients.contract.ContractClient.check_status')

        client = setup_contract_client(config)

        # If not defined (e.g. when using Core), don't try to set up mocks on a nonexistent client
        if client is None:
            return None

        # mock contract client API interface functions to some static responses
        mocked_create = mocker.patch.object(client, 'create_creditsafe_contract')
        mocked_create.return_valueMockResponse(json=[{'ok': True}])

        mocked_get = mocker.patch.object(client, 'get_creditsafe_contracts')
        mocked_get.return_value = MockResponse(
            json=[{'created_at': '2023-03-24T10:50:45.998088Z',
                   'email': '<EMAIL>',
                   'history': [{'modified_by': 'person-id',
                                'modified_time': '2023-03-24T10:50:45.998147Z',
                                'state': 'pending'}],
                   'id': 'b2c5-4df856bb2c114ab53d3432645b88d9fc-5268f754',
                   'organization_id': 'f392-f6ef16aeb76a9d08a7af737204247b36-af508d38',
                   'password': 'password',
                   'person_id': 'person-id',
                   'state': 'pending',
                   'tos_language': 'EN',
                   'tos_version': '1.0',
                   'updated_at': None,
                   'username': 'username'},
                  {'created_at': '2023-01-28T18:31:22.441823Z',
                   'email': None,
                   'history': [{'modified_by': '0035-8ba3b7734d6ac6282ec72fd077a35145-584bd878',
                                'modified_time': '2023-01-28T18:31:22.442925Z',
                                'state': 'active'}],
                   'id': 'b2c5-8a354f99341689cc4ab3addc263a0779-ef4acbb8',
                   'organization_id': 'f392-8347c2b694a33a58456e7351cc4e1c0c-1c78060e',
                   'password': symmetric_encrypt(bytes(credentials.get('password'), 'UTF-8'),
                                                 key),
                   'person_id': '0035-8ba3b7734d6ac6282ec72fd077a35145-584bd878',
                   'state': 'active',
                   'tos_language': 'EN',
                   'tos_version': '1.0',
                   'updated_at': None,
                   'username': credentials.get('username')}]
        )
        mocked_update = mocker.patch.object(client, 'update_creditsafe_contract')
        mocked_update.return_value = MockResponse(
            json={'created_at': '2023-03-24T10:50:45.998088Z',
                  'email': '<EMAIL>',
                  'history': [{'modified_by': 'person-id',
                               'modified_time': '2023-03-24T10:50:45.998147Z',
                               'state': 'pending'},
                              {'modified_by': 'person-id',
                               'modified_time': '2024-03-24T10:50:45.998147Z',
                               'state': 'terminated'}],
                  'id': 'b2c5-4df856bb2c114ab53d3432645b88d9fc-5268f754',
                  'organization_id': 'f392-f6ef16aeb76a9d08a7af737204247b36-af508d38',
                  'password': 'password',
                  'person_id': 'person-id',
                  'state': 'terminated',
                  'tos_language': 'EN',
                  'tos_version': '1.0',
                  'updated_at': '2024-03-24T10:50:45.998147Z',
                  'username': 'username'}
        )

        return client

    return make_contract_client


@pytest.fixture
def contract_client_multiple(config, mocker):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    mocker.patch('bolfak.clients.contract.ContractClient.check_status')

    client = setup_contract_client(config)

    # mock contract client API interface functions to some static responses
    mocked_call = mocker.patch.object(client, 'get_creditsafe_contracts')
    mocked_call.return_value = MockResponse(
        json=[{'created_at': '2023-03-24T10:50:45.998088Z',
               'email': '<EMAIL>',
               'history': [{'modified_by': 'person-id',
                            'modified_time': '2023-03-24T10:50:45.998147Z',
                            'state': 'pending'}],
               'id': 'b2c5-4df856bb2c114ab53d3432645b88d9fc-5268f754',
               'organization_id': 'f392-f6ef16aeb76a9d08a7af737204247b36-af508d38',
               'password': 'password1',
               'person_id': 'person-id',
               'state': 'active',
               'tos_language': 'EN',
               'tos_version': '1.0',
               'updated_at': None,
               'username': 'username1'},
              {'created_at': '2023-01-28T18:31:22.441823Z',
               'email': None,
               'history': [{'modified_by': '0035-8ba3b7734d6ac6282ec72fd077a35145-584bd878',
                            'modified_time': '2023-01-28T18:31:22.442925Z',
                            'state': 'active'}],
               'id': 'b2c5-8a354f99341689cc4ab3addc263a0779-ef4acbb8',
               'organization_id': 'f392-8347c2b694a33a58456e7351cc4e1c0c-1c78060e',
               'password': 'password2',
               'person_id': '0035-8ba3b7734d6ac6282ec72fd077a35145-584bd878',
               'state': 'active',
               'tos_language': 'EN',
               'tos_version': '1.0',
               'updated_at': None,
               'username': 'username2'}]
    )

    return client


@pytest.fixture
def contract_client_wrong_password(config, mocker):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    mocker.patch('bolfak.clients.contract.ContractClient.check_status')

    client = setup_contract_client(config)

    # mock contract client API interface functions to some static responses
    mocked_call = mocker.patch.object(client, 'get_creditsafe_contracts')
    mocked_call.return_value = MockResponse(
            json=[{'created_at': '2023-03-24T10:50:45.998088Z',
                   'email': '<EMAIL>',
                   'history': [{'modified_by': 'person-id',
                                'modified_time': '2023-03-24T10:50:45.998147Z',
                                'state': 'pending'}],
                   'id': 'b2c5-4df856bb2c114ab53d3432645b88d9fc-5268f754',
                   'organization_id': 'f392-f6ef16aeb76a9d08a7af737204247b36-af508d38',
                   'password': 'gAAAAABkRhJt7Wioh8Chc2Khu87vGndMER20D02lZCmUvjm_OOOQuYHer1KAwjDHONIGYJaVI0ILhsCiJh1h5etT-sSpNdHZ5w==',  # noqa
                   'person_id': 'person-id',
                   'state': 'active',
                   'tos_language': 'EN',
                   'tos_version': '1.0',
                   'updated_at': None,
                   'username': 'signatory-user'}]
        )

    return client


@pytest.fixture
def contract_client_empty_password(config, mocker):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    mocker.patch('bolfak.clients.contract.ContractClient.check_status')

    client = setup_contract_client(config)

    # mock contract client API interface functions to some static responses
    mocked_call = mocker.patch.object(client, 'get_creditsafe_contracts')
    mocked_call.return_value = MockResponse(
        json=[{'created_at': '2023-03-24T10:50:45.998088Z',
               'email': '<EMAIL>',
               'history': [{'modified_by': 'person-id',
                            'modified_time': '2023-03-24T10:50:45.998147Z',
                            'state': 'pending'}],
               'id': 'b2c5-4df856bb2c114ab53d3432645b88d9fc-5268f754',
               'organization_id': 'f392-f6ef16aeb76a9d08a7af737204247b36-af508d38',
               'password': '',
               'person_id': 'person-id',
               'state': 'active',
               'tos_language': 'EN',
               'tos_version': '1.0',
               'updated_at': None,
               'username': 'signatory-user'}]
    )

    return client


@pytest.fixture
def autoaccount_client_empty(storage, config, mocker):
    mocked_cs_response = _ResultWrapper({
        'xmlresponse': {
            'body': {
                'username': 'TestAutoAccount123',
                'password': 'AutoAccountPasswd'
            }
        }
    })
    mocker.patch(
        'bolfak.clients.autoaccount.CreditsafeAutoAccountClient'
        '._create_creditsafe_autoaccount', return_value=mocked_cs_response)

    return setup_autoaccount_client(storage, config)


@pytest.fixture
def contract_client_empty(config, mocker):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    mocker.patch('bolfak.clients.contract.ContractClient.check_status')

    client = setup_contract_client(config)

    # mock contract client API interface functions to some static responses
    mocked_call = mocker.patch.object(client, 'get_creditsafe_contracts')
    mocked_call.return_value = MockResponse(json=[])

    return client


@pytest.fixture
def contract_api_in_memory(mock_requests, mocker, config):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    ContractApi(mock_requests, 'https://contract-example.org')
    return setup_contract_client(config)


@pytest.fixture
def stamp(config, mocker, ff_visitors_on):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    return setup_stamp_client(config)


@pytest.fixture
def user_account_api(mock_requests, mocker, config):
    mocker.patch('bolfak.clients.gluu.GluuRequests._check_access_token')
    UserAccountApi(mock_requests, 'https://user-account-api')
    return setup_user_account_api_client(config)


@pytest.fixture
def company_list_fixture(app, storage):
    qvarn = storage.qvarn

    supplier = factories.supplier

    factories.create_org(storage, 'Test Company Admin',
                         registration_id=ADMIN_ORG_REG_NO)
    resp_org = factories.create_org(storage, 'Test Company Main',
                                    registration_id=MAIN_ORG_REG_NO)
    factories.create_org(storage, 'Test Company 1', registration_id='TC-1001')
    factories.create_org(storage, 'Test Company 2', registration_id='TC-1002')
    factories.create_org(storage, 'Test Company 3', registration_id='TC-1003')
    factories.create_org(storage, 'Test Company 4', registration_id='TC-1004')
    factories.create_org(storage, 'Test Company 5', registration_id='TC-1005')

    person = factories.create_person(qvarn, 'Anthony Bull', '<EMAIL>')

    p1 = factories.create_default_project(
        storage, 'Test project 1', project_id='PR-1', org=resp_org, project_leader=person
    )
    p2 = factories.create_default_project(
        storage, 'Test project 2', project_id='PR-2', org=resp_org, project_leader=person
    )
    p3 = factories.create_default_project(
        storage, 'Test project 3', project_id='PR-3', org=resp_org, project_leader=person
    )

    factories.create_suppliers_tree(
        storage, p1, [supplier('Test Company 1', supplier_role=SUPERVISOR_ROLE),
                      supplier('Test Company 3'),
                      supplier('Test Company 4')]
    )
    factories.create_suppliers_tree(
        storage, p2, [supplier('Test Company 2', supplier_role=SUPERVISOR_ROLE),
                      supplier('Test Company 3')]
    )
    factories.create_suppliers_tree(
        storage, p3, [supplier('Test Company 1', supplier_role=SUPERVISOR_ROLE),
                      supplier('Test Company 3')]
    )

    factories.create_report(storage, 'Test Company 1', status=STATUS_STOP,
                            interested_org_id=resp_org['id'])
    factories.create_report(storage, 'Test Company 4', status=STATUS_STOP,
                            interested_org_id=resp_org['id'])
    factories.create_report(storage, 'Test Company 5', status=STATUS_OK)


@pytest.fixture
def company_list_fixture_main(app, storage, company_list_fixture):
    person = factories.get_or_create_person(storage, 'Anthony Bull')
    resp_org = factories.get_or_create_org(storage, 'Test Company Main')
    app.login(resp_org, person, admin=False)


@pytest.fixture
def company_list_fixture_admin(app, storage, company_list_fixture):
    person = factories.get_or_create_person(storage, 'Anthony Bull')
    resp_org = factories.get_or_create_org(storage, 'Test Company Admin')
    app.login(resp_org, person, admin=True)


@pytest.fixture
def report_provider_cs(storage, vcr, contract_client):
    def make_report_provider_cs(credentials={'username': 'signatory-user',
                                             'password': 'verysecret'},
                                key=CS_ACCOUNT_STORAGE_SECRET_KEY,
                                interested_org_id=None,
                                contract_api=None):
        if contract_api is None:
            contract_api = contract_client(credentials, key)
        config = get_provider_config('creditsafe')
        provider = Creditsafe(
            config,
            storage.replace(contract_api=contract_api),
            'cache-key',
            contract_client=contract_api,
        )
        return provider

    with vcr.use_cassette('creditsafe_just_the_wsdl.yaml'):
        yield make_report_provider_cs


@pytest.fixture
def report_provider_cs_interested(storage, vcr, contract_client):
    config = get_provider_config('creditsafe')
    storage = storage.replace(contract_api=contract_client())
    provider = Creditsafe(config, storage, 'cache-key',
                          interested_org_id='777777-7777',
                          contract_client=storage.contract_api)
    with vcr.use_cassette('creditsafe_just_the_wsdl.yaml'):
        yield provider


@pytest.fixture
def report_provider_cs_v2(storage, vcr, contract_client):
    config = get_provider_config('creditsafe_v2')
    storage = storage.replace(
        contract_api=contract_client(
            credentials={'username': 'signatory-user', 'password': 'verysecret'},
            key=CS_ACCOUNT_STORAGE_SECRET_KEY,
        )
    )
    provider = CreditsafeV2(config, storage, 'cache-key', contract_client=storage.contract_api)
    with vcr.use_cassette('creditsafe_v2.yaml'):
        yield provider


@pytest.fixture
def info_provider_cs(storage, vcr):
    config = get_provider_config('creditsafe')
    provider = CreditsafeInfoProvider(config, storage, 'cache-key')
    with vcr.use_cassette('creditsafe_just_the_wsdl.yaml'):
        yield provider


def _get_json_cs_v2(report_provider_cs_v2, mocker, **kwargs):
    provider = report_provider_cs_v2

    mocker.patch('bolfak.statusreports.creditsafe.CreditsafeV2._fetch_entry',
                 return_value=get_creditsafe_response_v2(**kwargs))
    token = provider.get_token_from_gov_org_id('222222-0042')
    data = provider.fetch([token])
    token, report = next(provider.parse([token], data))

    return report


@pytest.fixture
def mock_response_empty():
    return MockResponse(json=[])


@pytest.fixture
def get_json_cs_v2():
    """We use this contraption to avoid import problem:
     - `from conftest import get_json_cs_v2` fails on localhost`
     - `from test.conftest import get_json_cs_v2` fails on CI/CD`
    """
    return _get_json_cs_v2


@pytest.fixture
def json_cs_v2(report_provider_cs_v2, mocker):
    return _get_json_cs_v2(report_provider_cs_v2, mocker)


@pytest.fixture
def connect_provider(storage, vcr, mocker, contract_client):
    config = get_provider_config('creditsafe_connect')
    provider = ConnectReportProvider(config, storage, contract_client, 'cache-key')
    return provider


@pytest.fixture
def sendgrid_client(mocker, config):
    sg = setup_sendgrid_client(config)
    client = mocker.Mock(spec=sg)
    client.default_sender = '<EMAIL>'
    return client


@pytest.fixture
def crashing_sendgrid_client(sendgrid_client):
    sendgrid_client.send_email = KeyError("induced test failure")
    return sendgrid_client


@pytest.fixture(params=[True, False])
def skip_pa_reg_step_enabled_and_disabled(set_feature_flags, request):
    set_feature_flags({'skip_pa_reg_step': request.param})
    return request.param


@pytest.fixture()
def skip_pa_reg_step_enabled(
    set_feature_flags
):
    set_feature_flags({'skip_pa_reg_step': True})


@pytest.fixture
def setup_and_teardown_external_record():
    base_url = get_core_base_url() + '/test-api'
    mock_org = MOCK_ORGS['monsters_inc']
    requests.post(f'{base_url}/organisationrecord-external', json=mock_org)
    yield mock_org
    requests.delete(
        f'{base_url}/organisationrecord-external/{mock_org["externalIdentifier"]}'
    )


@pytest.fixture
def setup_and_teardown_organisation_person(storage):
    base_url = get_core_base_url() + '/test-api'

    created_person = None
    created_org_record = None
    org_uuid_to_remove = None

    def create_org_person(
        org_uuid: Optional[str] = None,
        **kwargs
    ) -> Tuple[OrganisationPerson, Optional[OrganisationRecord]]:
        nonlocal created_person, org_uuid_to_remove,  created_org_record
        if not org_uuid:
            org_record_dict = factories.create_org_record('orgperson_organisation_name',
                                                          create_organisation=True)
            created_org_record = OrganisationRecord.deserialize(org_record_dict)
            org_uuid = created_org_record.organisation.uuid
        assert org_uuid is not None
        org_uuid_to_remove = org_uuid
        created_person = factories.create_org_person(storage, org_uuid, **kwargs)
        return created_person, created_org_record

    yield create_org_person

    requests.delete(
        f'{base_url}/organisation-person/{org_uuid_to_remove}/{created_person.person.uuid}'
    )
    if created_org_record is not None:
        requests.delete(f'{base_url}/organisation-record/{created_org_record.uuid}')


@pytest.fixture
def setup_and_teardown_organisation_persons_bol(storage):
    base_url = get_core_base_url() + '/test-api'

    created_org = None
    person_not_bol = None
    person_bol_user = None
    person_bol_administrator = None
    person_bol_user_administrator = None

    def create_org_persons(
        org_uuid: Optional[str] = None,
        **kwargs
    ) -> Tuple[CoreOrganisation,
               OrganisationPerson,
               OrganisationPerson,
               OrganisationPerson,
               OrganisationPerson]:

        nonlocal created_org, person_bol_user, person_bol_administrator, \
            person_bol_user_administrator, person_not_bol

        created_org = factories.create_core_organisation('test_org')

        person_not_bol = factories.create_org_person(
            storage, created_org.uuid, roles=['ADMINISTRATOR'], **kwargs)
        person_bol_user = factories.create_org_person(
            storage, created_org.uuid, roles=['BOL_USER'], **kwargs)
        person_bol_administrator = factories.create_org_person(
            storage, created_org.uuid, roles=['BOL_ADMINISTRATOR'], **kwargs)
        person_bol_user_administrator = factories.create_org_person(
            storage, created_org.uuid, roles=['BOL_USER', 'BOL_ADMINISTRATOR'], **kwargs)

        return created_org, person_not_bol, person_bol_user, \
            person_bol_administrator, person_bol_user_administrator

    yield create_org_persons

    requests.delete(
        f'{base_url}/organisation-person/{created_org.uuid}/{person_not_bol.person.uuid}')
    requests.delete(
        f'{base_url}/organisation-person/{created_org.uuid}/{person_bol_user.person.uuid}')
    requests.delete(
        f'{base_url}/organisation-person/{created_org.uuid}/{person_bol_administrator.person.uuid}')
    requests.delete(
        f'{base_url}/organisation-person/{created_org.uuid}/ \
            {person_bol_user_administrator.person.uuid}')
    requests.delete(f'{base_url}/organisation/{created_org.uuid}')


@pytest.fixture
def setup_and_teardown_core_and_org_person_bol(storage):
    """This fixture creates both the core person and organisation person
        And the organisation person will belong to the given organisation and get roles
        assigned based on optionally given roles (default BOL_USER)
    """
    base_url = get_core_base_url() + '/test-api'
    org_person = None
    org_uuid = None

    def create_core_and_org_person(
        organisation: CoreOrganisation, roles: List[str] = ['BOL_USER'],
        names: List[str] = ['Test Testsson'],
        **kwargs
    ) -> OrganisationPerson:

        nonlocal org_person, org_uuid

        core_person = factories.create_person(
            storage, names=names,
            email=names[0].replace(" ", "").lower()+'@privateemail.com', **kwargs)

        org_person = factories.create_org_person(
            storage, organisation.uuid, person_id=core_person['id'],
            names=names, email=names[0].replace(" ", "").lower()+'@workemail.com', roles=roles,
            **kwargs)

        org_uuid = organisation.uuid

        return org_person

    yield create_core_and_org_person

    requests.delete(f'{base_url}/organisation-person/{org_uuid}/{org_person.person.uuid}')
    requests.delete(f'{base_url}/person/{org_person.person.uuid}')


@pytest.fixture
def setup_and_teardown_org_record_and_org():
    """This fixture creates both an organisation record and an organisation
    and connects them
    """
    base_url = get_core_base_url() + '/test-api'

    created_org_record = None
    created_org = None

    def create_org_and_org_record(org_name: Optional[str] = 'test_org',
                                  org_record_name: Optional[str] = 'test_org_record') \
        -> tuple[CoreOrganisation,
                 OrganisationRecord,
                 dict]:

        nonlocal created_org, created_org_record

        org_record_dict = factories.create_org_record(name=org_record_name,
                                                      create_organisation=False)
        created_org_record = OrganisationRecord.deserialize(org_record_dict)

        created_org = factories.create_core_organisation(
            name=org_name, organisation_record_uuid=created_org_record.uuid,
            org_uuid=created_org_record.organisation.uuid)

        return created_org, created_org_record, org_record_dict

    yield create_org_and_org_record

    requests.delete(f'{base_url}/organisation/{created_org.uuid}')
    requests.delete(f'{base_url}/organisationrecord/{created_org_record.uuid}')


@pytest.fixture
def setup_and_teardown_project(storage):
    created_project = None

    # TODO: Replace return type when we have a common model for projects
    def create_project_core(
        organisation: CoreOrganisation, organisation_record: OrganisationRecord,
        project_name: Optional[str] = 'test_project', project_id: Optional[str] = 'PRJ-42') \
            -> any:

        nonlocal created_project

        created_project = factories.create_default_project(
            storage, project_id=project_id, name=project_name, org=organisation_record.uuid,
            created_by_org_id=organisation_record.uuid, client_company_id=organisation.uuid)

        return created_project

    yield create_project_core

    delete_project(storage.qvarn, created_project['id'])


@pytest.fixture
def setup_and_teardown_person(storage):
    base_url = get_core_base_url() + '/test-api'

    person = None

    def create_person(
        **kwargs
    ) -> CorePerson:
        nonlocal person
        person = factories.create_core_person(**kwargs)
        return person

    yield create_person

    requests.delete(
        f'{base_url}/person/{person.uuid}'
    )


def make_supplier_contact_dict(contract_id, person_id, email):
    if feature_active('person_id_for_project_users'):
        return {
            'supplier_contact_person_id': person_id,
            'supplier_contact_email': email,
        }
    else:
        return {
            'supplier_contact_contract_id': contract_id,
            'supplier_contact_email': email,
        }
