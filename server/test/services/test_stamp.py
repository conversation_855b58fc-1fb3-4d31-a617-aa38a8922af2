import datetime
import json
import logging
import textwrap
from operator import itemgetter

import pytest
from requests.exceptions import HTTPError
from bolfak.exceptions import DataSubscriptionError

from bolfak.fixtures import factories
from bolfak.models import (
    PA_STATUS_CONFIRMED,
    PA_STATUS_CREATED,
    PA_STATUS_REGISTERED,
    PA_STATUS_REJECTED,
    RAW,
    UNLINKED,
    VISITOR,
)
from bolfak.services.project_tree import get_project_tree
from bolfak.services.stamp import (
    get_bol_stamps,
    get_stamp_project_ids,
    get_stamps_from_client,
    process_stamps,
    process_stamps_project,
)
from bolfak.storage.qvarn.stamp import get_project_stamps
from bolfak.storage.core.company import subscribe_company_by_internal_id


class MockResponse:
    def __init__(self, json):
        self._json = json

    @property
    def text(self):
        return json.dumps(self._json)

    def json(self):
        return self._json


# Stub data
PROJECT_STAMPS = {
    'date': '2020-10-15',
    'sites': {
        'PROJECT-TAX-ID-1': [
            {
                'company_registration_number': 'company-gov_org_id-1',
                'country_code': 'SE',
            },
            {
                'company_registration_number': 'company-gov_org_id-3',
                'country_code': 'SE',
            }
        ],
        'PROJECT-TAX-ID-3': [
            {
                'company_registration_number': 'company-gov_org_id-3',
                'country_code': 'SE',
            },
        ]
    }
}


def test_get_bol_stamps(storage):
    project1 = factories.create_default_project(storage, tax_id='project-tax-id-1')
    factories.create_default_project(storage, tax_id='project-tax-id-spam')
    project3 = factories.create_default_project(storage, tax_id='project-tax-id-3')

    # stamps uses alpha-2 codes
    company1 = factories.create_org(storage, registration_id='company-gov_org_id-1', country='SE')
    factories.create_org(storage, registration_id='company-gov_org_id-SPAM')
    company3 = factories.create_org(storage, registration_id='company-gov_org_id-3', country='SE')

    assert get_bol_stamps(storage, stamps=PROJECT_STAMPS, projects=[project1, project3]) == {
        project1['id']: {company1['id'], company3['id']},
        project3['id']: {company3['id']},
    }


def test_get_bol_stamps_empty(storage, caplog):
    empty_stamps = {
        'date': '2020-10-15T00:00:00',
        'sites': [],
    }

    assert get_bol_stamps(storage, stamps=empty_stamps) == {}

    assert caplog.text == ''


def test_get_bol_stamps_unknown_company(storage, caplog):
    project3 = factories.create_default_project(storage, tax_id='PROJECT-TAX-ID-3')

    assert get_bol_stamps(
        storage, stamps=PROJECT_STAMPS, projects=[project3]
    ) == {project3['id']: set()}

    assert 'Failed to find stamp company: *******-***_***_id-3' in caplog.text

    assert 'Failed to find stamp company: *******-***_***_id-1' not in caplog.text
    assert 'Failed to find stamp company: *******-***_***_id-2' not in caplog.text


def test_process_stamps_project(storage, set_feature_flags):
    set_feature_flags({'visitors': True})

    project1 = factories.create_default_project(storage, tax_id='project-tax-id-1')
    s1 = factories.create_org(storage, 'S1', registration_id='company-gov_org_id-1')
    factories.create_org(storage, 'S2', registration_id='company-gov_org_id-2')
    v3 = factories.create_org(storage, 'V3', registration_id='company-gov_org_id-3')
    factories.create_org(storage, 'V4', registration_id='company-gov_org_id-4')
    u5 = factories.create_org(storage, 'U5', registration_id='company-gov_org_id-5')

    supplier = factories.supplier
    factories.create_suppliers_tree(storage, project1, [
        supplier('S1', subcontractors=[
            supplier('S2'),
        ]),
        supplier('U5', supplier_type=UNLINKED),
        supplier('V4', supplier_type=VISITOR, visitor_type=RAW),
    ])

    tree = get_project_tree(storage, project1)
    assert str(tree) == textwrap.dedent('''\
        Stockholm project
        ├── Linked suppliers
        │   └── S1
        │       └── S2
        ├── Unlinked suppliers
        │   └── U5
        └── Visitors
            └── V4
    ''')

    # Visitor V3, supplier S1, and supplier U5 companies visited project1.
    # [These should be returned by `get_bol_stamps()`.]
    bol_stamps = {s1['id'], v3['id'], u5['id']}

    first_visited = datetime.datetime.fromisoformat('2020-10-01')
    last_visited = datetime.datetime.fromisoformat('2020-10-15')

    # Update visitors
    process_stamps_project(storage, project1['id'], bol_stamps, first_visited)
    process_stamps_project(storage, project1['id'], bol_stamps, last_visited)

    # Visitors V3 and U5 were created, but supplier S1 was not registered as a visitor
    tree = get_project_tree(storage, project1)
    assert str(tree) == textwrap.dedent('''\
        Stockholm project
        ├── Linked suppliers
        │   └── S1
        │       └── S2
        ├── Unlinked suppliers
        │   └── U5
        └── Visitors
            ├── U5
            ├── V3
            └── V4
    ''')

    visitor_types = {
        node.tag: node.data['visitor_type']
        for node in tree.all_nodes()
        if node.data and node.data.get('supplier_type') == VISITOR
    }
    assert visitor_types == {
        'U5': RAW,
        'V3': RAW,
        'V4': RAW,
    }

    # stamp were updated only for visitor V3 and supplier U5 company stamps
    stamps = get_project_stamps(storage, project1['id'])
    stamps['companies'].sort(key=itemgetter('company_id'))

    assert stamps == {
        'project_id': project1['id'],
        'companies': sorted([
            {
                'company_id': v3['id'],
                'first_visited': ['2020-10-01T00:00:00'],
                'last_visited': ['2020-10-15T00:00:00'],
            },
            # Note that U5 gets the stamp updated twice, because it shows up in
            # the project tree twice!  I'm not 100% sure that is desired.
            {
                'company_id': u5['id'],
                'first_visited': ['2020-10-01T00:00:00'],
                'last_visited': ['2020-10-15T00:00:00'],
            },
            {
                'company_id': u5['id'],
                'first_visited': ['2020-10-01T00:00:00'],
                'last_visited': ['2020-10-15T00:00:00'],
            },
        ], key=itemgetter('company_id')),
    }


def test_process_stamps_project_empty(storage, caplog):
    project1 = factories.create_default_project(storage, tax_id='project-tax-id-1')
    date = datetime.datetime.fromisoformat('2020-10-15')
    bol_stamps = set()

    # Update suppliers
    process_stamps_project(storage, project1['id'], bol_stamps, date)

    assert caplog.text == ''


@pytest.mark.parametrize('date', [
    datetime.datetime.fromisoformat('2020-10-15'),
    datetime.date.fromisoformat('2020-10-15'),
])
def test_process_stamps_project_date(storage, caplog, date):
    project1 = factories.create_default_project(storage, tax_id='project-tax-id-1')

    bol_stamps = set()

    process_stamps_project(storage, project1['id'], bol_stamps, date)
    assert caplog.text == ''


@pytest.mark.parametrize('date', [
    'None-Such',
    '2020-10-15',
    '2020-10-15T00:00:00',
])
def test_process_stamps_project_date_fails(storage, date):
    project1 = factories.create_default_project(storage, tax_id='project-tax-id-1')

    bol_stamps = {'test-org-id'}

    # Update suppliers
    with pytest.raises(ValueError) as e:
        process_stamps_project(storage, project1['id'], bol_stamps, date)

    assert e.value.args == (
        'Expected date or datetime when adding stamps but got %s', date)


def test_process_stamps_project_missing_companies(storage, caplog):
    project1 = factories.create_default_project(storage, tax_id='project-tax-id-1')

    bol_stamps = set()

    date = datetime.datetime.fromisoformat('2020-10-15')

    # Update suppliers
    process_stamps_project(storage, project1['id'], bol_stamps, date)

    assert caplog.text == ''


def test_get_stamp_project_ids_filtering(storage, caplog):
    caplog.set_level(logging.INFO)

    tax_id1 = 'PL00000000000'
    tax_id2 = 'PL00000000001'
    tax_id3 = 'non-valid'
    project1 = factories.create_default_project(storage, name='test 1', tax_id=tax_id1)
    project2 = factories.create_default_project(storage, name='test 2', tax_id=tax_id2)
    project3 = factories.create_default_project(storage, name='test 3', tax_id=tax_id3)
    projects = [project1, project2, project3]

    # project 1 - filtered out
    # project 2 - in result
    # project 3 - invalid ID, skipped
    assert get_stamp_project_ids(projects, [tax_id2]) == [
        {
            'project': tax_id2,
            'country': 'SWE'
        },
    ]

    assert 'Stamps computing project tax_id for 3 project(s)' in caplog.text
    assert 'Stamps computed project tax_id for 1 project(s)' in caplog.text


def test_process_stamps_active_projects_only(storage, stamp):
    factories.create_default_project(
        storage, name='Draft project', state='draft', tax_id='TaxID1'
    )

    factories.create_default_project(
        storage, name='Closed project', state='closed', tax_id='TaxID2'
    )
    assert process_stamps(storage, stamp, '2020-02-01') == []


def test_get_stamps_from_client_siteid_upercase(stamp, mocker):
    project_tax_ids = [
        {
            'project': 'test-site-id-1',
            'country_code': 'SE'
        }
    ]
    mocked_call = mocker.patch.object(stamp, 'get_companies')
    get_stamps_from_client(stamp, project_tax_ids, None)
    mocked_call.assert_called_with([{'site_id': 'TEST-SITE-ID-1', 'country_code': 'SE'}], None)


def test_get_stamps_from_client_one_at_a_time(stamp, mocker, set_feature_flags):
    set_feature_flags({'stamp_workaround_one_site_at_a_time': True})
    project_tax_ids = [
        {
            'project': 'site-id-1',
            'country_code': 'SE',
        },
        {
            'project': 'site-id-2',
            'country_code': 'SE',
        },
        {
            'project': 'site-id-3',
            'country_code': 'SE',
        }
    ]
    mocked_call = mocker.patch.object(stamp, 'get_companies')
    mocked_call.side_effect = [
        MockResponse(json={'date': '2021-03-19', 'sites': {'site-id-1': '... site 1 data ...'}}),
        HTTPError(404, response=MockResponse(json={'error': 'no such site'})),
        MockResponse(json={'date': '2021-03-19', 'sites': {'site-id-3': '... site 3 data ...'}}),
    ]
    stamps = get_stamps_from_client(stamp, project_tax_ids, None)
    assert stamps == {
        'date': '2021-03-19',
        'sites': {
            'site-id-1': '... site 1 data ...',
            'site-id-3': '... site 3 data ...',
        },
    }


def test_process_stamps_project_visitors_with_suppliers_not_updated(storage, mocker):
    # set_feature_flags({'visitors': True})

    update_project_stamps_mock = mocker.patch('bolfak.services.stamp.update_project_stamps')

    project = factories.create_default_project(storage, tax_id='project-tax-id-1')
    # this compamy has supplier and visitor in the project
    sv = factories.create_org(storage, 'sv', registration_id='company-gov_org_id-1')
    # this company has only visitor
    v = factories.create_org(storage, 'V', registration_id='company-gov_org_id-3', country='SE')

    supplier = factories.supplier
    factories.create_suppliers_tree(storage, project, [
        supplier('sv'),
        supplier('sv', supplier_type=VISITOR),
        supplier('v', supplier_type=VISITOR),
    ])

    date = datetime.date(2020, 12, 23)

    # getting one stamp for existing visitor and supplier
    bol_stamps = {sv['id']}
    process_stamps_project(storage, project['id'], bol_stamps, date)
    assert not update_project_stamps_mock.called

    # getting stamps for existing and new visitor
    bol_stamps = {sv['id'], v['id']}
    process_stamps_project(storage, project['id'], bol_stamps, date)
    update_project_stamps_mock.assert_called_with(storage, project['id'], date,
                                                  company_ids=[v['id']])


@pytest.mark.parametrize(('pa_status', 'updated_company'), [
    (PA_STATUS_CREATED, 'S2'),
    (PA_STATUS_REJECTED, 'S2'),
    (PA_STATUS_REGISTERED, 'S2'),
    (PA_STATUS_CONFIRMED, None),
])
def test_process_stamps_project_supplier_preannoucement_not_confirmed(
        storage, pa_status, updated_company, set_feature_flags):
    set_feature_flags({'visitors': True})

    project = factories.create_default_project(storage, tax_id='project-tax-id-1')
    s1 = factories.create_org(storage, 'S1', registration_id='company-gov_org_id-1')
    s2 = factories.create_org(storage, 'S2', registration_id='company-gov_org_id-2')
    org_id_by_name = {'S2': s2['id']}

    supplier = factories.supplier
    tree = factories.create_suppliers_tree(storage, project, [
        supplier('S1', subcontractors=[
            supplier('S2'),
        ]),
    ])

    # Set PA status for the preannouced supplier 'S2'
    factories.create_pa(storage, project, tree, 'S1', 'S2', status=pa_status)

    tree = get_project_tree(storage, project)
    assert str(tree) == textwrap.dedent('''\
        Stockholm project
        ├── Linked suppliers
        │   └── S1
        │       └── S2
        ├── Unlinked suppliers
        └── Visitors
    ''')

    # supplier S1, and S2 companies visited project.
    # [These should be returned by `get_bol_stamps()`.]
    bol_stamps = {s1['id'], s2['id']}
    last_visited = datetime.datetime.fromisoformat('2022-07-01')

    # Update visitors
    updated = process_stamps_project(storage, project['id'], bol_stamps,
                                     last_visited)

    if updated_company:
        # Visitor was created
        tree = get_project_tree(storage, project)
        assert str(tree) == textwrap.dedent('''\
            Stockholm project
            ├── Linked suppliers
            │   └── S1
            │       └── S2
            ├── Unlinked suppliers
            └── Visitors
                └── S2
        ''')
        # Visitor stamp was updated
        assert updated == [org_id_by_name[updated_company]]

    elif updated_company is None:
        # Visitor was NOT created
        tree = get_project_tree(storage, project)
        assert str(tree) == textwrap.dedent('''\
            Stockholm project
            ├── Linked suppliers
            │   └── S1
            │       └── S2
            ├── Unlinked suppliers
            └── Visitors
        ''')
        # No visitor to update visitor stamps
        assert updated == []


def test_process_stamps(storage, mocker, caplog):
    caplog.set_level(logging.INFO)

    org = factories.create_org(storage, 'Test org',
                               registration_id='556643-9070', country='SE')
    project1 = factories.create_default_project(storage, 'Project1',
                                                tax_id='PL11111111111')
    project2 = factories.create_default_project(storage, 'Project2',
                                                tax_id='PL22222222222')

    stamps_from_client = {
        'date': '2022-07-07',
        'sites': {
            'PL11111111111': [
                {
                    'company_registration_number': '556643-9070',
                    'country_code': 'SE'
                }
            ],
            'PL22222222222': [
                {
                    'company_registration_number': '556643-9070',
                    'country_code': 'SE'
                }
            ],
        }
    }
    mocker.patch('bolfak.services.stamp.get_stamps_from_client',
                 return_value=stamps_from_client)

    actual = process_stamps(storage, 'dummy_stamp_client',
                            datetime.datetime(2020, 7, 7))

    assert actual == [
        {'project_id': project1['id'], 'company_ids': [org['id']]},
        {'project_id': project2['id'], 'company_ids': [org['id']]},
    ]
    assert f'Visitors updating done for project {project1["id"]}' in caplog.text
    assert f'Visitors updating done for project {project2["id"]}' in caplog.text


def test_process_stamps_no_projects_to_update(storage, caplog):
    caplog.set_level(logging.INFO)

    actual = process_stamps(storage, 'dummy_stamp_client', 'dummy_date')

    assert actual == []
    assert 'Visitors found no active projects, will stop' in caplog.text


def test_process_stamps_no_stamps(storage, mocker, caplog):
    caplog.set_level(logging.INFO)

    factories.create_org(storage, 'Test org', registration_id='556643-9070',
                         country='SE')
    factories.create_default_project(storage, 'Project1',
                                     tax_id='PL11111111111')

    stamps_from_client = {}
    mocker.patch('bolfak.services.stamp.get_stamps_from_client',
                 return_value=stamps_from_client)

    actual = process_stamps(storage, 'dummy_stamp_client',
                            datetime.datetime(2020, 7, 7))

    assert actual == []
    assert ('Visitors processing done: '
            'processed 1 projects, '
            'retrieved 0 sites that had visitors, '
            'prepared to update 0 projects, '
            'updated stamps for 0 projects, '
            'found only legitimate attendees in 0 projects.' in caplog.text)


def test_process_stamps_visitors_not_updatable(storage, mocker, caplog):
    caplog.set_level(logging.INFO)

    factories.create_org(storage, 'Test org', registration_id='556643-9070',
                         country='SE')
    project = factories.create_default_project(storage, 'Project',
                                               tax_id='PL11111111111')
    factories.create_suppliers_tree(storage, project, [
        factories.supplier('Test org')
    ])

    stamps_from_client = {
        'date': '2022-07-07',
        'sites': {
            'PL11111111111': [
                {
                    'company_registration_number': '556643-9070',
                    'country_code': 'SE'
                }
            ],
        }
    }
    mocker.patch('bolfak.services.stamp.get_stamps_from_client',
                 return_value=stamps_from_client)

    actual = process_stamps(storage, 'dummy_stamp_client',
                            datetime.datetime(2020, 7, 7))

    assert actual == []
    assert ('Visitors updating found only legitimate attendee stamps '
            f'in project {project["id"]}') in caplog.text
    assert ('Visitors processing done: '
            'processed 1 projects, '
            'retrieved 1 sites that had visitors, '
            'prepared to update 1 projects, '
            'updated stamps for 0 projects, '
            'found only legitimate attendees in 1 projects.' in caplog.text)


def test_process_stamps_subscription(storage, mocker, caplog, set_feature_flags):
    set_feature_flags({'visitors': True, 'core_mitt_id06': True})
    caplog.set_level(logging.INFO)

    org = factories.create_org(storage, 'Test org',
                               registration_id='556643-9070', country='SE',
                               create_organisation=True)
    project1 = factories.create_default_project(storage, 'Project1',
                                                tax_id='PL11111111111')
    project2 = factories.create_default_project(storage, 'Project2',
                                                tax_id='PL22222222222')

    stamps_from_client = {
        'date': '2022-07-07',
        'sites': {
            'PL11111111111': [
                {
                    'company_registration_number': '556643-9070',
                    'country_code': 'SE'
                }
            ],
            'PL22222222222': [
                {
                    'company_registration_number': '556643-9070',
                    'country_code': 'SE'
                }
            ],
        }
    }
    mocker.patch('bolfak.services.stamp.get_stamps_from_client',
                 return_value=stamps_from_client)

    actual = process_stamps(storage, 'dummy_stamp_client',
                            datetime.datetime(2020, 7, 7))

    assert actual == [
        {'project_id': project1['id'], 'company_ids': [org['id']]},
        {'project_id': project2['id'], 'company_ids': [org['id']]},
    ]
    assert f'Visitors updating done for project {project1["id"]}' in caplog.text
    assert f'Visitors updating done for project {project2["id"]}' in caplog.text
    # assert visitor org subscription was created
    assert f'Supplier company {org["id"]} has been subscribed' in caplog.text
    org_subscribed = factories.get_org(storage, org['id'])
    assert org_subscribed.get('bol_subscription') is True


def test_process_stamps_project_visitors_with_suppliers_not_updated_subscription(
    storage, mocker, set_feature_flags, caplog
):
    set_feature_flags({'visitors': True, 'core_mitt_id06': True})
    caplog.set_level(logging.INFO)

    update_project_stamps_mock = mocker.patch('bolfak.services.stamp.update_project_stamps')

    project = factories.create_default_project(storage, tax_id='project-tax-id-1')
    # this compamy has supplier and visitor in the project
    sv = factories.create_org(storage, 'sv', registration_id='company-gov_org_id-1',
                              create_organisation=True)
    # this company has only visitor
    v = factories.create_org(storage, 'V', registration_id='company-gov_org_id-3', country='SE',
                             create_organisation=True)
    n = factories.create_org(storage, 'N', registration_id='company-gov_org_id-4', country='SE',
                             create_organisation=True)
    supplier = factories.supplier
    factories.create_suppliers_tree(storage, project, [
        supplier('sv'),
        supplier('sv', supplier_type=VISITOR),
        supplier('v', supplier_type=VISITOR),
        supplier('n', supplier_type=VISITOR),
    ])

    date = datetime.date(2020, 12, 23)

    # getting one stamp for existing visitor and supplier
    bol_stamps = {sv['id']}
    process_stamps_project(storage, project['id'], bol_stamps, date)
    assert not update_project_stamps_mock.called

    # getting stamps for existing and new visitor
    bol_stamps = {sv['id'], v['id']}
    process_stamps_project(storage, project['id'], bol_stamps, date)
    update_project_stamps_mock.assert_called_with(storage, project['id'], date,
                                                  company_ids=[v['id']])
    # assert visitor org subscription was created
    assert f'Supplier company {v["id"]} has been subscribed' in caplog.text
    org_sv = factories.get_org(storage, sv['id'])
    assert org_sv.get('bol_subscription') is None
    org_v = factories.get_org(storage, v['id'])
    assert org_v.get('bol_subscription') is True

    # mock subscription failure
    def def_mock_subscribe(storage, org_id):
        if org_id == n['id']:
            raise DataSubscriptionError('Company subscription by internal id has failed')
        return subscribe_company_by_internal_id(storage, org_id)

    mocker.patch('bolfak.services.stamp.subscribe_company_by_internal_id',
                 def_mock_subscribe)

    # getting stamps for another new visitor
    bol_stamps = {n['id']}
    process_stamps_project(storage, project['id'], bol_stamps, date)
    update_project_stamps_mock.assert_called_with(storage, project['id'], date,
                                                  company_ids=[n['id']])
    # assert visitor org subscription was created
    assert f'Supplier company {n["id"]} subscription has failed' in caplog.text
    assert f'Supplier company {n["id"]} has been subscribed' not in caplog.text


def test_process_stamps_should_not_add_client(storage, mocker, caplog):
    caplog.set_level(logging.INFO)

    org = factories.create_org(storage, 'Test org',
                               registration_id='556643-9070', country='SE')
    project1 = factories.create_default_project(storage, 'Project1',
                                                tax_id='PL11111111111',
                                                client_company_id=org['id']
                                                )

    stamps_from_client = {
        'date': '2022-07-07',
        'sites': {
            'PL11111111111': [
                {
                    'company_registration_number': '556643-9070',
                    'country_code': 'SE'
                }
            ],
        }
    }
    mocker.patch('bolfak.services.stamp.get_stamps_from_client',
                 return_value=stamps_from_client)
    actual = process_stamps(storage, 'dummy_stamp_client',
                            datetime.datetime(2020, 7, 7))

    assert actual == []
    assert f'Visitors updating found only legitimate attendee stamps in project {project1["id"]}'\
        in caplog.text


def test_process_stamps_project_fetch_reports_for_new_visitors_whitelisted(
        storage, mocker, config):
    """Test that reports are fetched for new visitors when whitelisted orgs have permission."""
    # Set up whitelist config
    whitelisted_org = factories.create_org(storage, 'Whitelisted Org')
    config.set('monitoring', 'visitor_whitelist_org_ids', whitelisted_org['id'])

    project = factories.create_default_project(storage, org=whitelisted_org)
    visitor_org = factories.create_org(storage, 'Visitor Org', registration_id='visitor-gov-id')

    # Create project user for whitelisted org to enable permission
    person = factories.create_person(storage, 'Test Person')
    user_contract = factories.create_user_account(storage, person, whitelisted_org)
    factories.create_project_user_contract(storage, project, user_contract, whitelisted_org)

    # Mock start_project_status_update to verify it's called
    mock_start = mocker.patch('bolfak.services.stamp.start_project_status_update')

    # Process stamps with new visitor
    bol_stamps = {visitor_org['id']}
    process_stamps_project(storage, project['id'], bol_stamps, datetime.datetime(2020, 10, 15))

    # Verify that start_project_status_update was called for the whitelisted org
    mock_start.assert_called_once_with(storage, [visitor_org['id']], project)


def test_process_stamps_project_no_reports_for_visitors_not_whitelisted(storage, mocker, config):
    """Test that reports are not fetched for visitors when org is not whitelisted."""
    # Set up whitelist config with different org
    whitelisted_org = factories.create_org(storage, 'Whitelisted Org')
    config.set('monitoring', 'visitor_whitelist_org_ids', whitelisted_org['id'])

    project = factories.create_default_project(storage, org=whitelisted_org)
    visitor_org = factories.create_org(storage, 'Visitor Org', registration_id='visitor-gov-id')

    # Create project user for different org (not whitelisted)
    non_whitelisted_org = factories.create_org(storage, 'Non Whitelisted Org')
    person = factories.create_person(storage, 'Test Person')
    user_contract = factories.create_user_account(storage, person, non_whitelisted_org)
    factories.create_project_user_contract(storage, project, user_contract, non_whitelisted_org)

    # Mock start_project_status_update to verify it's NOT called
    mock_start = mocker.patch('bolfak.services.stamp.start_project_status_update')

    # Process stamps with new visitor
    bol_stamps = {visitor_org['id']}
    process_stamps_project(storage, project['id'], bol_stamps, datetime.datetime(2020, 10, 15))

    # Verify that start_project_status_update was NOT called
    mock_start.assert_not_called()


def test_process_stamps_project_no_reports_for_visitors_whitelisted_no_permission(
        storage, mocker, config):
    """Test that reports are not fetched for visitors when whitelisted org has no project users."""
    # Set up whitelist config
    whitelisted_org = factories.create_org(storage, 'Whitelisted Org')
    config.set('monitoring', 'visitor_whitelist_org_ids', whitelisted_org['id'])

    project = factories.create_default_project(storage, org=whitelisted_org)
    visitor_org = factories.create_org(storage, 'Visitor Org', registration_id='visitor-gov-id')

    # Do NOT create project user for whitelisted org (no permission)

    # Mock start_project_status_update to verify it's NOT called
    mock_start = mocker.patch('bolfak.services.stamp.start_project_status_update')

    # Process stamps with new visitor
    bol_stamps = {visitor_org['id']}
    process_stamps_project(storage, project['id'], bol_stamps, datetime.datetime(2020, 10, 15))

    # Verify that start_project_status_update was NOT called
    mock_start.assert_not_called()
