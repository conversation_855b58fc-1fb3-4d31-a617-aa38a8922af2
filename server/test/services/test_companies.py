import pytest

from operator import itemgetter
from unittest import mock

from bolfak.exceptions import DataDuplicatesFoundError, DataNotFoundError
from bolfak.models import (
    CLIENT_ROLE,
    MAIN_CONTRACTOR_ROLE,
    PROJECT_USER_ROLE_MEMBER,
    STATUS_ATTENTION,
    STATUS_OK,
    STATUS_INCOMPLETE,
    STATUS_STOP,
    SUPERVISOR_ROLE,
    SUPPLIER_ROLE,
    USER_ROLE_MAIN,
    USER_ROLE_BASIC,
    get_gov_org_id,
    get_org_name,
    GOV_ORG_ID_VALIDATOR_MODULE,
    BOL_STATUS_NOT,
    VISITOR,
)
from bolfak.fixtures import factories
from bolfak.fixtures.factories import (
    make_reg_number,
    make_vat,
)
from bolfak.services.companies import (
    clean_gov_org_id,
    find_companies_by_reg_no_prefix,
    find_company_by_reg_no,
    get_all_bolagsfakta_org_ids,
    get_company_list,
    get_company_related_projects,
    get_company_user_info_by_email_from_core,
    normalize_gov_org_id,
    get_company_related_project_ids_for_org,
)
from bolfak.storage.company import get_org
from bolfak.storage.qvarn.company import delete_org, get_orgs_by_name_fragment
from bolfak.storage.qvarn.projects import DRAFT, ACTIVE, CLOSED
from bolfak.storage.qvarn.suppliers import get_orgs_suppliers

pytestmark = pytest.mark.usefixtures(
    "person_id_for_project_users_feature_flag_enabled_and_disabled"
)


@pytest.fixture
def org_and_projects(storage, set_feature_flags):
    set_feature_flags(
        {
            "pre_announcements": True,
        }
    )
    org = factories.create_org(storage, "SupplierOrg")
    user_org = factories.create_org(storage, "UserOrg")
    user_contract = factories.get_or_create_user_account(storage, user_org, "Test user")
    # Create four projects, one where user_org is responsible, one supervisor, one
    # PA where the supplier is a subsupplier, and one PA where the supplier is not a subsupplier
    p1 = factories.create_default_project(storage, "Proj1", org=user_org)
    p2 = factories.create_default_project(storage, "Proj2")
    p3 = factories.create_default_project(storage, "Proj3", pa_form_enabled=True)
    p4 = factories.create_default_project(storage, "Proj4", pa_form_enabled=True)
    factories.create_suppliers_tree(
        storage,
        p1,
        [factories.supplier("SupplierOrg")],
    )
    factories.create_suppliers_tree(
        storage,
        p2,
        [
            factories.supplier("SupplierOrg"),
            factories.supplier("UserOrg", supplier_role=SUPERVISOR_ROLE),
        ],
    )
    factories.create_suppliers_tree(
        storage,
        p3,
        [
            factories.supplier(
                "UserOrg", subcontractors=[factories.supplier("SupplierOrg")]
            ),
        ],
    )
    factories.create_suppliers_tree(
        storage,
        p4,
        [
            factories.supplier("SupplierOrg"),
            factories.supplier("UserOrg"),
        ],
    )
    return org, user_org, user_contract, [p1["id"], p2["id"], p3["id"], p4["id"]]


def test_get_all_bolagsfakta_org_ids_empty(qvarn):
    org_ids = get_all_bolagsfakta_org_ids(qvarn)
    assert org_ids == []


def test_get_all_bolagsfakta_org_ids_nonempty(storage):
    qvarn = storage.qvarn

    factories.create_org(storage, 'C0')
    C1 = factories.create_org(storage, 'C1')['id']
    C2 = factories.create_org(storage, 'C2')['id']
    C3 = factories.create_org(storage, 'C3')['id']
    C4 = factories.create_org(storage, 'C4')['id']
    factories.create_suppliers_tree(storage, 'Prj1', [
        factories.supplier('C1'),
        factories.supplier('C2', subcontractors=[
            factories.supplier('C3'),
        ]),
    ])
    factories.create_suppliers_tree(storage, 'Prj2', [
        factories.supplier('C1'),
        factories.supplier('C4'),
    ])
    org_ids = get_all_bolagsfakta_org_ids(qvarn)
    assert sorted(org_ids) == sorted([C1, C2, C3, C4])


@pytest.mark.feature_flags(
    '',
    # TODO: Switching to BOL-STAMP, someone should uncomment this and fix what is failing.
    # '+bda_company_list',
)
@pytest.mark.parametrize(
    ['country', 'expected_country'],
    [('SWE', 'SWE'),
     ('SE', 'SWE'),
     ('FIN', 'FIN'),
     ('FI', 'FIN'),
     ('', None),
     ('XX', None)])
def test_get_company_list_countries(
    storage,
    config,
    mocker,
    country,
    expected_country,
    feature_flag,
):
    org = factories.create_org(storage, 'C1', country=country)
    user_role = USER_ROLE_MAIN
    user_contract = factories.get_or_create_user_account(
        storage, org, 'Test user', user_role=user_role
    )
    factories.create_report(storage, 'C1', interested_org_id=org)
    query = mock.Mock(search=None, status=None)

    companies = get_company_list(
        storage,
        query=query,
        user_contract=user_contract,
        active_org_id=org['id'],
        user_role=USER_ROLE_MAIN,
    )
    expected = [{'company_id': org['gov_org_ids'][0]['gov_org_id'],
                 'vat_number': None,
                 'company_status': STATUS_OK,
                 'country': expected_country,
                 'has_combined_report': None,
                 'id': org['id'],
                 'name': 'C1',
                 'project_count': 0,
                 'report_available': True}]
    assert companies == expected


def test_get_company_list_project_count(storage, config, mocker):
    supplier = factories.supplier

    org = factories.create_org(storage, 'Test Company 0')
    factories.create_org(storage, 'Test Company 1', registration_id='TC-1001')
    factories.create_org(storage, 'Test Company 2', registration_id='TC-1002')
    factories.create_org(storage, 'Test Company 3', registration_id='TC-1003')
    p1 = factories.create_default_project(storage, 'Test project 1', org=org)
    p2 = factories.create_default_project(storage, 'Test project 2', org=org)
    factories.create_suppliers_tree(
        storage, p1, [supplier('Test Company 1', supplier_role=SUPERVISOR_ROLE)]
    )
    factories.create_suppliers_tree(
        storage, p2, [supplier('Test Company 1'),
                      supplier('Test Company 2')]
    )

    user_role = USER_ROLE_MAIN
    user_contract = factories.get_or_create_user_account(
        storage, org, 'Test user', user_role=user_role
    )
    mocker.patch('bolfak.services.companies.companies_have_reports',
                 return_value={org['id']: True})
    query = mock.Mock(search=None, status=None)

    companies = get_company_list(
        storage,
        query=query,
        user_contract=user_contract,
        active_org_id=org['id'],
        user_role=USER_ROLE_MAIN,
    )
    filtered = [
        {
            'name': c['name'],
            'project_count': c['project_count']
        }
        for c in companies
    ]
    assert sorted(filtered, key=itemgetter('name')) == [
        {'name': 'Test Company 0',
         'project_count': 0},
        {'name': 'Test Company 1',
         'project_count': 2},
        {'name': 'Test Company 2',
         'project_count': 1},
    ]


def test_get_company_list_project_count_visitor(storage, config, mocker):
    supplier = factories.supplier

    org = factories.create_org(storage, 'Test Company 0')
    factories.create_org(storage, 'Test Company 1', registration_id='TC-1001')
    factories.create_org(storage, 'Test Company 3', registration_id='TC-1003')
    p1 = factories.create_default_project(storage, 'Test project 1', org=org)
    p2 = factories.create_default_project(storage, 'Test project 2', org=org)

    # Add visitor nodes to projects
    factories.create_suppliers_tree(
        storage, p1, [supplier('Test Company 1', supplier_role=SUPERVISOR_ROLE),
                      supplier('Test Company 3', supplier_type=VISITOR)]
    )
    factories.create_suppliers_tree(
        storage, p2, [supplier('Test Company 3', supplier_type=VISITOR)]
    )

    # Some stubbing
    user_role = USER_ROLE_MAIN
    user_contract = factories.get_or_create_user_account(
        storage, org, 'Test user', user_role=user_role
    )
    mocker.patch('bolfak.services.companies.companies_have_reports',
                 return_value={org['id']: True})
    query = mock.Mock(search=None, status=None)

    companies = get_company_list(
        storage,
        query=query,
        user_contract=user_contract,
        active_org_id=org['id'],
        user_role=USER_ROLE_MAIN,
    )
    filtered = [
        {
            'name': c['name'],
            'project_count': c['project_count']
        }
        for c in companies
    ]
    # Visitors do not make it into the Companies list
    assert sorted(filtered, key=itemgetter('name')) == [
        {'name': 'Test Company 0',
         'project_count': 0},
        {'name': 'Test Company 1',
         'project_count': 1},
    ]


@pytest.mark.parametrize(
    ['report_present', 'report_available'],
    [(True, True),
     (False, False)])
def test_get_company_list_report(qvarn, storage, config, mocker,
                                 report_present, report_available):
    org = factories.create_org(storage, 'C1')
    user_contract = factories.get_or_create_user_account(storage, org, 'Test user')
    if report_present:
        factories.create_report(storage, 'C1', status=STATUS_INCOMPLETE,
                                interested_org_id=org)
    query = mock.Mock(search=None, status=None)

    companies = get_company_list(
        storage, query, user_contract, org['id'], user_role=USER_ROLE_MAIN
    )
    expected = [{'company_id': org['gov_org_ids'][0]['gov_org_id'],
                 'vat_number': None,
                 'company_status': STATUS_INCOMPLETE,
                 'country': 'SWE',
                 'has_combined_report': None,
                 'id': org['id'],
                 'name': 'C1',
                 'project_count': 0,
                 'report_available': report_available}]
    assert companies == expected


def get_company_list_results(
    storage,
    query,
    user_contract,
    active_org_id,
    user_role='main',
    is_admin=False,
    cols=None,
):
    """A helper to get results."""
    companies = get_company_list(
        storage,
        query,
        user_contract,
        active_org_id,
        user_role=user_role,
        is_admin=is_admin,
    )
    if cols is None:
        return companies
    return list(map(itemgetter(*cols), companies))


def test_company_list_users(storage):
    """This is an integrational test."""
    c1_gov_org_ids = [factories.make_reg_number('000000-C100'), factories.make_vat('V123456')]
    s1_gov_org_ids = [factories.make_reg_number('000000-S100'), factories.make_vat('V111111')]
    s11_gov_org_ids = [factories.make_reg_number('000000-S101'), factories.make_vat('V222222')]

    # Create a few companies.
    client = factories.create_org(storage, 'C1', gov_org_ids_raw=c1_gov_org_ids)
    supplier_S1 = factories.create_org(storage, 'S1', gov_org_ids_raw=s1_gov_org_ids)
    supplier_S1_1 = factories.create_org(storage, 'S1.1', gov_org_ids_raw=s11_gov_org_ids)

    # Does not belong to any project tree, nor is a project user.
    factories.create_org(storage, 'S8')

    # Create project trees.
    factories.create_suppliers_tree(storage, 'P1', org='C1', suppliers=[
        factories.supplier('S1', supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('S1.1')
        ]),
    ])
    factories.create_suppliers_tree(storage, 'P2', org='C1', suppliers=[
        factories.supplier('C1'),
    ])

    # Create a few users.
    factories.get_or_create_user_account(storage, 'Client', 'C1')
    factories.get_or_create_user_account(storage, 'Super', 'S1')
    supplier_user = factories.get_or_create_user_account(
        storage, 'Slade', 'S1', user_role=USER_ROLE_MAIN
    )
    factories.create_project_user_contract(
        storage, 'P1', supplier_user, 'S1', PROJECT_USER_ROLE_MEMBER)
    no_access_user = factories.get_or_create_user_account(storage, 'No access', 'S1')
    factories.get_or_create_user_account(storage, 'Admin', 'C1')

    # Create a couple of reports for an org, visible to all the users we've created.
    for user_org in ['C1', 'S1']:
        user_org_id = factories.get_or_create_org(storage, user_org)['id']
        factories.create_report(storage, 'S1', status=STATUS_OK, days_ago=2,
                                interested_org_id=user_org_id)
        factories.create_report(storage, 'S1', status=STATUS_ATTENTION, days_ago=3,
                                interested_org_id=user_org_id)

    query = mock.Mock(search='', status='')

    # Admin user can see all suppliers and responsible orgs.
    expected_full = [
        {'company_id': '000000-C100',
         'vat_number': 'V123456',
         'company_status': 'incomplete',
         'country': 'SWE',
         'has_combined_report': None,
         'id': client['id'],
         'name': 'C1',
         'project_count': 1,
         'report_available': False},
        {'company_id': '000000-S100',
         'vat_number': 'V111111',
         'company_status': 'ok',
         'country': 'SWE',
         'has_combined_report': None,
         'id': supplier_S1['id'],
         'name': 'S1',
         'project_count': 1,
         'report_available': True},
        {'company_id': '000000-S101',
         'vat_number': 'V222222',
         'company_status': 'incomplete',
         'country': 'SWE',
         'has_combined_report': None,
         'id': supplier_S1_1['id'],
         'name': 'S1.1',
         'project_count': 1,
         'report_available': False},
    ]
    full_result = get_company_list_results(
        storage, query, None, client['id'], is_admin=True,
    )
    assert expected_full == sorted(full_result, key=itemgetter('company_id'))

    # Admin user can see all suppliers and responsible orgs.
    expected_admin = [
        ('C1', 'incomplete', 1),
        ('S1.1', 'incomplete', 1),
        ('S1', 'ok', 1),
    ]
    result_admin = get_company_list_results(
        storage, query, None, client['id'], is_admin=True,
        cols=['name', 'company_status', 'project_count']
    )
    assert sorted(expected_admin) == sorted(result_admin)

    # Client user can see all companies in client's project, but not more.
    expected_client = [
        ('C1', 'incomplete', 1),
        ('S1.1', 'incomplete', 1),
        ('S1', 'ok', 1),
    ]
    result_client = get_company_list_results(
        storage, query, None, client['id'], user_role='main',
        cols=['name', 'company_status', 'project_count']
    )
    assert sorted(expected_client) == sorted(result_client)

    # Look as a supervisor user - S1 representative should see her company and
    # other suppliers in the project
    expected_supervisor = [
        ('S1.1', 'incomplete', 1),
        ('S1', 'ok', 1),
    ]
    result_supervisor = get_company_list_results(
        storage, query, None, supplier_S1['id'], user_role='main',
        cols=['name', 'company_status', 'project_count']
    )
    assert sorted(expected_supervisor) == sorted(result_supervisor)

    # Look as a project member: BASIC companhy user with project contract
    expected_project_user = [
        ('S1.1', 'incomplete', 1),
        ('S1', 'ok', 1),
    ]
    result_project_user = get_company_list_results(
        storage,
        query,
        supplier_user,
        supplier_S1['id'],
        user_role='basic',
        cols=['name', 'company_status', 'project_count'],
    )
    assert sorted(expected_project_user) == sorted(result_project_user)

    # Look as a basic user without permissions, can see own company
    expected_no_access_user = [
        ('S1', 'ok', 0)
    ]
    result_no_access_user = get_company_list_results(
        storage,
        query,
        no_access_user,
        supplier_S1['id'],
        user_role='basic',
        cols=['name', 'company_status', 'project_count'],
    )
    assert expected_no_access_user == result_no_access_user


def test_company_list_first_registration_number(storage):
    gov_org_ids = [make_reg_number('REG-1'), make_reg_number('REG-2'), make_reg_number('REG-3')]
    org = factories.create_org(storage, name='Org 1', gov_org_ids_raw=gov_org_ids)
    query = mock.Mock(search='', status='')

    assert get_company_list_results(
        storage, query, user_contract=None, active_org_id=org['id'], cols=['company_id']
    ) == [('REG-1')]


def test_company_list_first_vat_number(storage):
    gov_org_ids = [make_vat('11111'), make_vat('22222'), make_vat('3333')]
    org = factories.create_org(storage, name='Org 1', gov_org_ids_raw=gov_org_ids)
    query = mock.Mock(search='', status='')

    assert get_company_list_results(
        storage, query, user_contract=None, active_org_id=org['id'], cols=['vat_number']
    ) == [('11111')]


def test_company_list_first_registration_number_mixed_types(storage):
    gov_org_ids = [make_vat('123456'), make_reg_number('REG-1')]
    org = factories.create_org(storage, name='Org 1', gov_org_ids_raw=gov_org_ids)

    query = mock.Mock(search='', status='')

    assert get_company_list_results(
        storage,
        query,
        user_contract=None,
        active_org_id=org['id'],
        cols=['company_id', 'vat_number'],
    ) == [('REG-1', '123456')]


def test_company_list_filter_search_by_vat(storage):
    gov_org_ids = [make_reg_number('REG-111'), make_vat('REG-559')]

    factories.create_org(storage, 'A company', gov_org_ids_raw=gov_org_ids)
    factories.create_org(storage, 'B company', registration_id='REG-715')
    factories.create_org(storage, 'C company', registration_id='REG-559')

    org = factories.create_org(storage, 'FB company', registration_id='REG-291')
    project = factories.create_default_project(storage, 'A project', org=org)
    factories.create_suppliers_tree(
        storage, project,
        suppliers=[
            factories.supplier('A company'),
            factories.supplier('B company'),
            factories.supplier('C company'),
        ]
    )
    # Exact filter ID
    query = mock.Mock(search='g-5', status='')
    actual = get_company_list_results(
        storage,
        query,
        user_contract=None,
        active_org_id=org['id'],
        cols=['name', 'company_id'],
    )
    expected = [
        ('A company', 'REG-111'),
        ('C company', 'REG-559'),
    ]
    assert sorted(expected) == sorted(actual)

    # Exact filter name
    query = mock.Mock(search='b', status='')
    actual = get_company_list_results(
        storage,
        query,
        user_contract=None,
        active_org_id=org['id'],
        cols=['name', 'company_id'],
    )
    expected = [
        ('B company', 'REG-715'),
        ('FB company', 'REG-291'),
    ]
    assert sorted(expected) == sorted(actual)


def test_get_company_related_projects_consider_not_project_responsible_org(storage, config):
    org = factories.create_org(storage, 'C1')
    user_contract = factories.get_or_create_user_account(storage, org, 'Test user')
    factories.create_default_project(storage, 'A Project', org=org, project_leader=user_contract)
    projects = get_company_related_projects(
        storage,
        org,
        user_contract,
        is_admin=False,
        active_org_id=org['id'],
        user_role=USER_ROLE_MAIN,
    )
    assert {p['name'] for p in projects} == set([])


def test_get_company_related_projects_shows_active_and_draft_projects(storage, config):
    org = factories.create_org(storage, 'C1')
    user_contract = factories.get_or_create_user_account(storage, org, 'Test user')
    factories.create_default_project(
        storage, 'Draft Project', state=DRAFT, project_leader=user_contract
    )
    factories.create_default_project(
        storage, 'Active Project', state=ACTIVE, project_leader=user_contract
    )
    factories.create_default_project(
        storage, 'Closed Project', state=CLOSED, project_leader=user_contract
    )
    factories.create_suppliers_tree(storage, 'Draft Project', [
        factories.supplier('C1'),
    ])
    factories.create_suppliers_tree(storage, 'Active Project', [
        factories.supplier('C1'),
    ])
    factories.create_suppliers_tree(storage, 'Closed Project', [
        factories.supplier('C1'),
    ])
    projects = get_company_related_projects(
        storage, org, user_contract, is_admin=True, active_org_id=org
    )
    assert {p['name'] for p in projects} == {'Active Project', 'Draft Project'}


@pytest.mark.parametrize(('confirmed', 'status_shown'), [
    (None, None),
    (False, None),
    (True, STATUS_INCOMPLETE)
])
def test_get_company_related_projects_hide_added_client_not_confirmed(
        storage, config, confirmed, status_shown):

    org = factories.create_org(storage, 'C1')
    user_contract = factories.get_or_create_user_account(storage, org, 'Test user')
    factories.create_default_project(storage, 'Draft Project', org=org,
                                     project_leader=user_contract,
                                     project_creator_role=MAIN_CONTRACTOR_ROLE,
                                     added_client_confirmed=confirmed)
    factories.create_suppliers_tree(storage, 'Draft Project', [
        factories.supplier('C1', supplier_role=MAIN_CONTRACTOR_ROLE),
    ])

    projects = get_company_related_projects(
        storage, org, user_contract, active_org_id=org['id'], user_role=USER_ROLE_MAIN
    )
    assert {p['status'] for p in projects} == set([status_shown])


def test_clean_gov_org_id(qvarn):
    assert clean_gov_org_id(' 555555-5555 ') == '555555-5555'

    # https://en.wikipedia.org/wiki/Dash#Common_dashes
    assert clean_gov_org_id('555555‒5555') == '555555-5555'   # U+2012 (figure dash)
    assert clean_gov_org_id('555555–5555') == '555555-5555'   # U+2013 (en dash)
    assert clean_gov_org_id('555555—5555') == '555555-5555'  # U+2014 (em dash)
    assert clean_gov_org_id('555555―5555') == '555555-5555'  # U+2015 (horizontal bar)
    assert clean_gov_org_id('555555⁓5555') == '555555~5555'   # U+2053 (swung dash)


@pytest.mark.parametrize(('exact_id', 'status_code', 'error'),
                         [('11111111111', 404, DataNotFoundError),
                          ('222222', 404, DataNotFoundError)])
def test_find_company_by_reg_no_raises(
    config, mocker, storage, exact_id, status_code, error
):
    factories.create_org(storage, registration_id='111111-1111')

    with pytest.raises(error):
        find_company_by_reg_no(storage, exact_id)


@pytest.mark.parametrize(
    ['exact_id', 'expected'],
    [('111111-1111', '111111-1111'),
     ('1111111111', '111111-1111')])
def test_find_company_by_reg_no_normalize(config, mocker, storage, exact_id, expected):
    org = factories.create_org(storage, registration_id='111111-1111')

    org = find_company_by_reg_no(storage, exact_id)
    gov_org_id = get_gov_org_id(org) if org else None
    assert gov_org_id == expected


@pytest.mark.parametrize(
    ['prefix', 'expected'],
    [('111111-1111', '111111-1111'),
     ('1111111111', '111111-1111'),
     ('111111111111', '111111-1111'),
     ('1111111111111', None),
     ('111111', '111111-1111')])
def test_find_companies_by_reg_no_prefix_normalize_SE(config, prefix,
                                                      expected, storage):
    org = factories.create_org(storage, registration_id='111111-1111',
                               country='SE')

    orgs = find_companies_by_reg_no_prefix(storage, prefix, country_code='SE')
    org = orgs and orgs[0]
    gov_org_id = get_gov_org_id(org) if org else None
    assert gov_org_id == expected


@pytest.mark.parametrize(
    ['prefix', 'expected'],
    [('111111-1111', None),
     ('1111111111', '1111111111'),
     ('1111111111111', None),
     ('111111', '1111111111')])
def test_find_companies_by_reg_no_prefix_normalize_PL(config, prefix,
                                                      expected, storage):
    org = factories.create_org(storage, registration_id='1111111111',
                               country='PL')

    orgs = find_companies_by_reg_no_prefix(storage, prefix, country_code='PL')
    org = orgs and orgs[0]
    gov_org_id = get_gov_org_id(org) if org else None
    assert gov_org_id == expected


@pytest.mark.parametrize(
    ['exact_id', 'country_name', 'expected'],
    [('111111-1111', 'SWE', '111111-1111')]
)
def test_find_company_by_reg_no_country(config, mocker, exact_id, country_name, expected,
                                        storage):
    org = factories.create_org(storage, registration_id='111111-1111')
    org = find_company_by_reg_no(storage, exact_id)
    gov_org_id = get_gov_org_id(org) if org else None
    assert gov_org_id == expected
    assert org["country"] == country_name


@pytest.mark.parametrize(
    ['id', 'country', 'expected'],
    [('111111-1111', 'AZE', '111111-1111')])
def test_find_company_by_reg_no_country_notfound(config, mocker, id, country, expected,
                                                 storage):
    org = factories.create_org(storage, registration_id='111111-1111')
    org = find_company_by_reg_no(storage, id)
    assert org["country"] != country


@pytest.mark.parametrize(
    ['prefix', 'expected'],
    [('Org', ['Org 100000', 'Org 110000', 'Org 111111']),
     ('Org 11', ['Org 110000', 'Org 111111']),
     ('Org 111', ['Org 111111']),
     ('X', [])])
def test_find_companies_by_name_prefix(prefix, expected, storage):
    factories.create_org(storage, name='Org 111111')
    factories.create_org(storage, name='Org 110000')
    factories.create_org(storage, name='Org 100000')

    orgs = sorted(get_orgs_by_name_fragment(storage, prefix), key=itemgetter('names'))
    org_names = [get_org_name(org) for org in orgs]
    assert org_names == expected


@pytest.mark.parametrize(
    ['gov_org_id', 'service_provider', 'country'],
    [('111111-1111', None, None),
     ('111111-1111', 'CORP', None),
     ('111111-1111', None, 'fi'),
     ('111111-1111', 'CORP', 'fi'),
     ('222222-2222', None, 'se'),
     ('222222-2222', None, 'fi')])
def test_get_company(storage, gov_org_id, service_provider, country):
    factories.create_org(storage,
                         registration_id='111111-1111',
                         service_provider='CORP',
                         country='fi')
    factories.create_org(storage, registration_id='222222-2222', country='fi')
    factories.create_org(storage, registration_id='222222-2222', country='se')
    factories.create_org(storage, registration_id='333333-3333', service_provider='CORP')
    factories.create_org(storage, registration_id='444444-4444')

    org = get_org(
        storage,
        gov_org_id=gov_org_id,
        service_provider=service_provider,
        country=country)
    assert bool(org)


@pytest.mark.parametrize(
    ['gov_org_id', 'service_provider', 'country'],
    [('111111-1111', 'notCORP', None),
     ('111111-1111', None, 'notfi'),
     ('222222-2222', 'CORP', 'fi')])
def test_get_company_not_found(storage, gov_org_id, service_provider, country):
    factories.create_org(storage,
                         registration_id='111111-1111',
                         service_provider='CORP',
                         country='fi')
    factories.create_org(storage, registration_id='222222-2222', country='fi')
    with pytest.raises(DataNotFoundError) as exinfo:
        get_org(
            storage,
            gov_org_id=gov_org_id,
            service_provider=service_provider,
            country=country)
        assert ('No org found for gov_org_id=\'%s\', country=%s, service_provider=%s' %
                (gov_org_id, country, service_provider)
                in str(exinfo.value))


def test_get_company_multiple(storage):
    factories.create_org(storage, registration_id='111111-1111', country='fi')
    factories.create_org(storage, registration_id='111111-1111', country='se')
    with pytest.raises(DataDuplicatesFoundError) as exinfo:
        get_org(storage, gov_org_id='111111-1111')
        assert ('More than one org found for gov_org_id=\'111111-1111\', country=None'
                in str(exinfo.value))


@pytest.mark.parametrize(
    ['gov_org_id', 'country', 'expected'],
    [('556860-2832', 'SWE', '556860-2832'),
     ('556860 2832', 'SWE', '556860-2832'),
     ('**********', 'SWE', '556860-2832'),
     ('**********', 'SE', '556860-2832'),
     ('**********', 'se', '556860-2832'),
     ('**********', 'swe', '556860-2832'),
     ('556860 BAD', 'SWE', '556860 BAD')])
def test_normalize_gov_org_id(gov_org_id, country, expected):
    assert expected == normalize_gov_org_id(gov_org_id, country)


def test_normalize_gov_org_id_supported_validators():
    # We may want to notice when validators change
    assert GOV_ORG_ID_VALIDATOR_MODULE == {
        'AT': 'stdnum.at.businessid',
        'BG': None,
        'CZ': None,
        'DE': 'stdnum.de.handelsregisternummer',
        'DK': 'stdnum.dk.cvr',
        'EE': 'stdnum.ee.registrikood',
        'ES': 'stdnum.es.cif',
        'FI': 'stdnum.fi.ytunnus',
        'IE': None,
        'IT': None,
        'LT': 'stdnum.lt.pvm',
        'LV': 'stdnum.lv.pvn',
        'HR': None,
        'NL': None,
        'NO': 'stdnum.no.orgnr',
        'MT': 'stdnum.mt.vat',
        'RO': None,
        'PL': None,
        'PT': 'stdnum.pt.nif',
        'SE': 'stdnum.se.orgnr',
        'SI': None,
        'SK': None,
        'HU': None,
        'GB': None,
    }


@pytest.mark.parametrize(
    ['gov_org_id', 'country'],
    [('111111-1111', 'SWE'),
     ('BAD_ID', 'SWE')])
def test_normalize_gov_org_id_invalid_id(caplog, gov_org_id, country):
    exception = ('Normalize gov_org_id: not possible for gov_org_id=%s country=%s' %
                 (gov_org_id, country))
    normalize_gov_org_id(gov_org_id, country)
    assert exception in caplog.text


@pytest.mark.parametrize(
    ['country', ],
    [(None, ),
     ('RU', ),
     ('', )])
def test_normalize_gov_org_id_no_validator(caplog, country):
    assert 'some_id' == normalize_gov_org_id('some_id', country)
    exception = 'Normalize gov_org_id: unsupported country=%s' % country
    assert exception in caplog.text


def test_bol_company_list_iter(storage):
    # imported from qvarn

    org = factories.create_org(storage, name='Org 1', registration_id='REG-1')
    query = mock.Mock(search='', status='')

    assert get_company_list_results(
        storage, query, user_contract=None, active_org_id=org['id']
    ) == [
        {
            'id': org['id'],
            'company_id': 'REG-1',
            'vat_number': None,
            'country': 'SWE',
            'has_combined_report': None,
            'name': 'Org 1',
            'company_status': 'incomplete',
            'report_available': False,
            'project_count': 0,
        }
    ]


def test_bol_company_list_multiple_reports(storage):
    # imported from qvarn
    org = factories.create_org(storage, name='Org 1', registration_id='REG-1')
    factories.create_report(storage, org, status=STATUS_OK, days_ago=0,
                            interested_org_id=org['id'])
    factories.create_report(storage, org, status=STATUS_INCOMPLETE, days_ago=1,
                            interested_org_id=org['id'])

    query = mock.Mock(search='', status='')
    assert get_company_list_results(
        storage, query, user_contract=None, active_org_id=org['id']
    ) == [
        {
            'id': org['id'],
            'company_id': 'REG-1',
            'vat_number': None,
            'country': 'SWE',
            'has_combined_report': None,
            'name': 'Org 1',
            'company_status': STATUS_OK,
            'report_available': True,
            'project_count': 0,
        }
    ]


def test_bol_company_list_multiple_reports_with_same_timestamp(storage):
    # imported from qvarn
    org = factories.create_org(storage, name='Org 1', registration_id='REG-1')
    # This situation should not ever happen.  Still, let's not do
    # silly things like returning multiple rows for the same organization
    # in case it does happen.
    factories.create_report(
        storage, org, status=STATUS_OK, days_ago=0, interested_org_id=org['id']
    )
    factories.create_report(
        storage, org, status=STATUS_ATTENTION, days_ago=0, interested_org_id=org['id']
    )
    factories.create_report(
        storage, org, status=STATUS_INCOMPLETE, days_ago=1, interested_org_id=org['id']
    )

    query = mock.Mock(search='', status='')

    assert get_company_list_results(
        storage, query, user_contract=None, active_org_id=org['id']
    ) == [
        {
            'id': org['id'],
            'company_id': 'REG-1',
            'vat_number': None,
            'country': 'SWE',
            'has_combined_report': None,
            'name': 'Org 1',
            'company_status': STATUS_ATTENTION,
            'report_available': True,
            'project_count': 0,
        }
    ]


def test_bol_company_list_filter_status(storage):
    # imported from qvarn
    org = factories.create_org(storage, 'F company', registration_id='REG-291')
    factories.create_report(storage, org, status=STATUS_OK)

    a = factories.create_org(storage, 'A company', registration_id='REG-572')
    factories.create_report(storage, a, status=STATUS_OK, interested_org_id=org['id'])

    b = factories.create_org(storage, 'B company', registration_id='REG-715')
    factories.create_report(
        storage, b, status=STATUS_INCOMPLETE, interested_org_id=org['id']
    )

    c = factories.create_org(storage, 'C company', registration_id='REG-659')

    d = factories.create_org(storage, 'D company', registration_id='REG-436')
    factories.create_report(
        storage, d, status=STATUS_ATTENTION, interested_org_id=org['id']
    )

    e = factories.create_org(storage, 'E company', registration_id='REG-646')
    factories.create_report(storage, e, status=STATUS_STOP, interested_org_id=org['id'])

    project = factories.create_default_project(storage, 'A project', org=org)
    factories.create_suppliers_tree(storage, project, suppliers=[
        factories.supplier(a),
        factories.supplier(b),
        factories.supplier(c),
        factories.supplier(d),
        factories.supplier(e),
    ])
    # Exact filter
    filter_status = STATUS_OK
    query = mock.Mock(search='', status=filter_status)
    actual = get_company_list_results(
        storage,
        query,
        user_contract=None,
        active_org_id=org['id'],
        cols=['name', 'company_id', 'company_status'],
    )
    expected = [
        ('A company', 'REG-572', STATUS_OK),
        ('F company', 'REG-291', STATUS_OK),
    ]
    assert sorted(expected) == sorted(actual)

    # not:ok
    filter_status = BOL_STATUS_NOT + STATUS_OK
    query = mock.Mock(search='', status=filter_status)
    actual = get_company_list_results(
        storage,
        query,
        user_contract=None,
        active_org_id=org['id'],
        cols=['name', 'company_id', 'company_status'],
    )
    expected = [
        ('E company', 'REG-646', STATUS_STOP),
        ('B company', 'REG-715', STATUS_INCOMPLETE),
        ('C company', 'REG-659', STATUS_INCOMPLETE),
        ('D company', 'REG-436', STATUS_ATTENTION),
    ]
    assert sorted(expected) == sorted(actual)


def test_get_company_list_bda_missing_org(storage, set_feature_flags, caplog):
    set_feature_flags({'bda_company_list': True})

    org = factories.create_org(storage, 'C1')
    user_role = USER_ROLE_MAIN
    user_contract = factories.get_or_create_user_account(
        storage, org, 'Test user', user_role=user_role
    )
    factories.create_report(storage, 'C1', interested_org_id=org)
    query = mock.Mock(search=None, status=None)

    # supplier org
    a = factories.create_org(storage, 'A company', registration_id='REG-572')
    factories.create_report(storage, a, status=STATUS_OK, interested_org_id=org['id'])

    project = factories.create_default_project(storage, 'A project', org=org)
    factories.create_suppliers_tree(storage, project, suppliers=[
        factories.supplier(a),
    ])

    delete_org(storage, a['id'])
    companies = get_company_list(
        storage,
        query=query,
        user_contract=user_contract,
        active_org_id=org['id'],
        user_role=USER_ROLE_MAIN,
    )
    expected = [{'company_id': org['gov_org_ids'][0]['gov_org_id'],
                 'vat_number': None,
                 'company_status': STATUS_OK,
                 'country': 'SWE',
                 'has_combined_report': None,
                 'id': org['id'],
                 'name': 'C1',
                 'project_count': 0,
                 'report_available': True}]
    assert companies == expected
    error_msg = "bolfak.services.companies: ERROR: BDA company list: "
    f"not found companies ids: {a['id']}\n"
    assert error_msg in caplog.text


def test_get_company_list_bda_missing_org_in_cqpoc(storage,
                                                   set_feature_flags, caplog):
    set_feature_flags({'bda_company_list': True, 'cqpoc_get_companies': True})

    org = factories.create_org(storage, 'C1')
    user_role = USER_ROLE_MAIN
    user_contract = factories.get_or_create_user_account(
        storage, org, 'Test user', user_role=user_role
    )
    factories.create_report(storage, 'C1', interested_org_id=org)
    factories.create_default_project(storage, 'A project', org=org)

    # Mock cqpoc_client response with not found companies (`None`) and CQPOC 'error'.
    class MockCQPOCClient():
        def get_multiple_companies(self, company_ids):
            return [
                None,
                {
                    'error': {
                        'error_code': 'ItemDoesNotExist',
                        'message': 'Organization with id f392-bar-c121586e does not exist'}}]

    query = mock.Mock(search=None, status=None)

    companies = get_company_list(
        storage,
        cqpoc_client=MockCQPOCClient(),
        query=query,
        user_contract=user_contract,
        active_org_id=org['id'],
        user_role=USER_ROLE_MAIN,
    )

    # No companies found
    assert companies == []

    # Both None and 'error' in the CQPOC response were processed without errors
    error_msg = 'bolfak.services.companies: ERROR: BDA company list: '
    "not found companies ids: {}\n"
    assert error_msg in caplog.text


def test_get_company_list_missing_org_basic_user(storage, caplog):
    org = factories.create_org(storage, 'C1')
    user_role = USER_ROLE_BASIC
    user_contract = factories.get_or_create_user_account(
        storage, org, 'Test user', user_role=user_role
    )
    factories.create_report(storage, 'C1', interested_org_id=org)
    query = mock.Mock(search=None, status=None)

    # supplier org
    a = factories.create_org(storage, 'A company', registration_id='REG-572')
    factories.create_report(storage, a, status=STATUS_OK, interested_org_id=org['id'])

    project = factories.create_default_project(storage, 'A project', org=org)
    factories.create_project_user_contract(storage, project, user_contract, org)
    factories.create_suppliers_tree(storage, project, suppliers=[
        factories.supplier(a),
    ])

    delete_org(storage, a['id'])
    companies = get_company_list(
        storage,
        query=query,
        user_contract=user_contract,
        active_org_id=org['id'],
        user_role=user_role,
    )
    expected = [{'company_id': org['gov_org_ids'][0]['gov_org_id'],
                 'vat_number': None,
                 'company_status': STATUS_OK,
                 'country': 'SWE',
                 'has_combined_report': None,
                 'id': org['id'],
                 'name': 'C1',
                 'project_count': 0,
                 'report_available': True}]
    assert companies == expected
    warning_msg = 'bolfak.services.companies: WARNING: Company list: '
    f"not found companies ids: {a['id']}\n"
    assert warning_msg in caplog.text


def test_get_company_related_projects_block_project_client(app, storage,
                                                           ff_add_project_client_on,
                                                           ff_block_project_client_on):
    # specific test case for https://vaultit.atlassian.net/browse/BOL-5996
    org_a = factories.create_org(storage, 'org A')
    org_b = factories.create_org(storage, 'org B')
    org_c = factories.create_org(storage, 'org C')

    # Log in as Company A
    user_contract = app.login(org_a)
    # Create project1 as Main Contractor (A), and add a project client (B).
    # Block client access: “The project's client can not see and
    # will not have access to the project”
    p1 = factories.create_default_project(storage,
                                          'project 1',
                                          project_creator_role=MAIN_CONTRACTOR_ROLE,
                                          created_by_org_id=org_a['id'],
                                          client_company_id=org_b['id'],
                                          added_client_can_view=False)
    # Add Company C as supplier to the project
    factories.create_suppliers_tree(storage, p1, [
        factories.supplier('org C', supplier_role=SUPPLIER_ROLE),
    ])
    # Get related projectsr for company C
    projects = get_company_related_projects(
        storage, org_c, user_contract, is_admin=True, active_org_id=org_a['id']
    )
    # 'Related projects' shows project1, created by company A /Main Contractor
    assert [p['name'] for p in projects] == [p1['names'][0]]

    # Log in as Company B
    user_contract = app.login(org_b)
    # Create project2 as Client
    p2 = factories.create_default_project(storage,
                                          'project 2',
                                          created_by_org_id=org_b['id'],
                                          project_creator_role=CLIENT_ROLE)
    # Add Company C as supplier to the project
    factories.create_suppliers_tree(storage, p2, [
        factories.supplier('org C', supplier_role=SUPPLIER_ROLE),
    ])
    # Get related projectsr for company C
    projects = get_company_related_projects(
        storage, org_c, user_contract, is_admin=True, active_org_id=org_b['id']
    )
    # 'Related projects' shows project2, created by company B /Client
    # 'Related projects' does not show project1, created by company A
    assert [p['name'] for p in projects] == [p2['names'][0]]


def test_get_company_related_projects_empty_role(
    app, storage, ff_add_project_client_on, ff_block_project_client_on
):
    org = factories.create_org(storage, "C1")
    user_contract = factories.get_or_create_user_account(storage, org, "Test user")
    factories.create_default_project(
        storage, "Active Project", state=ACTIVE, project_leader=user_contract, org=org
    )
    factories.create_suppliers_tree(
        storage,
        "Active Project",
        [
            factories.supplier("C1"),
        ],
    )
    no_projects = get_company_related_projects(
        storage,
        org,
        user_contract,
        is_admin=False,
        active_org_id=org["id"],
        user_role=None,
    )
    both_projects = get_company_related_projects(
        storage,
        org,
        user_contract,
        is_admin=False,
        active_org_id=org["id"],
        user_role=USER_ROLE_MAIN,
    )
    assert len(no_projects) == 0
    assert {p["name"] for p in both_projects} == {"Active Project"}


def test_get_company_user_info_by_email_from_core_no_bol_permission(
        storage,
        core_mitt_id06,
        setup_and_teardown_organisation_person
):
    org_person, org_record = setup_and_teardown_organisation_person(email='<EMAIL>')
    result = get_company_user_info_by_email_from_core(
        storage,
        org_record.organisation.uuid,
        org_person['professionalEmail']
    )
    assert result == {
        'full_name': org_person.person.full_name,
        'email': org_person.professional_email,
        'contract_id': None,
        'person_id': org_person.person.uuid,
        'is_active': True,
        'has_bol_permission': False
    }


def test_get_company_user_info_by_email_from_core_bol_permission(
        storage,
        core_mitt_id06,
        setup_and_teardown_organisation_person
):
    org_person, org_record = setup_and_teardown_organisation_person(
        email='<EMAIL>',
        roles=['BOL_USER']
    )
    result = get_company_user_info_by_email_from_core(
        storage,
        org_record.organisation.uuid,
        org_person['professionalEmail']
    )
    assert result == {
        'full_name': org_person.person.full_name,
        'email': org_person.professional_email,
        'contract_id': None,
        'person_id': org_person.person.uuid,
        'is_active': True,
        'has_bol_permission': True
    }


def test_get_company_user_info_by_email_from_core_invalid_email(
        storage,
        core_mitt_id06,
        setup_and_teardown_organisation_person
):
    org_person, org_record = setup_and_teardown_organisation_person(email='<EMAIL>')
    result = get_company_user_info_by_email_from_core(
        storage,
        org_record.organisation.uuid,
        '<EMAIL>'
    )
    assert result is None


def test_get_company_user_info_by_email_from_core_invalid_org_id(
        storage,
        core_mitt_id06,
        setup_and_teardown_organisation_person
):
    org_person, org_record = setup_and_teardown_organisation_person(email='<EMAIL>')
    with pytest.raises(
        DataNotFoundError,
        match='Org wrong-org-id was not found'
    ):
        get_company_user_info_by_email_from_core(
            storage,
            'wrong-org-id',
            org_person['professionalEmail']
        )


def test_get_company_user_info_by_email_from_core_missing_org_id(
        storage,
        core_mitt_id06,
        setup_and_teardown_organisation_person
):
    org_person, org_record = setup_and_teardown_organisation_person(email='<EMAIL>')
    result = get_company_user_info_by_email_from_core(
        storage,
        None,
        org_person['professionalEmail']
    )
    assert result is None


def test_get_company_related_project_ids_for_org_main_user_sees_all_related_projects(
    storage, org_and_projects
):
    org, user_org, user_contract, project_ids = org_and_projects
    result = get_company_related_project_ids_for_org(
        storage, org["id"], user_contract, user_org["id"], is_main_user=True
    )
    assert result == set(project_ids[0:3])


def test_get_company_related_project_ids_for_org_non_main_user_sees_only_own_projects(
    storage, org_and_projects, mocker
):
    org, user_org, user_contract, project_ids = org_and_projects
    # Simulate user only belongs to p1
    mocker.patch(
        "bolfak.services.companies.get_user_project_id_list_for_contract",
        return_value=[project_ids[0]],
    )
    result = get_company_related_project_ids_for_org(
        storage, org["id"], user_contract, user_org["id"], is_main_user=False
    )
    assert result == {project_ids[0]}


def test_get_company_related_project_ids_for_org_no_projects_if_not_supplier(storage):
    org = factories.create_org(storage, "SupplierOrg")
    user_org = factories.create_org(storage, "UserOrg")
    user_contract = factories.get_or_create_user_account(storage, user_org, "Test user")
    # No suppliers created
    result = get_company_related_project_ids_for_org(
        storage, org["id"], user_contract, user_org["id"], is_main_user=True
    )
    assert result == set()


@pytest.mark.parametrize("materialized_path", ["", "   ", None, [], {}])
def test_get_company_related_project_ids_for_org_empty_materialized_path(
    storage, org_and_projects, mocker, materialized_path
):
    org, user_org, user_contract, project_ids = org_and_projects
    org_suppliers = get_orgs_suppliers(
        storage, [org["id"]], select=["project_resource_id", "materialized_path"]
    )
    for supplier in org_suppliers:
        assert supplier["materialized_path"] is not None
        supplier["materialized_path"] = materialized_path
    mocker.patch(
        "bolfak.services.companies.get_orgs_suppliers",
        return_value=org_suppliers,
    )
    result = get_company_related_project_ids_for_org(
        storage, org["id"], user_contract, user_org["id"], is_main_user=True
    )
    # Assert that the PA projects are not in the list
    assert result == set(project_ids[0:2])
