import json
import logging
import re
import textwrap
import threading
from operator import itemgetter

from bolfak.testing.utils import get_fixture_path
import bottle
import pytest
from bs4 import BeautifulSoup
from freezegun.api import freeze_time
from qvarnclient.qvarnapi import QvarnUnknownError

from bolfak.fixtures import factories
from bolfak.fixtures.factories import make_f_tax, make_reg_number, make_vat, supplier
from bolfak.models import (
    CLIENT_ROLE,
    MAIN_CONTRACTOR_ROLE,
    PROJECT_USER_ROLE_MANAGER,
    PROJECT_USER_ROLE_MEMBER,
    STATUS_ATTENTION,
    STATUS_INCOMPLETE,
    STATUS_INVESTIGATE,
    STATUS_OK,
    STATUS_STOP,
    SUPERVISOR_ROLE,
    SUPPLIER_ROLE,
    USER_ROLE_BASIC,
    USER_ROLE_MAIN,
    VISITOR,
    alpha3_country_code,
)
from bolfak.schemas.reports import PrincipalFunction
from bolfak.services.companies import (
    find_company_by_reg_no,
    get_org,
)
from bolfak.services.reports import finalize_report
from bolfak.statusreports.sandbox import company_template
from bolfak.storage.qvarn.company import get_orgs_by_name_fragment
from bolfak.storage.qvarn.projects import CLOSED
from bolfak.storage.qvarn.suppliers import get_supplier
from bolfak.testing.statusreports import get_creditsafe_error_response
from bolfak.featureflags import feature_active
from fixtures.core_mock import MOCK_ORGS
from statusreports.test_connect import FakeCreditsafeConnectClient


def soup_prettify(text, keep_newlines=True):
    # contract consecutive ' ' into one
    text = re.sub(' +', ' ', text)
    # Remove trailing ' '
    text = re.sub(' \n', '\n', text)
    # contract consecutive '\n' into one
    text = re.sub('\n+', '\n', text)
    if not keep_newlines:
        text = re.sub('\n', '', text)
    return text


def soup_find_section_by_name(soup, name):
    sections = soup.findAll('table', class_='section')

    result = None
    for section in sections:
        if section.find('span', class_='section_name').string.strip() == name:
            result = section
    return result


def soup_get_contained(soup, container):
    """Break container into rows and into texts within these rows.

    Very basic.

    Will work with simple structures like:
    <div class="my-container">
        <tr>
            <td> </td>
            <td> </td>
        </tr>
    </div>
    """
    ROW_TAGS = ['tr', 'caption', 'div']
    COL_TAGS = ['td', 'th', 'span', 'i']

    rows = []
    if not container:
        return rows

    if container.name in ROW_TAGS:
        rows.append(container)
    rows.extend(container.find_all(ROW_TAGS))

    text_rows = []
    for row in rows:
        # find and remove nested row tags as these will have their own iterations
        for nested_row_tag in row.find_all(ROW_TAGS):
            nested_row_tag.extract()

        cols = row.find_all(COL_TAGS)

        col_texts = []
        for col in cols:
            # find and remove nested col tags as these will have their own iterations
            for nested_col_tag in col.find_all(COL_TAGS):
                nested_col_tag.extract()

            col_text = col.text.strip()
            if not col_text:
                continue

            col_texts.append(soup_prettify(col_text, keep_newlines=False))
        row_text = ' '.join(col_texts)
        text_rows.append(row_text)
    return text_rows


def soup_get_section(soup, section_name):
    section = soup_find_section_by_name(soup, section_name)
    return soup_get_contained(soup, section)


def test_company_list_fixtures(app, storage, company_list_fixture_main):
    resp = app.get_with_org('/api/company-list')
    qvarn_company = find_company_by_reg_no(storage, 'TC-1001')
    assert resp.json['companies'][0] == {
        'id': qvarn_company['id'],
        'vat_number': None,
        'name': 'Test Company 1',
        'company_id': 'TC-1001',
        'company_status': 'stop',
        'report_available': True,
        'country': 'SWE',
        'has_combined_report': None,
        'project_count': 2,
    }
    assert resp.json['companies'][1]['name'] == 'Test Company 4'
    assert resp.json['companies'][1]['project_count'] == 1
    assert resp.json['companies'][2]['name'] == 'Test Company 2'
    assert resp.json['companies'][2]['project_count'] == 1
    assert resp.json['companies'][3]['name'] == 'Test Company 3'
    assert resp.json['companies'][3]['project_count'] == 3
    assert resp.json['companies'][4]['name'] == 'Test Company Main'
    assert resp.json['companies'][4]['project_count'] == 0
    assert len(resp.json['companies']) == 5


def test_bda_company_list_closed_project_basic_user(app, storage, set_feature_flags):
    set_feature_flags({'bda_company_list': True})
    _test_company_list_closed_project_basic_user(app, storage)


def _test_company_list_closed_project_basic_user(app, storage):
    qvarn = storage.qvarn

    org = factories.create_org(storage, 'Responsible Org', registration_id='1111-1111-R')
    supplier_org = factories.create_org(storage, 'A supplier', registration_id='ABC-112')
    factories.create_org(storage, 'Other supplier', registration_id='ABC-100')

    person = factories.create_person(qvarn, 'Anthony Bull', '<EMAIL>')

    user_contract = factories.create_user_account(
        storage, person, supplier_org, user_role=USER_ROLE_BASIC
    )
    project = factories.create_default_project(
        storage, 'Some project', project_id='PR-42', org=org, state=CLOSED
    )
    factories.create_suppliers_tree(storage, project, [
        factories.supplier('A supplier', supplier_role=SUPERVISOR_ROLE),
        factories.supplier('Other supplier', supplier_role=MAIN_CONTRACTOR_ROLE),
    ])
    factories.create_project_user_contract(storage, project, user_contract, supplier_org)

    app.login(supplier_org, person, admin=False, role='basic')
    resp = app.get_with_org('/api/company-list')
    # only active company is included into company list
    assert resp.json['companies'] == [
        {
            'id': supplier_org['id'],
            'company_id': 'ABC-112',
            'vat_number': None,
            'country': 'SWE',
            'has_combined_report': None,
            'name': 'A supplier',
            'company_status':
            'incomplete',
            'report_available': False,
            'project_count': 0
        }
    ]


def _test_company_list(app, storage):
    asfalt_gov_org_ids = [make_reg_number('3100-9876-Z'), make_vat('ABC112233')]
    widgets_gov_org_ids = [make_reg_number('3145-9876-X'), make_vat('ABC888888')]

    org = factories.create_org(storage, 'Responsible Org', registration_id='1111-1111-R')
    supplier_orgs = [
        factories.create_org(storage, 'Widgets Ltd.', gov_org_ids_raw=widgets_gov_org_ids),
        factories.create_org(storage, "Pancakes'R'Us", registration_id='4567-9876-Y'),
        factories.create_org(storage, "Asphalt Mixers Co", gov_org_ids_raw=asfalt_gov_org_ids),
    ]
    person = factories.create_person(storage, "Anthony Bull", "<EMAIL>")
    project = factories.create_default_project(
        storage, 'Some project', project_id='PR-42', org=org, project_leader=person
    )
    factories.create_suppliers_tree(
        storage, project, [supplier('Widgets Ltd.'),
                           supplier("Pancakes'R'Us"),
                           supplier("Asphalt Mixers Co"),
                           ]
    )

    factories.create_report(storage, 'Widgets Ltd.', status=STATUS_OK, interested_org_id=org['id'])

    app.login(org, person, admin=False)

    resp = app.get_with_org('/api/company-list')
    result = sorted(resp.json['companies'], key=itemgetter('name'))

    assert result[0] == {
        'id': supplier_orgs[2]['id'],
        'company_id': '3100-9876-Z',
        'vat_number': 'ABC112233',
        'company_status': 'incomplete',
        'country': 'SWE',
        'has_combined_report': None,
        'name': 'Asphalt Mixers Co',
        'project_count': 1,
        'report_available': False}

    assert result[-1] == {
        'company_id': '3145-9876-X',
        'vat_number': 'ABC888888',
        'company_status': STATUS_OK,
        'country': 'SWE',
        'has_combined_report': None,
        'id': supplier_orgs[0]['id'],
        'name': 'Widgets Ltd.',
        'project_count': 1,
        'report_available': True}


def test_company_list(app, storage):
    _test_company_list(app, storage)


def test_company_list_bda(storage, app, set_feature_flags):
    set_feature_flags({'bda_company_list': True, 'pagination': True})
    _test_company_list(app, storage)


def test_company_list_core(storage, app_with_core, set_feature_flags):
    set_feature_flags({"bda_company_list": True, "pagination": True})
    _test_company_list(app_with_core, storage)


def test_company_list_ordering(storage, app):
    # imported from qvarn
    # The company list is ordered by status (worst companies first), then by
    # name.
    org = get_orgs_by_name_fragment(storage, 'Trafikverket')[0]

    factories.create_report(storage, org, status=STATUS_OK)

    d = factories.create_org(storage, 'D company', registration_id='REG-436')
    factories.create_report(
        storage, d, status=STATUS_ATTENTION, interested_org_id=org['id']
    )

    f = factories.create_org(storage, 'F company', registration_id='REG-646')
    factories.create_report(storage, f, status=STATUS_STOP, interested_org_id=org['id'])

    e = factories.create_org(storage, 'E company', registration_id='REG-405')
    factories.create_report(
        storage, e, status=STATUS_ATTENTION, interested_org_id=org['id']
    )

    a = factories.create_org(storage, 'A company', registration_id='REG-572')
    factories.create_report(storage, a, status=STATUS_OK, interested_org_id=org['id'])

    h = factories.create_org(storage, 'H company', registration_id='REG-360')
    factories.create_report(storage, h, status=STATUS_OK, interested_org_id=org['id'])

    c = factories.create_org(storage, 'C company', registration_id='REG-411')
    factories.create_report(storage, c, status=STATUS_OK, interested_org_id=org['id'])

    b = factories.create_org(storage, 'B company', registration_id='REG-715')
    factories.create_report(
        storage, b, status=STATUS_INCOMPLETE, interested_org_id=org['id']
    )

    z = factories.create_org(storage, 'Z company', registration_id='REG-843')
    factories.create_report(
        storage, z, status=STATUS_INCOMPLETE, interested_org_id=org['id']
    )

    g = factories.create_org(storage, 'G company', registration_id='REG-637')
    factories.create_report(
        storage, g, status=STATUS_ATTENTION, interested_org_id=org['id']
    )

    x = factories.create_org(storage, 'X company', registration_id='REG-659')

    # Make sure our user can see all of the above
    project = factories.create_default_project(storage, 'A project', org=org)
    factories.create_suppliers_tree(storage, project, suppliers=[
        factories.supplier(a),
        factories.supplier(b),
        factories.supplier(c),
        factories.supplier(d),
        factories.supplier(e),
        factories.supplier(f),
        factories.supplier(g),
        factories.supplier(h),
        factories.supplier(x),
        factories.supplier(z),
    ])

    resp = app.get_with_org('/api/company-list', org_id=org['id'])
    result = resp.json['companies']

    cols = ['name', 'company_id', 'company_status']
    actual = list(map(itemgetter(*cols), result))

    expected = [
        # Note that (no report) sorts as Incomplete!
        ('F company', 'REG-646', STATUS_STOP),
        ('B company', 'REG-715', STATUS_INCOMPLETE),
        ('X company', 'REG-659', STATUS_INCOMPLETE),
        ('Z company', 'REG-843', STATUS_INCOMPLETE),
        ('D company', 'REG-436', STATUS_ATTENTION),
        ('E company', 'REG-405', STATUS_ATTENTION),
        ('G company', 'REG-637', STATUS_ATTENTION),
        ('A company', 'REG-572', STATUS_OK),
        ('C company', 'REG-411', STATUS_OK),
        ('H company', 'REG-360', STATUS_OK),
        ('Trafikverket', '105741-4736', 'ok'),
    ]
    assert actual == expected


def test_search_by_id(app, company_list_fixture_main):
    resp = app.get_with_org('/api/company-list', {'search': 'tc-1001'})
    assert len(resp.json['companies']) == 1
    assert resp.json['companies'][0]['name'] == 'Test Company 1'


def test_search_by_vat(app, storage, set_feature_flags):
    set_feature_flags({'pagination': True})
    org = factories.create_org(storage, 'Responsible Org', registration_id='1111-1111-R')

    supplier_orgs = [
        factories.create_org(
            storage, 'Widgets Ltd.',
            gov_org_ids_raw=[make_reg_number('3145-9876-X'), make_vat('ABC123456')]
        ),
        factories.create_org(storage, "Pancakes'R'Us", registration_id='4567-9876-Y'),
        factories.create_org(storage, "Asphalt Mixers Co", registration_id='3100-12385-Z'),
        factories.create_org(
            storage, "Roof breakers AB", gov_org_ids_raw=[make_vat('0987654321')]
        ),
    ]
    person = factories.create_person(storage.qvarn, 'Anthony Bull', '<EMAIL>')
    project = factories.create_default_project(
        storage, 'Some project', project_id='PR-42', org=org, project_leader=person
    )
    factories.create_suppliers_tree(storage, project, [
        supplier('Widgets Ltd.'),
        supplier("Pancakes'R'Us"),
        supplier("Asphalt Mixers Co"),
    ])

    factories.create_report(
        storage, 'Widgets Ltd.', status=STATUS_OK, interested_org_id=org['id']
    )
    app.login(org, person, admin=False)

    resp = app.get_with_org('/api/company-list', {'search': '123'})
    result = sorted(resp.json['companies'], key=itemgetter('name'))
    assert len(result) == 2

    assert result[0] == {
        'company_id': '3100-12385-Z',
        'vat_number': None,
        'company_status': 'incomplete',
        'country': 'SWE',
        'has_combined_report': None,
        'id': supplier_orgs[2]['id'],
        'name': 'Asphalt Mixers Co',
        'project_count': 1,
        'report_available': False}

    assert result[1] == {
        'company_id': '3145-9876-X',
        'vat_number': 'ABC123456',
        'company_status': STATUS_OK,
        'country': 'SWE',
        'has_combined_report': None,
        'id': supplier_orgs[0]['id'],
        'name': 'Widgets Ltd.',
        'project_count': 1,
        'report_available': True}


def test_get_countries(app):
    resp_en = app.get_with_org('/api/countries/en')
    resp_sv = app.get_with_org('/api/countries/sv')
    assert resp_en.json['countries'][0]['code'] == 'af'
    assert resp_en.json['countries'][0]['name'] == 'Afghanistan'
    assert resp_sv.json['countries'][1]['name'] == 'Albanien'
    assert len(resp_en.json['countries']) == 249


def test_search_by_id_fragment(app, company_list_fixture_main, pagination_off):
    resp = app.get_with_org('/api/company-list', {'search': ' tc-100'})
    assert len(resp.json['companies']) == 4
    assert resp.json['companies'][0]['company_id'] == 'TC-1001'


def test_search_with_whitespace(app, company_list_fixture_main, pagination_off):
    """Regression test"""
    resp = app.get_with_org('/api/company-list', {'search': '  tc     '})
    assert len(resp.json['companies']) == 5


def test_search_by_title(app, company_list_fixture_main, pagination_off):
    resp = app.get_with_org('/api/company-list', {'search': 'company 4'})
    assert len(resp.json['companies']) == 1
    assert resp.json['companies'][0]['name'] == 'Test Company 4'


def _test_search_by_title_with_non_ascii_characters(app, storage):
    factories.make_orgs_visible(
        storage,
        orgs=['Very ASCII', 'Very non-ASCII: ąčęėį'],
        user_company='User company',
    )
    app.login('User company')
    resp = app.get_with_org('/api/company-list', {'search': 'ąčę'})
    assert len(resp.json['companies']) == 1
    assert resp.json['companies'][0]['name'].startswith('Very non-ASCII')


def test_search_by_title_with_non_ascii_characters(app, storage):
    _test_search_by_title_with_non_ascii_characters(app, storage)


def test_search_by_title_with_non_ascii_characters_bda(
        storage, app, set_feature_flags):
    set_feature_flags({'bda_company_list': True, 'pagination': True})
    _test_search_by_title_with_non_ascii_characters(app, storage)


def test_filter_by_status(app, company_list_fixture_main, pagination_off):
    resp = app.get_with_org('/api/company-list', {'status': 'stop'})
    assert len(resp.json['companies']) == 2
    assert sum(1 for x in resp.json['companies'] if x['company_status'] != 'stop') == 0


def test_filter_by_negative_status(app, company_list_fixture_main, pagination_off):
    resp = app.get_with_org('/api/company-list', {'status': 'not:ok'})
    assert len(resp.json['companies']) > 0
    assert sum(1 for x in resp.json['companies'] if x['company_status'] == STATUS_OK) == 0


def test_admin_user(app, qvarn, company_list_fixture_admin, pagination_off):
    resp = app.get_with_org('/api/company-list')
    assert len(resp.json['companies']) == 6


def test_nonadmin_user_can_only_see_their_own_company(app, qvarn, pagination_off):
    # Test that a user that is not an admin and doesn't have any projects
    # can only see their own company
    app.login('Non-project company', admin=False)
    resp = app.get_with_org('/api/company-list')
    assert len(resp.json['companies']) == 1


def test_companies_details_returns_companies_details_when_request_is_valid(app, storage):
    expected_gov_org_ids = [
        {
            "country": "SWE",
            "gov_org_id": "852372-1298",
            "org_id_type": "registration_number",
        },
        {
            "country": "SWE",
            "gov_org_id": "ABC12345",
            "org_id_type": "vat_number",
        },
    ]

    supplier_gov_org_ids = [make_reg_number("852372-1298"), make_vat("ABC12345")]
    factories.create_org(storage, "Supplier", gov_org_ids_raw=supplier_gov_org_ids)
    tree = factories.create_suppliers_tree(
        storage,
        "My Project",
        [
            factories.supplier(
                "Main Supplier",
                supplier_role=SUPERVISOR_ROLE,
                subcontractors=[
                    factories.supplier("Supplier"),
                ],
            ),
        ],
    )
    user_contract = app.login("Main Supplier")
    factories.create_project_user_contract(
        storage, "My Project", user_contract, "Main Supplier"
    )
    org = get_org(storage, name="Supplier")
    factories.create_report(storage, org, status=STATUS_ATTENTION)
    s1_1 = get_supplier(storage, tree[0].subcontractors[0].id)
    response = app.post_json_with_org(
        "/api/companies-details", {"id_pairs": [
            {"company_id": org["id"],
             "supplier_id": s1_1["id"]}
            ]}
    )

    assert response.json == {
        "companies": [
            {
                "comments": [],
                "name": "Supplier",
                "status": {
                    "overall_status_icon": "info_outline",
                    "items": [
                        {
                            "icon": "info_outline",
                            "ifmissing": "-",
                            "interpretation": STATUS_ATTENTION,
                            "label": {
                                "en": "Company registration",
                                "sv": "Bolagsregistrering",
                                "fi": "Company registration",
                            },
                            "source": "",
                            "status": {
                                "en": (
                                    "Information about the company registration not found in "
                                    "Bolagsverket."
                                ),
                                "sv": "Information om företagsregistrering i Bolagsverket saknas",
                                "fi": (
                                    "Information about the company registration not found in "
                                    "Bolagsverket."
                                ),
                            },
                            "updated": None,
                        }
                    ],
                    "overall_status": STATUS_ATTENTION,
                    "overall_status_text": {
                        "en": "Attention",
                        "sv": "OBS",
                        "fi": "Huomioi",
                    },
                    "updated_date": None,
                },
                "country": "SWE",
                "company_id": org["id"],
                "gov_org_ids": expected_gov_org_ids,
                "permissions": ["view_related_projects"],
                'company_details_url':
                f'https://companies-example.id06.se/#/{org["id"]}/organization/basicinfo'
            }
        ]
    }


def test_companies_details_v2(app, storage, mocker, get_json_cs_v2,
                              report_provider_cs_v2):

    main_org = factories.create_org(storage, "Main Supplier")
    org = factories.create_org(storage, "Supplier", registration_id='222222-0042')
    project = factories.create_default_project(storage, "My Project")
    tree = factories.create_suppliers_tree(storage, project, [
        factories.supplier(
            main_org,
            supplier_role=SUPERVISOR_ROLE,
            subcontractors=[factories.supplier(org)]
        )
    ])

    report = get_json_cs_v2(report_provider_cs_v2, mocker,
                            f_tax='No', company_type='')
    factories.create_report(storage, org, report=report,
                            interested_org_id=main_org['id'],
                            used_providers=['creditsafe_v2'])

    user_contract = app.login(main_org)
    factories.create_project_user_contract(
        storage, project, user_contract, main_org
    )
    s1_1 = get_supplier(storage, tree[0].subcontractors[0].id)
    response = app.post_json_with_org(
        "/api/companies-details", {"id_pairs": [
            {"company_id": org["id"],
             "supplier_id": s1_1["id"]}
        ]}
    )

    assert response.json == {'companies': []}


def test_companies_details_not_allowed_orgs(app_with_pa, storage, mocker, get_json_cs_v2,
                                            report_provider_cs_v2):
    app = app_with_pa

    cl = factories.create_org(storage, 'Client')
    project = factories.create_default_project(storage, 'Project', org=cl)
    s1 = factories.create_org(storage, 'S1')
    s2 = factories.create_org(storage, 'S2')
    tree = factories.create_suppliers_tree(storage, project, org=cl, suppliers=[
        supplier('S1', supplier_role=MAIN_CONTRACTOR_ROLE),
        supplier('S2'),
    ])
    report = get_json_cs_v2(report_provider_cs_v2, mocker,
                            f_tax='No', company_type='')
    factories.create_report(storage, s1, status='stop', report=report,
                            interested_org_id=cl['id'],
                            used_providers=['creditsafe_v2'])
    factories.create_report(storage, s2, status='stop', report=report,
                            interested_org_id=cl['id'],
                            used_providers=['creditsafe_v2'])
    user_contract = app.login(s2)
    factories.create_project_user_contract(
        storage, project, user_contract, s2
    )
    s1_1 = get_supplier(storage, tree[0].id)
    response = app.post_json_with_org(
        "/api/companies-details", {"id_pairs": [
            {"company_id": s2["id"],
             "supplier_id": s1_1["id"]}
        ]}
    )
    assert response.json == {
        'companies': [{
            'comments': [],
            'company_id': s2["id"],
            'country': 'SWE',
            'gov_org_ids': [{'country': 'SWE',
                             'gov_org_id': '717615-5192',
                             'org_id_type': 'registration_number'}],
            'name': 'S2',
            'company_details_url':
            f'https://companies-example.id06.se/#/{s2["id"]}/organization/basicinfo',
            'permissions': ['view_related_projects'],
            'status': None}]
    }


def test_companies_details_returns_errors_when_companies_not_in_request_body(app):
    response = app.post_json_with_org("/api/companies-details", {})

    assert response.json == {
        'errors': {
            "id_pairs": ['This field is required.'],
        },
        'ok': False,
    }


def test_companies_details_returns_errors_when_no_request_body(app):
    response = app.post_json_with_org('/api/companies-details')

    assert response.json == {
        'errors': {
            "id_pairs": ['This field is required.'],
        },
        'ok': False,
    }


def test_company_report(app, storage, company_list_fixture_main):
    qvarn_company = find_company_by_reg_no(storage, 'TC-1001')
    resp = app.get_with_org('/api/company/{}/report'.format(qvarn_company['id']))
    assert resp.headers['content-type'] == 'text/html; charset=UTF-8'
    assert b'href="/api/static/id06/report.css"' in resp.body


def test_company_report_not_accessbile_when_logged_out(app):
    app.get_with_org('/api/company/42/report', org_id='12345', status=403)


def test_company_report_not_found_when_logged_in(app, company_list_fixture_main):
    app.get_with_org('/api/company/zzzz/report', status=404)


def test_company_report_forbidden(app, storage, company_list_fixture):
    app.login(find_company_by_reg_no(storage, 'TC-1004'))
    qvarn_company = find_company_by_reg_no(storage, 'TC-1005')
    app.get_with_org('/api/company/{}/report'.format(qvarn_company['id']), status=404)


def test_company_report_admin_can_see_everything(company_list_fixture_admin, app, storage):
    qvarn_company = find_company_by_reg_no(storage, 'TC-1005')
    app.get_with_org('/api/company/{}/report'.format(qvarn_company['id']))


@pytest.mark.parametrize(('used_providers', 'html_version'), [
    (None, ''),  # For test compat
    (['creditsafe_ggs'], ''),
    (['creditsafe', 'bisnode', 'skatteverket'], ''),
    (['creditsafe_v2'], '2.0'),
    (['creditsafe_ggs', 'creditsafe_connect'], '')])
def test_company_report_used_providers(storage, app, qvarn, used_providers,
                                       html_version):
    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report={}, used_providers=used_providers)

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))

    meta_version = resp.html.head.find(attrs={'name': 'version'})['content']
    assert meta_version == html_version


@pytest.mark.parametrize('unsupported_providers', [
    ['creditsafe_v2', 'creditsafe'],  # no template supporting both v2 and v1
    ['creditsafe_v2', 'creditsafe_ggs']])
def test_company_report_used_providers_unsupported_combinations(storage, app, qvarn,
                                                                unsupported_providers):
    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report={},
                            used_providers=unsupported_providers)
    app.login(org, admin=True)
    with pytest.raises(NotImplementedError):
        app.get_with_org('/api/company/{}/report'.format(org['id']))


@pytest.mark.parametrize('unsupported_providers', [
    ('NONE SUCH STRING'),
    ('v2'),
    ('creditsaf'),
    ('creditsafe_v'),
    ('ggs'),
    ('connect')])
def test_company_report_used_providers_unsupported(storage, app, qvarn,
                                                   unsupported_providers, caplog):
    org = factories.get_or_create_org(storage, 'Org')
    with pytest.raises(QvarnUnknownError):
        factories.create_report(storage, org, report={},
                                used_providers=unsupported_providers)
        assert f'"used_providers": ["unallowed values [{unsupported_providers}' in caplog.text


def test_company_report_i18n(app, storage, company_list_fixture_main):
    app.set_cookie('used_ui_language', 'sv')
    company = find_company_by_reg_no(storage, 'TC-1001')
    resp = app.get_with_org('/api/company/{}/report'.format(company['id']))
    assert resp.html.title.string == 'Företagsrapport'

    app.set_cookie('used_ui_language', 'en')
    resp = app.get_with_org('/api/company/{}/report'.format(company['id']))
    assert resp.html.title.string == 'Company report'


def test_partial_company_report(app, qvarn, storage):
    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report={})
    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    assert resp.headers['content-type'] == 'text/html; charset=UTF-8'
    assert b'href="/api/static/id06/report.css"' in resp.body


def test_company_report_board_member_presentation(app, qvarn, set_feature_flags, storage):
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report={
        'board_members': [
            {
                'appointment_date': '2016-09-13',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'BOARD_MEMBER',
                'principal_id': '999999-XXXX',
                'principal_name': 'Antoni, Bo Sven',
                'raw_function': 'Ledamot',
            },
            {
                'appointment_date': '2016-03-23',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'BOARD_MEMBER',
                'principal_id': '999999-XXXX',
                'principal_name': 'Nilsson, John Tore Harald',
                'raw_function': 'Ledamot',
            },
            {
                'appointment_date': '2016-03-23',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'AUDITOR',
                'principal_id': '999999-XXXX',
                'principal_name': 'Öhrlings Pricewaterhousecoopers Ab',
                'raw_function': 'Revisor',
            },
            {
                'appointment_date': '2016-03-23',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'BOARD_MEMBER',
                'principal_id': '999999-XXXX',
                'principal_name': 'Fredholm, Cid Pether',
                'raw_function': 'Ledamot',
            },
            {
                'appointment_date': '2016-08-15',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'PRESIDENT',
                'principal_id': '999999-XXXX',
                'principal_name': 'Fredholm, Cid Pether',
                'raw_function': 'Ordförande',
            },
            {
                'appointment_date': '2017-06-13',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'BOARD_MEMBER',
                'principal_id': '999999-XXXX',
                'principal_name': 'Åkerlind, Mats Olof',
                'raw_function': 'Ledamot',
            },
            {
                'appointment_date': '2016-10-19',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': '',
                'principal_id': '999999-XXXX',
                'principal_name': 'Löfgren, Peter Lennart',
                'raw_function': 'Extern Verkställande Direktör',
            },
            {
                'appointment_date': '2016-03-23',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': 'LEAD_AUDITOR',
                'principal_id': '999999-XXXX',
                'principal_name': 'Rönnkvist, Johan André',
                'raw_function': 'Huvudansvarig Revisor',
            },
        ],
    })
    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    assert resp.headers['content-type'] == 'text/html; charset=UTF-8'
    assert b'href="/api/static/id06/report.css"' in resp.body
    assert (resp.body.find(b'Member of the board, president')
            < resp.body.find(b'<span>Fredholm, Cid Pether</span>')
            < resp.body.find(b'Board members'))


def test_company_report_unclassified_board_member(app, qvarn, set_feature_flags, storage):
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report={
        'board_members': [
            {
                'appointment_date': '2016-03-23',
                'boardMemberType': '',
                'disqualifications': '',
                'principal_function': PrincipalFunction.UNCLASSIFIED,
                'principal_id': '999999-XXXX',
                'principal_name': 'Nilsson, John Tore Harald',
                'raw_function': 'Something not expected',
            },
        ],
    })
    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    assert resp.headers['content-type'] == 'text/html; charset=UTF-8'
    assert b'href="/api/static/id06/report.css"' in resp.body
    assert (resp.body.find(b'Unclassified board members')
            < resp.body.find(b'<span>Nilsson, John Tore Harald</span>')
            < resp.body.find(b'External company signatory'))


def test_company_report_missing_address(app, qvarn, set_feature_flags, storage):
    """Regression BOL-2642."""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report={
        'visiting_addresses': [
            {'address': None,
             'post_code': None,
             'town': None}]
    })
    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))

    # There are no `None`s in the report
    assert str(resp.body).count('None') == 0


def test_find_company_with_country(app, storage):
    factories.create_org(storage, 'Kele Ltd.', registration_id='TC-1998')
    response = app.get_with_org('/api/find-company', dict(query='TC-1998', country='SWE'))
    assert len(response.json["results"]) > 0


def test_find_company_with_same_reg_as_other_company_f_tax(app, storage):
    """
        Find company should not search for f-tax. See BOL-4576.
        So if there's a company with f-tax number which is the same as another
        company's registration number we should only find one company.
    """
    org_name = 'Kele Ltd.'
    factories.create_org(storage, org_name, registration_id='516000-0000')
    gov_org_ids = [
        factories.make_f_tax(gov_org_id='516000-0000', country='FI')
    ]
    factories.create_org(
        storage, 'Kalasjärvi osakeyhtiö', gov_org_ids_raw=gov_org_ids, country='FI')
    response = app.get_with_org('/api/find-company', dict(query='516000-0000'))

    assert len(response.json["results"]) == 1
    company = response.json['results'][0]
    assert company['name'] == org_name

    # Prefix search should yield the same result.
    response = app.get_with_org('/api/find-company', dict(query='516000'))

    assert len(response.json["results"]) == 1
    company = response.json['results'][0]
    assert company['name'] == org_name


def test_find_company_case_insensitive(app, storage):
    org_name = 'Kele Ltd.'
    factories.create_org(storage, org_name, registration_id='TC-1001')
    response = app.get_with_org('/api/find-company', dict(query='tc-1001'))

    assert len(response.json["results"]) == 1
    company = response.json['results'][0]
    assert company['name'] == org_name


def test_find_company_with_country_no_data(app, storage):
    factories.create_org(storage, 'Kele Ltd.', registration_id='TC-1998')
    response = app.get_with_org('/api/find-company', dict(query='TC-1998', country='AZE'))
    assert len(response.json["results"]) == 0


def test_find_company_core_org_record(app_with_core):
    app = app_with_core
    mock_org = MOCK_ORGS['vaultit_ab']
    response = app.get_with_org(
        '/api/find-company',
        dict(query=mock_org['organisationIdentifier'],
             country=mock_org['countryCode'])
    )
    res = response.json["results"]
    assert len(res) == 1
    assert res[0]['gov_org_id'] == mock_org['organisationIdentifier']
    assert res[0]['name'] == mock_org['organisationName']
    assert res[0]['country'] == alpha3_country_code(mock_org['countryCode'])


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2(app, qvarn, set_feature_flags, storage,
                                      get_json_cs_v2, report_provider_cs_v2,
                                      mocker):
    """Smoke test for CreditsafeV2 report template rendering

    Pretty stupid and ugly.
    Strips tags and most of the whitespace and prints HTML as text.

    Goal - catch rogue or missing contents.
    """
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(report_provider_cs_v2, mocker)
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))

    soup = BeautifulSoup(resp.body, 'html.parser')
    # Normalize HTML
    soup = BeautifulSoup(soup.prettify(), 'html.parser')

    text = soup_prettify(soup.text)

    expected_text = """
        Company report
        Summary
        Company report
        Date report taken: 2021-06-11
        Latest registered change: 2021-06-11
        done
        ID06 AB
        This is the company name used by ID06:
        Org
        info_outline
        Organisation number:
        559052-2040
        This company fulfills the criteria in ID06 Bolagsdeklaration.
        done
        F-tax control:
        Holds F-tax
        done
        Company status:
        No status impact noted
        done
        Company/Organizational form for Swedish companies:
        Limited liability company
        done
        Swedish VAT register:
        Registered in the Swedish VAT-register
        done
        Swedish employer register:
        Registered in the employer register
        done
        Debt balance taxes and fees at Swedish Enforcement Authority:
        The company has no tax debts.
        done
        Debt balance company or private individual at Swedish Enforcement Authority:
        The company has no debts regarding company or private individuals
        done
        Control of requirement for auditor:
        Has auditor
        done
        Rating:
        Moderate risk to very low risk for insolvency within 12 months
        done
        Representative check Swedish AB:
        No negative information found
        Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act
        Information below is not covered by the publishing certificates
        Symbol meanings:
        done
        This company fulfills the criteria in ID06 Bolagsdeklaration.
        info_outline
        This company should provide additional information regarding the reported deviations or some information should be noted.
        help
        Information is missing or we are waiting for confirmation from Creditsafe.
        report_problem
        This company does not fulfill the criteria and needs to be investigated.
        pan_tool
        VERIFY THE COMPANY!
        Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act
        Information below is not covered by the publishing certificates
        Symbol legend
        Information sources
        Company information
        Updated: 2021-06-11
        (Source: Creditsafe)
        Organisation number
        559052-2040
        VAT number
        SE559052204001
        Company type
        AB
        (Limited liability company)
        Branch
        Other activities auxiliary to financial services, except insurance and pension funding
        Domicile
        10303 STOCKHOLM
        Registration information (Swedish register)
        Updated: 2021-06-11
        (Source: Creditsafe)
        Incorporation date
        2016-02-22
        F-tax control
        Holds F-tax
        Registration date for F-tax
        2016-09
        VAT register
        Yes
        Registration date for VAT
        2016-09
        Swedish employer register
        Yes
        Company status
        Updated: 2021-06-11
        (Source: Creditsafe)
        Company status
        done
        No status impact noted
        Rating
        Updated: 2021-06-11
        (Source: Creditsafe)
        Rating
        done
        Moderate risk to very low risk for insolvency within 12 months
        Rating comments
        This company has no payment remark related to public claims
        This company has changed address, board and/or industry
        This company has high cash ratio
        This company has a positive net profit margin
        This company has high total equity
        Swedish Enforcement Agency information
        Updated: 2021-06-11
        (Source: Creditsafe)
        Debt balance and payment applications
        Sum of debt balance
        0\xa0SEK
        - of which sum of public debts
        0\xa0SEK
        - of which sum of private debts
        0\xa0SEK
        The debt balance is attributed to the following number
        0
        - of which number of public debts
        0
        - of which number of private debts
        0
        Sum of payment applications
        0\xa0SEK
        Number of payment applications
        0
        Distraint attempt
        Distraint attempt
        No
        Control balance sheet
        Updated: 2021-06-11
        (Source: Creditsafe)
        Control balance sheet
        Not relevant based on account information and auditor's comments
        Payment remarks
        Updated: 2021-06-11
        (Source: Creditsafe)
        Payment remarks last 3-5 years
        Total sum of payment remarks
        0\xa0SEK
        - of which sum of public remarks*
        0\xa0SEK
        - of which sum of private remarks
        0\xa0SEK
        Number of remarks
        0
        - of which number of public remarks*
        0
        - of which number of private remarks
        0
        * Only specific public debts, such as Withholding tax, Payroll tax, Remaining tax, F-tax/Special A-tax, Tax account.
        Financial information (kSEK)
        Updated: 2021-06-11
        (Source: Creditsafe)
        Account period
        ********-********
        Information about employees
        Number of employees
        23
        Employer contributions
        2021-04: 516
        2021-03: 463
        2021-02: 482
        2021-01: 456
        2020-12: 456
        2020-11: 459
        2020-10: 446
        2020-09: 487
        2020-08: 453
        Estimated minimum payroll sum for last month based on 31,42% employer contributions
        1\xa0642
        Profit and loss sheet
        Net turnover
        134\xa0081
        Profit after tax
        4\xa0505
        Balance sheet
        Share capital
        5\xa0000
        Equity
        32\xa0726
        Short-term liability
        36\xa0690
        Long-term liability
        35\xa0000
        Total equity and liabilities
        104\xa0416
        Account key values
        Turnover per employee
        5\xa0830
        Solvency %
        31,3%
        Equity share %
        655%
        Quick ratio %
        121,0%
        Balance liquidity
        160%
        Audit information
        Updated: 2021-06-11
        (Source: Creditsafe)
        Account period
        ********-********
        Auditor
        done
        Has auditor
        Annual report endorsed
        The auditor has endorsed the annual report
        Auditor’s remarks and comments
        -
        Representatives
        Updated: 2021-06-11
        (Source: Creditsafe)
        Signing for company
        Signing for company
        Firman tecknas av styrelsen Firman tecknas två i förening av
        Fredholm, Cid Pether
        Gyrulf, Elin Christina
        Löfgren, Peter Lennart Dessutom har verkställande direktören rätt att teckna firman beträffande löpande förvaltningsåtgärder
        Other board positions
        Limited board information: members not all categories
        Antoni, Bo Sven
        491538XXXX
        Ledamot
        Appointment date
        2016-09-13
        Contact information and Group information
        Updated: 2021-06-11
        (Source: Creditsafe)
        Contact information
        Visiting address
        Klarabergsviadukten 63,
        11164,
        STOCKHOLM
        Post code
        10303
        Post town
        STOCKHOLM
        Telephone
        010-4809200
        Email
        Web address
        Group information
        Name of the group mother
        ID06 AB
        Organisation number of the group mother
        5590522040
        Country code of the group mother
        SE
        Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act
        Information above is not covered by the publishing certificates
    """  # noqa: E501
    expected = textwrap.indent(textwrap.dedent(expected_text), ' ')
    assert text == expected


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_report_reject(app, qvarn, storage,
                                                    set_feature_flags,
                                                    report_provider_cs_v2,
                                                    mocker):
    """Test display Cause_of_reject'"""
    provider = report_provider_cs_v2

    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')

    # Get report JSON
    mocker.patch('bolfak.statusreports.creditsafe.Creditsafe._fetch_entry',
                 return_value=get_creditsafe_error_response(
                     cause='S200',
                     message='Inaktivt',
                 ))
    token = provider.get_token_from_gov_org_id('559052-2040')
    data = provider.fetch([token])
    token, report = next(provider.parse([token], data))
    report['used_providers'] = {provider.name}
    finalize_report(report)
    # Store the report
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])
    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    # Get risk indicators
    indicators = soup.findAll('div', class_='collected-status')
    risk_texts = [soup_get_contained(soup, indicator)
                  for indicator in indicators]

    assert risk_texts == [
        ['pan_tool', 'Company status: Inactive'],
    ]

    # Get full report texts
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    # Normalize HTML
    soup = BeautifulSoup(soup.prettify(), 'html.parser')
    text = soup_prettify(soup.text)

    # * Should contain oly the basic report (no extended report).
    # * Status should be Warning
    expected_text = """
        Company report
        Summary
        Company report
        Date report taken: 2021-06-11
        Latest registered change: 2021-06-11
        pan_tool
        Org
        Organisation number:
        559052-2040
        VERIFY THE COMPANY!
        pan_tool
        Company status:
        Inactive
        Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act
        Information below is not covered by the publishing certificates
        Symbol meanings:
        done
        This company fulfills the criteria in ID06 Bolagsdeklaration.
        info_outline
        This company should provide additional information regarding the reported deviations or some information should be noted.
        help
        Information is missing or we are waiting for confirmation from Creditsafe.
        report_problem
        This company does not fulfill the criteria and needs to be investigated.
        pan_tool
        VERIFY THE COMPANY!
        Information presented in ID06 Bolagsdeklaration has been prepared by Creditsafe i Sverige AB (556514-4408), which is a credit reporting company authorized under the Credit Information Act
        Information below is not covered by the publishing certificates
        Symbol legend
    """  # noqa: E501
    expected = textwrap.indent(textwrap.dedent(expected_text), ' ')
    assert text == expected


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_section_rating(app, qvarn,
                                                     set_feature_flags,
                                                     storage, get_json_cs_v2,
                                                     report_provider_cs_v2,
                                                     mocker):
    """Test for section 'Rating'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(report_provider_cs_v2, mocker)
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Rating')

    expected = [
        'Rating Updated: 2021-06-11 (Source: Creditsafe)',
        'Rating',
        'done Moderate risk to very low risk for insolvency within 12 months',
        'Rating comments',
        'This company has no payment remark related to public claims',
        'This company has changed address, board and/or industry',
        'This company has high cash ratio',
        'This company has a positive net profit margin',
        'This company has high total equity',
    ]
    assert section_texts == expected


@freeze_time("2021-06-30")
def test_company_report_creditsafe_v2_section_rating_empty(app, qvarn,
                                                           set_feature_flags,
                                                           storage, get_json_cs_v2,
                                                           report_provider_cs_v2,
                                                           mocker):
    """Test for section 'Rating'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(report_provider_cs_v2, mocker,
                            rating='',
                            rating_text='',
                            commentary='',
                            n_commentary='')
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Rating')

    expected = [
        'Rating Updated: 2021-06-30 (Source: Creditsafe)',
        'Rating',
        'help No information available',
        'Rating comments No information available',
    ]
    assert section_texts == expected


@freeze_time("2021-06-30")
def test_company_report_creditsafe_v2_section_rating_0_commentary(
        app, qvarn, set_feature_flags, storage, get_json_cs_v2,
        report_provider_cs_v2, mocker):
    """Test for section 'Rating' when COMMENTARY==0"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(report_provider_cs_v2, mocker,
                            rating='',
                            rating_text='',
                            commentary='',
                            n_commentary='0')
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Rating')

    expected = [
        'Rating Updated: 2021-06-30 (Source: Creditsafe)',
        'Rating',
        'help No information available',
        'Rating comments There are no rating comments',
    ]
    assert section_texts == expected


def test_company_report_creditsafe_v2_risk_indicators(app, qvarn, set_feature_flags,
                                                      storage, json_cs_v2):
    """Smoke test for CreditsafeV2 risk indicators"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org, report=json_cs_v2, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    indicators = soup.findAll('div', class_='collected-status')
    risk_texts = [soup_get_contained(soup, indicator)
                  for indicator in indicators]

    expected = [
        ['done', 'F-tax control: Holds F-tax'],
        ['done', 'Company status: No status impact noted'],
        ['done', 'Company/Organizational form for Swedish companies: Limited liability company'],
        ['done', 'Swedish VAT register: Registered in the Swedish VAT-register'],
        ['done', 'Swedish employer register: Registered in the employer register'],
        ['done', 'Debt balance taxes and fees at Swedish Enforcement Authority: The company '
         'has no tax debts.'],
        ['done', 'Debt balance company or private individual at Swedish Enforcement '
         'Authority: The company has no debts regarding company or private '
         'individuals'],
        ['done', 'Control of requirement for auditor: Has auditor'],
        ['done', 'Rating: Moderate risk to very low risk for insolvency within 12 months'],
        ['done', 'Representative check Swedish AB: No negative information found'],
    ]
    assert risk_texts == expected


@freeze_time('2021-06-18')
@pytest.mark.parametrize(
    ['company_type', 'company_sole_trader', 'is_company_sole_trader_visible'],
    [
        ('AB', '', False),
        ('AB', 'ABC123', False),
        ('EF', '', False),
        ('EF', 'ABC123', True)
    ],
)
def test_company_report_creditsafe_v2_company_sole_trader(
    app, qvarn, storage, get_json_cs_v2, report_provider_cs_v2, mocker,
    company_type, company_sole_trader, is_company_sole_trader_visible,
):
    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        company_type=company_type,
        company_sole_trader=company_sole_trader,
    )

    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)

    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(
        BeautifulSoup(resp.body, 'html.parser').prettify(),
        'html.parser',
    )
    text = soup_prettify(soup.text)

    assert (f'Company sole trader: {company_sole_trader}' in text) is is_company_sole_trader_visible


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_section_payment_remarks(app, qvarn,
                                                              set_feature_flags,
                                                              storage,
                                                              get_json_cs_v2,
                                                              report_provider_cs_v2,
                                                              mocker):
    """Test for section 'Swedish Enforcement Agency information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        kf_remarks_claims=4,
        kf_remarks_private_amount=50000,
        kf_remarks_private_claims=1,
        n_kf_remarks_public_claims=3,
        kf_remarks_public_claims="".join("""
            <ROP_PUBLIC_CLAIMS
              diffgr:id="ROP_PUBLIC_CLAIMS1"
              msdata:rowOrder="0"
              diffgr:hasChanges="inserted"
            >
              <AMOUNT>5000</AMOUNT>
              <TYPE>10</TYPE>
            </ROP_PUBLIC_CLAIMS>
            <ROP_PUBLIC_CLAIMS
                diffgr:id="ROP_PUBLIC_CLAIMS2"
                msdata:rowOrder="1"
                diffgr:hasChanges="inserted"
            >
              <AMOUNT>**********-SPAM</AMOUNT>
              <TYPE>33</TYPE>
            </ROP_PUBLIC_CLAIMS>
            <ROP_PUBLIC_CLAIMS
                diffgr:id="ROP_PUBLIC_CLAIMS3"
                msdata:rowOrder="2"
                diffgr:hasChanges="inserted"
            >
              <AMOUNT>5000</AMOUNT>
              <TYPE>96</TYPE>
            </ROP_PUBLIC_CLAIMS>
        """)
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Payment remarks')

    expected = [
        'Payment remarks Updated: 2021-06-11 (Source: Creditsafe)',
        'Payment remarks last 3-5 years',
        'Total sum of payment remarks 60\xa0000\xa0SEK',
        '- of which sum of public remarks* 10\xa0000\xa0SEK',
        '- of which sum of private remarks 50\xa0000\xa0SEK',
        'Number of remarks 3',
        '- of which number of public remarks* 2',
        '- of which number of private remarks 1',
        '',
        '* Only specific public debts, such as Withholding tax, Payroll tax, Remaining '
        'tax, F-tax/Special A-tax, Tax account.',
    ]
    assert section_texts == expected


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_section_debt(app, qvarn,
                                                   set_feature_flags, storage,
                                                   get_json_cs_v2,
                                                   report_provider_cs_v2,
                                                   mocker):
    """Test for section 'Swedish Enforcement Agency information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(report_provider_cs_v2, mocker)
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Swedish Enforcement Agency information')

    expected = [
        'Swedish Enforcement Agency information Updated: 2021-06-11 (Source: Creditsafe)',
        'Debt balance and payment applications',
        '',
        'Sum of debt balance 0\xa0SEK',
        '- of which sum of public debts 0\xa0SEK',
        '- of which sum of private debts 0\xa0SEK',
        '',
        'The debt balance is attributed to the following number 0',
        '- of which number of public debts 0',
        '- of which number of private debts 0',
        '',
        'Sum of payment applications 0\xa0SEK',
        'Number of payment applications 0',
        '',
        'Distraint attempt',
        'Distraint attempt No',
    ]
    assert section_texts == expected


@freeze_time("2021-06-11")
@pytest.mark.parametrize(['kf_debt_date', 'expected'], [
    ('', ''),
    ('2020-11-11', 'Date for the latest debt balance 2020-11-11'),
])
def test_company_report_creditsafe_v2_section_debt_date(app, qvarn,
                                                        set_feature_flags, storage,
                                                        get_json_cs_v2,
                                                        report_provider_cs_v2,
                                                        mocker, kf_debt_date, expected):
    """Test for section 'Swedish Enforcement Agency information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(report_provider_cs_v2, mocker,
                            kf_debt_date=kf_debt_date)
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Swedish Enforcement Agency information')

    expected = [
        'Swedish Enforcement Agency information Updated: 2021-06-11 (Source: Creditsafe)',
        'Debt balance and payment applications',
        expected,
        'Sum of debt balance 0\xa0SEK',
        '- of which sum of public debts 0\xa0SEK',
        '- of which sum of private debts 0\xa0SEK',
        '',
        'The debt balance is attributed to the following number 0',
        '- of which number of public debts 0',
        '- of which number of private debts 0',
        '',
        'Sum of payment applications 0\xa0SEK',
        'Number of payment applications 0',
        '',
        'Distraint attempt',
        'Distraint attempt No',
    ]
    assert section_texts == expected


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_section_trustee(app, qvarn,
                                                      set_feature_flags, storage,
                                                      get_json_cs_v2,
                                                      report_provider_cs_v2,
                                                      mocker):
    """Test for section 'Trustee'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        trustee_name='Stolt, Per',
        trustee_company='ADVOKATFIRMAN AB',
        trustee_phone_direct='031-725 00 99',
        trustee_phone_central='031-725 00 00',
        trustee_visiting_address='NORRA GATAN 12',
        trustee_box_address='BOX 123',
        trustee_post_code='41200',
        trustee_town='GÖTEBORG',
        n_reconstruction_information=1,
        reconstruction_information=''.join("""
            <RECONSTRUCTION_INFORMATION
                diffgr:id="RECONSTRUCTION_INFORMATION1"
                msdata:rowOrder="0"
                diffgr:hasChanges="inserted"
            >
                <RECONSTRUCTION_TEXT>Reconstruction declared</RECONSTRUCTION_TEXT>
                <RECONSTRUCTION_DATE>2021-05-11</RECONSTRUCTION_DATE>
                <RECONSTRUCTION_ADM_NAME>Arebo, Tomas</RECONSTRUCTION_ADM_NAME>
                <RECONSTRUCTION_ADM_FIRM>Plana AB</RECONSTRUCTION_ADM_FIRM>
                <RECONSTRUCTION_ADM_VISIT_ADDRESS>Kroksdal 5</RECONSTRUCTION_ADM_VISIT_ADDRESS>
                <RECONSTRUCTION_ADM_BOX_ADDRESS>Box 2886</RECONSTRUCTION_ADM_BOX_ADDRESS>
                <RECONSTRUCTION_ADM_POST_CODE>18728</RECONSTRUCTION_ADM_POST_CODE>
                <RECONSTRUCTION_ADM_POST_TOWN>TÄBY</RECONSTRUCTION_ADM_POST_TOWN>
            </RECONSTRUCTION_INFORMATION>
        """),
        n_directors=1,
        directors=''.join("""
            <DIRECTORS
                diffgr:id="DIRECTORS1"
                msdata:rowOrder="0"
                diffgr:hasChanges="inserted"
            >
                <NAME>Antoni, Bo Sven</NAME>
                <FUNCTION>Likvidator</FUNCTION>
                <SOCSECURITYNR>**********</SOCSECURITYNR>
                <APPOINTMENTDATE>2016-09-01</APPOINTMENTDATE>
            </DIRECTORS>
        """)
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Trustee')

    expected = [
        'Trustee Updated: 2021-06-11 (Source: Creditsafe)',
        'Name Stolt, Per',
        'Function Bankruptcy administrator',
        'Company ADVOKATFIRMAN AB',
        'Telephone, direct 031-725 00 99',
        'Telephone, central 031-725 00 00',
        'Visiting address NORRA GATAN 12',
        'Box address BOX 123',
        'Post code 41200',
        'Post town GÖTEBORG',
        '',
        'Name Arebo, Tomas',
        'Function Reconstructor',
        'Current reconstruction information Reconstruction declared',
        'Date of current reconstruction information 2021-05-11',
        'Company Plana AB',
        'Visiting address Kroksdal 5',
        'Box address Box 2886',
        'Post code 18728',
        'Post town TÄBY',
        '',
        'Name Antoni, Bo Sven',
        'Function Likvidator',
        'Social security number 123456-XXXX',
        'Appointment date 2016-09-01',
    ]
    assert section_texts == expected


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_section_representatives(app, qvarn,
                                                              set_feature_flags, storage,
                                                              get_json_cs_v2,
                                                              report_provider_cs_v2,
                                                              mocker):
    """Test for section 'Trustee'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        n_directors=4,
        directors=''.join("""
            <DIRECTORS
                diffgr:id="DIRECTORS1"
                msdata:rowOrder="0"
                diffgr:hasChanges="inserted"
            >
                <NAME>Antoni, Bo Sven</NAME>
                <FUNCTION>Likvidator</FUNCTION>
                <SOCSECURITYNR>**********</SOCSECURITYNR>
                <APPOINTMENTDATE>2016-09-01</APPOINTMENTDATE>
            </DIRECTORS>
            <DIRECTORS
                diffgr:id="DIRECTORS2"
                msdata:rowOrder="1"
                diffgr:hasChanges="inserted"
            >
                <NAME>Stolt, Per</NAME>
                <FUNCTION>Verkställande direktör</FUNCTION>
                <SOCSECURITYNR>**********</SOCSECURITYNR>
                <APPOINTMENTDATE>2021-05-01</APPOINTMENTDATE>
            </DIRECTORS>
            <DIRECTORS
                diffgr:id="DIRECTORS3"
                msdata:rowOrder="2"
                diffgr:hasChanges="inserted"
            >
                <NAME>Stolt, Per</NAME>
                <FUNCTION>Ledamot</FUNCTION>
                <SOCSECURITYNR>**********</SOCSECURITYNR>
                <APPOINTMENTDATE>2021-05-01</APPOINTMENTDATE>
            </DIRECTORS>
            <DIRECTORS
                diffgr:id="DIRECTORS4"
                msdata:rowOrder="3"
                diffgr:hasChanges="inserted"
            >
                <NAME>Antoni, Bo Sven</NAME>
                <FUNCTION>Revisor</FUNCTION>
                <SOCSECURITYNR>**********</SOCSECURITYNR>
                <APPOINTMENTDATE>2016-09-01</APPOINTMENTDATE>
            </DIRECTORS>
        """)
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Representatives')

    expected = [
        'Representatives Updated: 2021-06-11 (Source: Creditsafe)',
        'CEO',
        'CEO or deputy CEO Stolt, Per',
        '012345-XXXX',
        'Verkställande direktör',
        'Appointment date 2021-05-01',
        '',
        '',
        'Signing for company',
        'Signing for company Firman tecknas av styrelsen Firman tecknas två i '
        'förening av Fredholm, Cid Pether Gyrulf, Elin Christina Löfgren, Peter '
        'Lennart Dessutom har verkställande direktören rätt att teckna firman '
        'beträffande löpande förvaltningsåtgärder',
        '',
        'Other board positions',
        'Limited board information: members not all categories',
        'Antoni, Bo Sven',
        '123456-XXXX',
        'Likvidator',
        'Appointment date 2016-09-01',
        '',
        'Stolt, Per',
        '012345-XXXX',
        'Ledamot',
        'Appointment date 2021-05-01',
        '',
        'Antoni, Bo Sven',
        '123456-XXXX',
        'Revisor',
        'Appointment date 2016-09-01',
    ]
    assert section_texts == expected


@freeze_time('2021-06-18')
def test_company_report_creditsafe_v2_section_contacts_missing_group(
        app, qvarn, set_feature_flags, storage, get_json_cs_v2,
        report_provider_cs_v2, mocker):
    """Test for section 'Trustee'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        groupmother_organisation_name='',
        groupmother_organisation_nr='',
        groupmother_country='',
    )

    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Contact information and Group information')

    # Should not show group/mother rows
    assert section_texts == [
        'Contact information and Group information Updated: 2021-06-18 (Source: '
        'Creditsafe)',
        'Contact information',
        'Visiting address Klarabergsviadukten 63, 11164, STOCKHOLM',
        'Post code 10303',
        'Post town STOCKHOLM',
        'Telephone 010-4809200',
        'Email',
        'Web address',
        'Group information',
        'The company is not part of a group',
    ]


@freeze_time("2021-06-11")
def test_company_report_creditsafe_v2_section_financial_information_no_empl_contributions(
    app, qvarn, set_feature_flags, storage, get_json_cs_v2, report_provider_cs_v2, mocker,
    employer_contributions='',
):
    """Test for section 'Financial information' if no employer contributions exist"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        employer_contributions=''
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Financial information (kSEK)')
    #  Making sure no employer contributions labels are visible without relevant data
    expected = [
        'Financial information (kSEK) Updated: 2021-06-11 (Source: Creditsafe)',
        'Account period ********-********',
        '',
        'Information about employees',
        'Number of employees 23',
        'Employer contributions -',
        'Estimated minimum payroll sum for last month based on 31,42% employer contributions -',
        '',
    ]
    assert section_texts[:8] == expected


@freeze_time("2021-06-11")
@pytest.mark.parametrize('nr_employees, nr_employees_int, nr_employees_expected', [
    ('13', '10-20', '13'),
    ('-', '10-20', '10\u002D20'),
    ('-', '-20000', '<\xa020\xa0000'),
    ('-', '10000-', '>\xa010\xa0000'),
    ('6.003', '6000-7000', '6.003'),
])
def test_company_report_creditsafe_v2_section_financial_information_nr_employees(
    app, qvarn, set_feature_flags, storage, get_json_cs_v2, report_provider_cs_v2, mocker,
    nr_employees, nr_employees_int, nr_employees_expected,
):
    """Test for section 'Financial information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        nr_employees=nr_employees,
        nr_employees_int=nr_employees_int,
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Financial information (kSEK)')

    expected = [
        'Financial information (kSEK) Updated: 2021-06-11 (Source: Creditsafe)',
        'Account period ********-********',
        '',
        'Information about employees',
        f'Number of employees {nr_employees_expected}',
        'Employer contributions',
        '2021-04: 516',
        '2021-03: 463',
        '2021-02: 482',
        '2021-01: 456',
        '2020-12: 456',
        '2020-11: 459',
        '2020-10: 446',
        '2020-09: 487',
        '2020-08: 453',
        'Estimated minimum payroll sum for last month based on 31,42% employer contributions',
        '1\xa0642',
        '',
        'Profit and loss sheet',
        'Net turnover 134\xa0081',
    ]
    assert section_texts[:20] == expected


@freeze_time("2021-06-11")
@pytest.mark.parametrize('turnover, turnover_int, turnover_expected', [
    ('88905', '50000 - 99999 tkr', '88\xa0905'),
    ('-', '50000 - 99999 tkr', '50\xa0000\u002D99\xa0999'),
    ('-', '-99999', '<\xa099\xa0999'),
    ('-', '50000-', '>\xa050\xa0000'),
])
def test_company_report_creditsafe_v2_section_financial_information_turnover(
    app, qvarn, set_feature_flags, storage, get_json_cs_v2, report_provider_cs_v2, mocker,
    turnover, turnover_int, turnover_expected,
):
    """Test for section 'Financial information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        net_turnover=turnover,
        turnover_interval=turnover_int,
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Financial information (kSEK)')

    expected = [
        'Financial information (kSEK) Updated: 2021-06-11 (Source: Creditsafe)',
        'Account period ********-********',
        '',
        'Information about employees',
        'Number of employees 23',
        'Employer contributions',
        '2021-04: 516',
        '2021-03: 463',
        '2021-02: 482',
        '2021-01: 456',
        '2020-12: 456',
        '2020-11: 459',
        '2020-10: 446',
        '2020-09: 487',
        '2020-08: 453',
        'Estimated minimum payroll sum for last month based on 31,42% employer contributions',
        '1\xa0642',
        '',
        'Profit and loss sheet',
        f'Net turnover {turnover_expected}',
    ]
    assert section_texts[:20] == expected


@freeze_time("2021-06-11")
@pytest.mark.parametrize('share_capital, expected', [
    ('5000000SEK', '5\xa0000'),
    ('5000000', '5\xa0000'),
    ('0', '0'),
    ('-', '-'),
    ('?', '-'),
    ('', '-'),
])
def test_company_report_creditsafe_v2_section_financial_information_share_capital(
    app, qvarn, set_feature_flags, storage, get_json_cs_v2, report_provider_cs_v2, mocker,
    share_capital, expected,
):
    """Test for section 'Financial information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        share_capital=share_capital,
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Financial information (kSEK)')

    assert f'Share capital {expected}' in section_texts


@freeze_time("2021-06-11")
@pytest.mark.parametrize('equity_share, expected', [
    ('655%', '655%'),
    ('31,3%', '31,3%'),
    ('-171,5%', '-171,5%'),
    ('-5768%', '-5\xa0768%'),
    ('-5768,2%', '-5\xa0768,2%'),
])
def test_company_report_creditsafe_v2_section_financial_information_equity_share_percent(
    app, qvarn, set_feature_flags, storage, get_json_cs_v2, report_provider_cs_v2, mocker,
    equity_share, expected,
):
    """Test for section 'Financial information'"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        equity_share=equity_share,
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Financial information (kSEK)')

    expected = [
        f'Equity share % {expected}',
    ]
    assert section_texts[32:33] == expected


@freeze_time('2021-07-09')
@pytest.mark.parametrize(['account_notes_comment', 'exp_approved', 'exp_comments'], [
    (
        '-',
        '-',
        ['-'],
    ),
    (
        'Short comment;Longer comment',
        'The auditor has endorsed the annual report with comments',
        ['Short comment', 'Longer comment'],
    ),
])
def test_company_report_creditsafe_v2_section_audit_information(
        app, qvarn, set_feature_flags, storage, get_json_cs_v2, report_provider_cs_v2,
        mocker, account_notes_comment, exp_approved, exp_comments
):
    """Test for section Audit information"""
    set_feature_flags({'extended_report': True})

    org = factories.get_or_create_org(storage, 'Org')
    report = get_json_cs_v2(
        report_provider_cs_v2,
        mocker,
        revision_approved='Med komm',
        n_acc_comment_text=account_notes_comment,
    )
    factories.create_report(storage, org, report=report, used_providers=['creditsafe_v2'])

    app.login(org, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')

    section_texts = soup_get_section(soup, 'Audit information')

    expected = [
        'Audit information Updated: 2021-07-09 (Source: Creditsafe)',
        'Account period ********-********',
        'Auditor done Has auditor',
        f'Annual report endorsed {exp_approved}',
        '',
        'Auditor’s remarks and comments',
        *exp_comments,
    ]
    assert section_texts == expected


@freeze_time('2023-05-25')
# Parametrize this and ensure it all werks...
@pytest.mark.parametrize('nr_employees, employees_expected_report, employees_expected_template', [
    ('8.017', '8.017', '8.017'),
    ('800', '800', '800'),
    ('0', '0', '0'),
    (640, 640, '640'),
    ('80 to 100', '80 to 100', '80 to 100'),
    ('80 - 100', '80 - 100', '80 - 100'),
    ('1-3', '1-3', '1-3'),
    ('', '', '-'),
    ('two hundred', 'two hundred', 'two hundred'),
    ([], None, '-')
])
def test_company_report_creditsafe_connect_employee_number_with_dot(storage, connect_provider, app,
                                                                    set_feature_flags, nr_employees,
                                                                    employees_expected_report,
                                                                    employees_expected_template):
    set_feature_flags({'extended_report': True})
    org1 = factories.create_org(
        storage,
        country='EE',
        gov_org_ids_raw=[
            factories.make_reg_number('10015858', 'EE'),
        ]
    )
    token = connect_provider.get_token(org1)
    with open(get_fixture_path('report_data/report_connect_ee.json')) as f:
        data = json.loads(f.read())
    if nr_employees == []:
        data['report']['otherInformation']['employeesInformation'] = []
    else:
        data['report']['otherInformation']['employeesInformation'].insert(0, {
            'year': 2023,
            'numberOfEmployees': nr_employees,
        })
    client = FakeCreditsafeConnectClient(response=json.dumps(data))
    connect_provider.report_data_client = client
    data = connect_provider.fetch([token])
    token, report = next(connect_provider.parse([token], data))
    org2 = factories.get_or_create_org(storage, 'Org')
    factories.create_report(storage, org2, report=report, used_providers=['creditsafe_connect'])

    app.login(org2, admin=True)
    resp = app.get_with_org('/api/company/{}/report'.format(org2['id']))
    soup = BeautifulSoup(resp.body, 'html.parser')
    text = soup.find(id='nr-employees').text

    assert report['nr_employees'] == employees_expected_report
    assert text.strip() == employees_expected_template


@pytest.mark.slow
def test_i18n_multi_threading_support(App, company_list_fixture_main, storage):
    company = find_company_by_reg_no(storage, 'TC-1001')

    # Do this in the main thread once so we won't try to race doing it in other threads
    person = factories.get_or_create_person(storage, 'Thomas Anmderson')
    factories.get_or_create_user_account(storage, person, company)

    errors = []

    def test(lang, title):
        try:
            bottle.request = bottle.LocalRequest()  # I don't understand this either!
            app = App()
            app.login(company, person)
            app.set_cookie('used_ui_language', lang)
            resp = app.get_with_org('/api/company/{}/report'.format(company['id']))
            result = resp.html.title.string
            if result != title:
                errors.append('language={} returned {}'.format(lang, repr(result)))
        except Exception as e:
            logging.exception("Failure in thread")
            errors.append('{}: {}'.format(e.__class__.__name__, e))

    threads = []
    for i in range(100):
        if i % 2 == 0:
            lang, title = ('en', 'Company report')
        else:
            lang, title = ('sv', 'Företagsrapport')
        threads.append(threading.Thread(target=test, args=(lang, title)))

    for thread in threads:
        thread.start()

    for thread in threads:
        thread.join()

    assert errors == []


@pytest.mark.parametrize('dash', [
    '\u2012',
    '\u2013',
    '\u2014',
    '\u2015'
])
def test_find_company_alternatives_dashes(app, storage, dash):
    factories.create_org(storage, 'Widgets Ltd.', registration_id='3145-9876-X')
    response = app.get_with_org('/api/find-company', dict(query='3145{}9876{}X'.format(dash, dash)))
    assert len(response.json['results']) == 1


def test_find_company_when_not_found(app, storage):
    factories.create_org(storage, 'Widgets Ltd.', registration_id='3145-9876-X')
    response = app.get_with_org('/api/find-company', dict(query='31415926535897932384'))
    assert response.json['results'] == []


def test_find_company_when_empty_search_string(app, storage):
    factories.create_org(storage, 'Widgets Ltd.', registration_id='3145-9876-X')
    response = app.get_with_org('/api/find-company')
    # Expect 3 companies: Trafikverket, 2nd user company and Widgets Ltd.
    assert len(response.json['results']) == 3


def test_find_company_when_found(app, storage):
    orgs = [
        factories.create_org(storage, 'Widgets Ltd.', registration_id='3145-9876-X'),
        factories.create_org(storage, "Pancakes'R'Us", registration_id='4567-9876-Y'),
        factories.create_org(storage, "Asphalt Mixers Co", registration_id='3100-9876-Z'),
    ]
    response = app.get_with_org('/api/find-company', dict(query='31'))
    assert sorted(response.json['results'], key=itemgetter('name')) == [
        {'id': orgs[2]['id'],
         'name': 'Asphalt Mixers Co',
         'country': 'SWE',
         'external_id': None,
         'gov_org_id': '3100-9876-Z'},
        {'id': orgs[0]['id'],
         'name': 'Widgets Ltd.',
         'country': 'SWE',
         'external_id': None,
         'gov_org_id': '3145-9876-X'},
    ]


# Skipped due to qvarn.update refactored out!
# Is there any way to set two names for a company at Core?
@pytest.mark.skip
def test_find_company_multiple_names(app, qvarn, storage):
    org = factories.create_org(storage, 'Widgets Ltd.', registration_id='3145-9876-X')
    org['names'] = ['Widgets Ltd.', 'Obsolete name']
    qvarn.update('orgs', org['id'], org)

    response = app.get_with_org('/api/find-company', dict(query='31'))
    assert response.json['results'] == [
        {'id': org['id'],
         'name': 'Widgets Ltd.',
         'country': 'SWE',
         'gov_org_id': '3145-9876-X'},
    ]


@pytest.mark.parametrize(['import_sole_traders', 'expected_error'], [
    (True, 'Not querying Creditsafe about private person.'),
    (False, 'Not querying Creditsafe about sole trader - feature disabled.'),
])
def test_find_company_for_project_nonbusiness_person(app, storage, config, mocker,
                                                     import_sole_traders, expected_error):
    config.read_dict({'feature-flags': {'import_sole_traders': import_sole_traders}})
    person_id = '810101-0001'
    mocker.patch('bolfak.statusreports.sandbox.DATABASE', {
        person_id: company_template('810101-0001', 'Private person',
                                    company_format='Something else'),
    })

    response = app.get_with_org('/api/find-company', dict(query=person_id))
    assert response.json[STATUS_OK] is False
    assert response.json['results'] == []
    assert response.json['errors']['gov_org_id'] == [expected_error]


def test_company_details(app, storage):
    expected_gov_org_ids = [
        {
            'country': 'SWE',
            'gov_org_id': '852372-1298',
            'org_id_type': 'registration_number',
        },
        {
            'country': 'SWE',
            'gov_org_id': 'ABC12345',
            'org_id_type': 'vat_number',
        },
    ]

    supplier_gov_org_ids = [make_reg_number('852372-1298'), make_vat('ABC12345')]
    factories.create_org(storage, 'Supplier', gov_org_ids_raw=supplier_gov_org_ids)
    factories.create_suppliers_tree(storage, 'My Project', [
        factories.supplier('Main Supplier', supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Supplier'),
        ]),
    ])

    user_contract = app.login('Main Supplier')
    factories.create_project_user_contract(storage, 'My Project', user_contract, 'Main Supplier')
    org = get_org(storage, name='Supplier')
    factories.create_report(storage, org)
    response = app.get_with_org('/api/company/%s' % org['id'])
    assert response.json == {
        'name': 'Supplier',
        'status': {
            'overall_status_icon': 'done',
            'items': [
                # Ignore employer_contributions
                # (https://jira.tilaajavastuu.fi/browse/BOL-1836) until
                # Skatteverket is re-eabled
                # {
                #     'icon': 'help',
                #     'ifmissing': '-',
                #     'interpretation': 'incomplete',
                #     'label': {'en': 'Employer tax contributions',
                #               'sv': 'Arbetsgivaravgifter'},
                #     'source': '',
                #     'status': {'en': 'Employer contribution declaration missing',
                #                'sv': 'Arbetsgivardeklaration saknas'},
                #     'updated': None,
                # },
                {
                    'icon': 'info_outline',
                    'ifmissing': '-',
                    'interpretation': STATUS_ATTENTION,
                    'label': {'en': 'Company registration',
                              'sv': 'Bolagsregistrering',
                              'fi': 'Company registration'},
                    'source': '',
                    'status': {'en': ('Information about the company registration not found in '
                                      'Bolagsverket.'),
                               'sv': 'Information om företagsregistrering i Bolagsverket saknas',
                               'fi': ('Information about the company registration not found in '
                                      'Bolagsverket.')},
                    'updated': None,
                }
            ],
            'overall_status': STATUS_OK,
            'overall_status_text': {
                'en': 'OK',
                'sv': 'OK',
                'fi': 'OK',
            },
            'updated_date': None,
        },
        'country': 'SWE',
        'company_id': org['id'],
        'gov_org_ids': expected_gov_org_ids,
        'permissions': ['view_related_projects'],
        'company_details_url':
        f'https://companies-example.id06.se/#/{org["id"]}/organization/basicinfo'
    }


@pytest.mark.parametrize(
        'can_view_comments, can_write_comments, feature_enabled, has_comment_permission', [
            (True, True, True, True),
            (False, False, True, False),
            (True, True, False, False),
            (False, False, False, False)
        ])
def test_company_details_with_comments_permissions(app, storage, can_view_comments,
                                                   can_write_comments, has_comment_permission,
                                                   set_feature_flags, feature_enabled, mocker):
    set_feature_flags({'project_supplier_comments': feature_enabled})
    auth_function_mock = mocker.patch(
        'bolfak.views.companies.authorization.can_view_supplier_comments',
        return_value=can_view_comments
    )
    auth_function_mock = mocker.patch(
        'bolfak.views.companies.authorization.can_create_supplier_comments',
        return_value=can_write_comments
    )
    expected_gov_org_ids = [
        {
            'country': 'SWE',
            'gov_org_id': '852372-1298',
            'org_id_type': 'registration_number',
        },
        {
            'country': 'SWE',
            'gov_org_id': 'ABC12345',
            'org_id_type': 'vat_number',
        },
    ]

    supplier_gov_org_ids = [make_reg_number('852372-1298'), make_vat('ABC12345')]
    factories.create_org(storage, 'Supplier', gov_org_ids_raw=supplier_gov_org_ids)
    tree = factories.create_suppliers_tree(storage, 'My Project', [
        factories.supplier('Main Supplier', supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Supplier'),
        ]),
    ])
    user_contract = app.login('Main Supplier')
    factories.create_project_user_contract(storage, 'My Project', user_contract, 'Main Supplier')
    org = get_org(storage, name='Supplier')
    factories.create_report(storage, org)
    supplier_id = tree[0].subcontractors[0].id
    response = app.get_with_org(f'/api/company/{org["id"]}?supplierId={supplier_id}')
    assert auth_function_mock.called is feature_enabled
    if has_comment_permission:
        permissions = ["view_related_projects", "view_supplier_comments", "write_supplier_comments"]
    else:
        permissions = ['view_related_projects']

    assert response.json == {
        'name': 'Supplier',
        'status': {
            'overall_status_icon': 'done',
            'items': [
                {
                    'icon': 'info_outline',
                    'ifmissing': '-',
                    'interpretation': STATUS_ATTENTION,
                    'label': {'en': 'Company registration',
                              'sv': 'Bolagsregistrering',
                              'fi': 'Company registration'},
                    'source': '',
                    'status': {'en': ('Information about the company registration not found in '
                                      'Bolagsverket.'),
                               'sv': 'Information om företagsregistrering i Bolagsverket saknas',
                               'fi': ('Information about the company registration not found in '
                                      'Bolagsverket.')},
                    'updated': None,
                }
            ],
            'overall_status': STATUS_OK,
            'overall_status_text': {
                'en': 'OK',
                'sv': 'OK',
                'fi': 'OK',
            },
            'updated_date': None,
        },
        'country': 'SWE',
        'company_id': org['id'],
        'gov_org_ids': expected_gov_org_ids,
        'permissions': permissions,
        'company_details_url':
        f'https://companies-example.id06.se/#/{org["id"]}/organization/basicinfo'
    }


def test_company_details_employer_contributions(app, set_feature_flags, storage):
    set_feature_flags({'report_employer_contributions': True})

    factories.create_suppliers_tree(storage, 'My Project', [
        factories.supplier('Main Supplier', supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Supplier'),
        ]),
    ])

    user_contract = app.login('Main Supplier')
    factories.create_project_user_contract(storage, 'My Project', user_contract, 'Main Supplier')
    org = get_org(storage, name='Supplier')
    factories.create_report(storage, org)
    response = app.get_with_org('/api/company/%s' % org['id'])
    assert response.json['status']['items'] == [
        {
            'icon': 'help',
            'ifmissing': '-',
            'interpretation': 'incomplete',
            'label': {'en': 'Employer tax contributions',
                      'sv': 'Arbetsgivaravgifter',
                      'fi': 'Employer tax contributions'},
            'source': '',
            'status': {'en': 'Employer contribution declaration missing',
                       'sv': 'Arbetsgivardeklaration saknas',
                       'fi': 'Employer contribution declaration missing'},
            'updated': None,
        },
        {
            'icon': 'info_outline',
            'ifmissing': '-',
            'interpretation': STATUS_ATTENTION,
            'label': {'en': 'Company registration',
                      'sv': 'Bolagsregistrering',
                      'fi': 'Company registration'},
            'source': '',
            'status': {'en': ('Information about the company registration not found in '
                              'Bolagsverket.'),
                       'sv': 'Information om företagsregistrering i Bolagsverket saknas',
                       'fi': ('Information about the company registration not found in '
                              'Bolagsverket.')},
            'updated': None,
        }
    ]


def test_company_details_report_intricacies(app, storage):
    factories.create_suppliers_tree(storage, 'My Project', [
        factories.supplier('Main Supplier', supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Supplier'),
        ]),
    ])

    user_contract = app.login('Main Supplier')
    factories.create_project_user_contract(storage, 'My Project', user_contract, 'Main Supplier')
    org = get_org(storage, name='Supplier')
    factories.create_report(storage, org, report={
        'clreports_interpretation': 'Incomplete',
        'tax_interpretation': 'incomplete',
        'tax_format': 'very messy',
        'tax_updated': '2017-05-16',
        'tax_source': 'Grapevine',
        'report_creation_date': '2021-11-15',
    })
    response = app.get_with_org('/api/company/%s' % org['id'])
    assert response.json == {
        'name': 'Supplier',
        'status': {
            'overall_status': 'incomplete',
            'overall_status_icon': 'help',
            'overall_status_text': {
                'en': 'Incomplete',
                'sv': 'OFULLSTÄNDIG',
                'fi': 'Tietoja odotetaan',
            },
            'updated_date': '2021-11-15',
            'items': [
                {
                    'icon': 'help',
                    'ifmissing': 'Tax form is missing',
                    'interpretation': 'incomplete',
                    'label': {
                        'en': 'Tax form',
                        'sv': 'Skatteform',
                        'fi': 'Tax form',
                    },
                    'source': 'Grapevine',
                    'status': {
                        'en': 'very messy',
                        'sv': 'very messy',
                        'fi': 'very messy',
                    },
                    'updated': '2017-05-16',
                },
                {
                    'icon': 'info_outline',
                    'ifmissing': '-',
                    'interpretation': STATUS_ATTENTION,
                    'label': {'en': 'Company registration',
                              'sv': 'Bolagsregistrering',
                              'fi': 'Company registration'},
                    'source': '',
                    'status': {'en': ('Information about the company registration not found in '
                                      'Bolagsverket.'),
                               'sv': 'Information om företagsregistrering i Bolagsverket saknas',
                               'fi': ('Information about the company registration not found in '
                                      'Bolagsverket.')},
                    'updated': None
                }
            ],
        },
        'country': 'SWE',
        'company_id': org['id'],
        'gov_org_ids': [
            {
                'country': 'SWE',
                'gov_org_id': '852372-1298',
                'org_id_type': 'registration_number',
            },
        ],
        'permissions': ['view_related_projects'],
        'company_details_url':
        f'https://companies-example.id06.se/#/{org["id"]}/organization/basicinfo'
    }


@pytest.mark.parametrize('url', [
    '/api/company/{id}',
    '/api/company/{id}/related-projects',
])
def test_company_views_404_when_not_authorized(app, storage, url):
    factories.create_suppliers_tree(storage, 'My Project', [
        factories.supplier('Main Supplier', subcontractors=[
            factories.supplier('Supplier'),
        ]),
    ])

    app.login('Supplier')
    org = get_org(storage, name='Main Supplier')
    app.get_with_org(url.format_map(org), status=404)


def test_company_related_projects(app, storage):
    org = factories.create_org(storage, 'Widgets Ltd')
    user_contract = app.login(org, role=USER_ROLE_BASIC)
    factories.create_suppliers_tree(storage, 'Circle Road Construction', [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_project_user_contract(storage, 'Circle Road Construction', user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)
    factories.create_suppliers_tree(storage, 'Intersection Near My House', [
        factories.supplier("Gadgets'R'Us", supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Widgets Ltd'),
        ]),
        factories.supplier('Widgets Ltd', supplier_role=MAIN_CONTRACTOR_ROLE),
    ])
    factories.create_project_user_contract(
        storage, 'Intersection Near My House', user_contract, org,
        role=PROJECT_USER_ROLE_MEMBER)
    factories.create_suppliers_tree(storage, 'Some Boring Work That Needs No Widgets', [
        factories.supplier("Gadgets'R'Us", supplier_role=SUPERVISOR_ROLE),
    ])
    response = app.get_with_org('/api/company/%s/related-projects' % org['id'])
    projects = response.json['related_projects']
    assert [p['name'] for p in projects] == [
        'Circle Road Construction',
        'Intersection Near My House',
    ]
    expected_keys = {
        'id', 'name', 'project_id', 'tax_id', 'start_date', 'end_date', 'status',
    }
    # Add comment-related keys if feature is active
    if feature_active('project_supplier_comments'):
        expected_keys.update({'can_view_comments', 'has_comments'})
    assert projects[0].keys() == expected_keys

    # Test comment-related fields when feature is active
    if feature_active('project_supplier_comments'):
        assert isinstance(projects[0]['can_view_comments'], bool)
        assert isinstance(projects[0]['has_comments'], bool)


def test_company_related_projects_status_added_client_not_confirmed(
        app, storage, ff_add_project_client_on):
    # https://vaultit.atlassian.net/browse/BOL-5763 specific test
    org_mc = factories.create_org(storage, 'MC')
    org_client = factories.create_org(storage, 'Client')
    # Login as Main Contractor
    app.login(org_mc, role=USER_ROLE_MAIN)
    # Create a project, add a project client
    project = factories.create_default_project(storage, 'added client project',
                                               added_client_confirmed=False,
                                               project_creator_role=MAIN_CONTRACTOR_ROLE,
                                               client_company_id=org_client['id'])
    factories.create_suppliers_tree(storage, project, [
        factories.supplier('MC', supplier_role=MAIN_CONTRACTOR_ROLE),
        factories.supplier('Client', supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_report(storage, org_mc, status=STATUS_OK)
    factories.create_report(storage, org_client, status=STATUS_INVESTIGATE)
    # Check project status in related projects
    response = app.get_with_org(f"/api/company/{org_mc['id']}/related-projects")
    projects = response.json['related_projects']
    # Project status is visible in related projects for other interested orgs,
    # except Client, when Client has not confirmed access.
    assert get_cols(projects, ['name', 'status']) == [
        ('added client project', STATUS_INVESTIGATE)
    ]
    # Login as Client
    app.login(org_client, role=USER_ROLE_MAIN)
    response = app.get_with_org(f"/api/company/{org_client['id']}/related-projects")
    projects = response.json['related_projects']
    # The project status should be hidden only for Client, until they confirm access
    # to the project.
    assert get_cols(projects, ['name', 'status']) == [
        ('added client project', None)
    ]


def test_company_related_projects_with_supplier_comments_feature(AppWithFeatureFlags, storage):
    """Test that related projects include comment fields when feature is enabled."""
    app = AppWithFeatureFlags({
        'project_supplier_comments': True,
        'bda_project_suppliers': True
    })

    # Use the same pattern as the existing test
    org = factories.create_org(storage, 'Test Company')
    user_contract = app.login(org, role=USER_ROLE_BASIC)

    # Create multiple projects to test concurrent processing (threshold is 3)
    projects = []
    for i in range(4):
        project_name = f'Test Project {i+1}'

        # Create suppliers tree with the org as a supplier
        factories.create_suppliers_tree(storage, project_name, [
            factories.supplier(org, supplier_role=SUPERVISOR_ROLE),
        ])
        factories.create_project_user_contract(storage, project_name, user_contract, org,
                                               role=PROJECT_USER_ROLE_MEMBER)

        # Get the project and supplier for adding comments
        project = factories.get_or_create_project(storage, project_name)
        suppliers_tree = factories.create_suppliers_tree(
            storage, project, [factories.supplier(org)])
        supplier_id = suppliers_tree[0].id

        # Add a comment for the first project only
        if i == 0:
            factories.create_supplier_comment(
                storage,
                project_id=project['id'],
                supplier_id=supplier_id,
                org_id=org['id'],
                commentor_person_id='test-person',
                commentor_org_id='test-org',
                text='Test comment'
            )

        projects.append(project)

    # Test the API
    resp = app.get_with_org(f"/api/company/{org['id']}/related-projects")
    assert resp.status_code == 200

    projects_response = resp.json['related_projects']
    assert len(projects_response) == 4

    # Check that all projects have comment-related fields
    for project_data in projects_response:
        assert 'can_view_comments' in project_data
        assert 'has_comments' in project_data
        assert isinstance(project_data['can_view_comments'], bool)
        assert isinstance(project_data['has_comments'], bool)

    # Find the first project which should have comments
    project_1 = next((p for p in projects_response if p['name'] == 'Test Project 1'), None)
    assert project_1 is not None
    assert project_1['has_comments'] is True

    # Other projects should not have comments
    for i in range(2, 5):
        project = next((p for p in projects_response if p['name'] == f'Test Project {i}'), None)
        assert project is not None
        assert project['has_comments'] is False


def test_company_related_projects_block_project_client(app, storage, ff_block_project_client_on):
    org = factories.create_org(storage, 'Widgets Ltd')
    user_contract = app.login(org, role=USER_ROLE_BASIC)
    p1 = factories.create_default_project(storage,
                                          'Circle Road Construction',
                                          project_creator_role=MAIN_CONTRACTOR_ROLE,
                                          client_company_id=org['id'],
                                          added_client_confirmed=True,
                                          added_client_can_view=True)
    p2 = factories.create_default_project(storage,
                                          'Intersection Near My House',
                                          project_creator_role=MAIN_CONTRACTOR_ROLE,
                                          client_company_id=org['id'],
                                          added_client_confirmed=True,
                                          added_client_can_view=False)
    p3 = factories.create_default_project(storage,
                                          'project 3',
                                          project_creator_role=MAIN_CONTRACTOR_ROLE,
                                          client_company_id=org['id'],
                                          added_client_confirmed=False,
                                          added_client_can_view=True)
    p4 = factories.create_default_project(storage,
                                          'project 4',
                                          project_creator_role=MAIN_CONTRACTOR_ROLE,
                                          client_company_id=org['id'],
                                          added_client_confirmed=False,
                                          added_client_can_view=False)
    factories.create_suppliers_tree(storage, p1, [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_project_user_contract(storage, p1, user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)
    factories.create_project_user_contract(storage, p2, user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)
    factories.create_suppliers_tree(storage, p2, [
        factories.supplier("Gadgets'R'Us", supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_suppliers_tree(storage, p2, [
        factories.supplier("Gadgets'R'Us", supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Widgets Ltd'),
        ]),
        factories.supplier('Widgets Ltd', supplier_role=MAIN_CONTRACTOR_ROLE),
    ])
    factories.create_suppliers_tree(storage, p3, [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_project_user_contract(storage, p3, user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)
    factories.create_suppliers_tree(storage, p4, [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_project_user_contract(storage, p4, user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)

    response = app.get_with_org('/api/company/%s/related-projects' % org['id'])
    projects = response.json['related_projects']
    assert [p['name'] for p in projects] == [p1['names'][0]]


def test_company_related_projects_not_hidden_for_supervisor(
        app, storage, ff_block_project_client_on):
    org_client = factories.create_org(storage, 'Client org')
    org_mc = factories.create_org(storage, 'Main contractor org')
    org_supervisor = factories.create_org(storage, 'Supervisor org')
    org_supplier = factories.create_org(storage, 'Supplier org')
    user_account_client = factories.get_or_create_user_account(
        storage, org_client, 'Client')
    user_account_mc = factories.get_or_create_user_account(
        storage, org_mc, 'Main contractor')

    # Logged in as the supervisor
    user_account_supervisor = app.login(org_supervisor, role=USER_ROLE_BASIC)
    p = factories.create_default_project(
        storage,
        'Circle Road Construction',
        project_leader=user_account_client,
        project_creator_role=CLIENT_ROLE,
        client_company_id=org_client['id'],
        added_client_confirmed=None,
        added_client_can_view=False)
    factories.create_suppliers_tree(storage, p, [
        factories.supplier(org_mc, supplier_role=MAIN_CONTRACTOR_ROLE, subcontractors=[
            factories.supplier(org_supplier)
        ]),
        factories.supplier(org_supervisor, supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_project_user_contract(storage, p, user_account_mc, org_mc,
                                           role=PROJECT_USER_ROLE_MANAGER)
    factories.create_project_user_contract(storage, p, user_account_supervisor, org_supervisor,
                                           role=PROJECT_USER_ROLE_MANAGER)

    for org in [org_mc, org_supervisor, org_supplier]:
        response = app.get_with_org(f'/api/company/{org["id"]}/related-projects')
        projects = response.json['related_projects']
        assert [_p['name'] for _p in projects] == [p['names'][0]]


def test_company_related_old_projects(storage, app,
                                      ff_add_project_client_on,
                                      ff_block_project_client_on):
    org_1 = factories.create_org(storage, 'org 1')
    org_2 = factories.create_org(storage, 'org 2')
    app.login(org_1)
    p1 = factories.create_default_project(storage, 'project 1')
    factories.create_suppliers_tree(storage, p1, [
        factories.supplier('org 1', supplier_role=MAIN_CONTRACTOR_ROLE),
        factories.supplier('org 2')
    ])
    response = app.get_with_org('/api/company/%s/related-projects' % org_2['id'])
    projects = response.json['related_projects']
    assert [p['name'] for p in projects] == [p1['names'][0]]


def get_cols(items, cols):
    return list(map(itemgetter(*cols), items))


def test_company_related_projects_status(app, storage):
    org = factories.create_org(storage, 'Widgets Ltd')

    factories.create_suppliers_tree(storage, 'Intersection Near My House', [
        factories.supplier('Widgets Ltd', supplier_role=MAIN_CONTRACTOR_ROLE),
        factories.supplier("Gadgets'R'Us", supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_report(storage, 'Widgets Ltd', status=STATUS_OK)
    factories.create_report(storage, "Gadgets'R'Us", status=STATUS_INVESTIGATE)

    response = app.get_with_org('/api/company/%s/related-projects' % org['id'])
    projects = response.json['related_projects']

    assert get_cols(projects, ['name', 'status']) == [
        ('Intersection Near My House', STATUS_INVESTIGATE)
    ]


def test_company_related_projects_status_visitors(app, storage):
    org = factories.create_org(storage, 'Widgets Ltd')

    factories.create_suppliers_tree(storage, 'Intersection Near My House', [
        factories.supplier('Widgets Ltd', supplier_role=MAIN_CONTRACTOR_ROLE),
        factories.supplier("Gadgets'R'Us", supplier_role=SUPERVISOR_ROLE),
        factories.supplier('Tresspas Co', supplier_type=VISITOR),
    ])
    factories.create_report(storage, "Gadgets'R'Us", status=STATUS_INVESTIGATE)
    factories.create_report(storage, 'Tresspas Co', status=STATUS_STOP)

    response = app.get_with_org('/api/company/%s/related-projects' % org['id'])
    projects = response.json['related_projects']

    assert get_cols(projects, ['name', 'status']) == [
        ('Intersection Near My House', STATUS_INVESTIGATE)
    ]


def test_company_related_projects_top_level_suppliers_project_id(app, storage):
    org = factories.create_org(storage, 'Widgets Ltd')
    main_contractor_org = factories.create_org(storage, 'Main contractor')
    supplier_org = factories.create_org(storage, 'Supplier')

    mc_internal_project_id = 'My-Project'

    project = factories.create_default_project(storage, 'A project', org=org)
    factories.create_suppliers_tree(storage, project, [
        factories.supplier('Main contractor', supplier_role=MAIN_CONTRACTOR_ROLE,
                           internal_project_id=mc_internal_project_id),
        factories.supplier('Supplier')
    ])
    app.login(main_contractor_org)
    # can I see own company internal project id?
    response = app.get_with_org(f"/api/company/{main_contractor_org['id']}/related-projects")
    projects = response.json['related_projects']
    assert projects[0]['project_id'] == mc_internal_project_id

    response = app.get_with_org(f"/api/company/{supplier_org['id']}/related-projects")
    projects = response.json['related_projects']
    assert projects[0]['project_id'] == mc_internal_project_id


def test_company_related_projects_not_closed(app, storage):
    org = factories.create_org(storage, 'Widgets Ltd')
    user_contract = app.login(org, role=USER_ROLE_BASIC)

    factories.create_suppliers_tree(storage, 'Circle Road Construction', [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])
    factories.create_project_user_contract(storage, 'Circle Road Construction', user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)
    closed_project = factories.create_default_project(storage, 'Intersection Near My House',
                                                      state=CLOSED)
    factories.create_suppliers_tree(storage, closed_project, [
        factories.supplier("Widgets Ltd", supplier_role=SUPERVISOR_ROLE)
    ])
    factories.create_project_user_contract(storage, closed_project, user_contract, org,
                                           role=PROJECT_USER_ROLE_MEMBER)
    response = app.get_with_org('/api/company/%s/related-projects' % org['id'])
    projects = response.json['related_projects']
    assert [p['name'] for p in projects] == [
        'Circle Road Construction',
    ]
    assert projects[0].keys() == {
        'id', 'name', 'project_id', 'tax_id', 'start_date', 'end_date', 'status',
    }


@pytest.mark.parametrize("pa_enabled", [True, False])
def test_company_related_classic_projects_view_permissions(
    AppWithFeatureFlags, storage, pa_enabled
):
    app = AppWithFeatureFlags(
        {
            "pre_announcements": pa_enabled,
            "person_id_for_project_users": True,
        }
    )
    # Setup: project 1 owned by company A, company B and C are suppliers
    # project 2 owned by company B, company C is supplier
    # project 3 owned by company A, company B is supervisor, company C is supplier
    orgA = factories.create_org(storage, "Company A")
    orgB = factories.create_org(storage, "Company B")
    orgC = factories.create_org(storage, "Company C")
    orgD = factories.create_org(storage, "Company D")
    user_contract = app.login(orgB, role=USER_ROLE_BASIC)
    factories.create_suppliers_tree(
        storage,
        "Company A Project",
        [
            factories.supplier(
                "Company B",
                supplier_role=SUPPLIER_ROLE,
                subcontractors=[factories.supplier("Company D")],
            ),
            factories.supplier("Company C", supplier_role=SUPPLIER_ROLE),
        ],
        org="Company A",
    )
    factories.create_suppliers_tree(
        storage,
        "Company B Project",
        [
            factories.supplier("Company A", supplier_role=SUPPLIER_ROLE),
            factories.supplier("Company C", supplier_role=SUPPLIER_ROLE),
            factories.supplier("Company D", supplier_role=SUPPLIER_ROLE),
        ],
        org="Company B",
    )
    factories.create_suppliers_tree(
        storage,
        "Company A Project, B Supervisor",
        [
            factories.supplier("Company B", supplier_role=SUPERVISOR_ROLE),
            factories.supplier("Company C", supplier_role=SUPPLIER_ROLE),
        ],
        org="Company A",
    )
    factories.create_project_user_contract(
        storage, "Company A Project", user_contract, orgB, role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage, "Company B Project", user_contract, orgB, role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage,
        "Company A Project, B Supervisor",
        user_contract,
        orgB,
        role=PROJECT_USER_ROLE_MEMBER,
    )
    project_list = app.get_with_org("/api/project-list").json["projects"]

    related_projects_A = app.get_with_org(
        "/api/company/%s/related-projects" % orgA["id"], expect_errors=True
    ).json["related_projects"]
    related_projects_C = app.get_with_org(
        "/api/company/%s/related-projects" % orgC["id"]
    ).json["related_projects"]
    related_projects_D = app.get_with_org(
        "/api/company/%s/related-projects" % orgD["id"]
    ).json["related_projects"]
    # Assert that we do not get Company A Project for our own list - if this changes, it implies
    # a change in the business logic. We should then also update the related projects endpoint.
    assert {project["name"] for project in project_list} == {
        "Company A Project, B Supervisor",
        "Company B Project",
    }
    assert {project["name"] for project in related_projects_A} == {
        "Company B Project",
    }
    assert {project["name"] for project in related_projects_C} == {
        "Company A Project, B Supervisor",
        "Company B Project",
    }
    assert {project["name"] for project in related_projects_D} == {
        "Company B Project",
    }


def test_company_related_pa_project_view_permissions(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {
            "pre_announcements": True,
            "person_id_for_project_users": True,
        }
    )
    # Setup: project 1 owned by company A, company B and C are suppliers. Company D is a
    # subsupplier of Company B.
    # project 2 owned by company B, company C and D are suppliers
    # project 3 owned by company A, company B is supervisor, company C is supplier
    orgA = factories.create_org(storage, "Company A")
    orgB = factories.create_org(storage, "Company B")
    orgC = factories.create_org(storage, "Company C")
    orgD = factories.create_org(storage, "Company D")
    company_a_project = factories.create_default_project(
        storage, "Company A Project", pa_form_enabled=True, org=orgA
    )
    company_b_project = factories.create_default_project(
        storage, "Company B Project", pa_form_enabled=True, org=orgB
    )
    company_a_project_b_supervisor = factories.create_default_project(
        storage, "Company A Project, B Supervisor", pa_form_enabled=True, org=orgA
    )
    user_contract = app.login(orgB, role=USER_ROLE_BASIC)
    factories.create_suppliers_tree(
        storage,
        company_a_project,
        [
            factories.supplier(
                "Company B",
                supplier_role=SUPPLIER_ROLE,
                subcontractors=[factories.supplier("Company D")],
            ),
            factories.supplier("Company C", supplier_role=SUPPLIER_ROLE),
        ],
        org="Company A",
    )
    factories.create_suppliers_tree(
        storage,
        company_b_project,
        [
            factories.supplier("Company A"),
            factories.supplier("Company C"),
            factories.supplier("Company D"),
        ],
        org="Company B",
    )
    factories.create_suppliers_tree(
        storage,
        company_a_project_b_supervisor,
        [
            factories.supplier("Company B", supplier_role=SUPERVISOR_ROLE),
            factories.supplier("Company C", supplier_role=SUPPLIER_ROLE),
        ],
        org="Company A",
    )
    factories.create_project_user_contract(
        storage, "Company A Project", user_contract, orgB, role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage, "Company B Project", user_contract, orgB, role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage,
        "Company A Project, B Supervisor",
        user_contract,
        orgB,
        role=PROJECT_USER_ROLE_MEMBER,
    )

    project_list = app.get_with_org("/api/project-list").json["projects"]
    related_projects_for_A = app.get_with_org(
        "/api/company/%s/related-projects" % orgA["id"]
    ).json["related_projects"]
    related_projects_for_C = app.get_with_org(
        "/api/company/%s/related-projects" % orgC["id"]
    ).json["related_projects"]
    related_projects_for_D = app.get_with_org(
        "/api/company/%s/related-projects" % orgD["id"]
    ).json["related_projects"]
    assert {project["name"] for project in project_list} == {
        "Company A Project",
        "Company A Project, B Supervisor",
        "Company B Project",
    }
    assert {project["name"] for project in related_projects_for_A} == {
        "Company B Project",
    }
    assert {project["name"] for project in related_projects_for_C} == {
        "Company A Project, B Supervisor",
        "Company B Project",
    }
    assert {project["name"] for project in related_projects_for_D} == {
        "Company A Project",
        "Company B Project",
    }


def test_company_user_cannot_see_other_projects(app, storage):
    qvarn = storage.qvarn

    org = factories.create_org(storage, 'Darius Global company', registration_id='3145-9876-X')
    person1 = factories.create_person(qvarn, 'Anthony Bull', '<EMAIL>')
    person2 = factories.create_person(qvarn, 'Cippolino Zappa', '<EMAIL>')
    app.login(org, person=person1)
    factories.create_default_project(storage, 'Super company', org=org, project_leader=person1)

    app.login(org, person=person2, role=USER_ROLE_BASIC)
    resp = app.get_with_org('/api/company/%s/related-projects' % org['id'])
    related_projects = resp.json['related_projects']
    assert len(related_projects) == 0


@pytest.mark.parametrize('user_role, my_projects', [
    (USER_ROLE_MAIN, [
        'I am client here and have friend',
        'I am supervisor here and have friend',
    ]),
    (USER_ROLE_BASIC, [
        'I am supervisor here and have friend',
    ]),
])
def test_company_related_projects_seen(app, storage, user_role, my_projects):
    qvarn = storage.qvarn

    org = factories.create_org(storage, 'Widgets Ltd')
    org_target = factories.create_org(storage, 'Snippets Ltd')
    person = factories.create_person(qvarn, 'Silly person')

    user_contract = app.login(org, role=user_role)

    project1 = factories.create_default_project(
        storage, 'I am a supplier here and lonely'
    )
    project2 = factories.create_default_project(
        storage, 'I am client here and have friend', org=org
    )
    project3 = factories.create_default_project(
        storage, 'I am supervisor here and have friend'
    )
    project4 = factories.create_default_project(
        storage, 'I am supplier here and Im guest', org=org_target
    )
    project5 = factories.create_default_project(
        storage, 'I am supervisor here and Im guest', org=org_target
    )
    project6 = factories.create_default_project(
        storage, 'I am nothing here'
    )

    factories.create_suppliers_tree(storage, project1, [
        factories.supplier('Widgets Ltd', supplier_role=SUPPLIER_ROLE),
    ])

    factories.create_suppliers_tree(storage, project2, [
        factories.supplier('Snippets Ltd', supplier_role=SUPPLIER_ROLE),
    ])

    factories.create_suppliers_tree(storage, project3, [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
        factories.supplier('Snippets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])

    factories.create_project_user_contract(
        storage,
        project3,
        user_contract,
        org,
        role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage,
        project3,
        person,
        org_target,
        role=PROJECT_USER_ROLE_MEMBER
    )

    factories.create_suppliers_tree(storage, project4, [
        factories.supplier('Widgets Ltd', supplier_role=SUPPLIER_ROLE),
    ])

    factories.create_suppliers_tree(storage, project5, [
        factories.supplier('Widgets Ltd', supplier_role=SUPERVISOR_ROLE),
    ])

    factories.create_project_user_contract(
        storage,
        project5,
        user_contract,
        org,
        role=PROJECT_USER_ROLE_MEMBER
    )

    factories.create_suppliers_tree(storage, project6, [
        factories.supplier('Snippets Ltd', supplier_role=MAIN_CONTRACTOR_ROLE),
    ])

    response = app.get_with_org('/api/company/%s/related-projects' % org_target['id'])
    projects = response.json['related_projects']
    assert [p['name'] for p in projects] == my_projects


def test_company_related_pa_project_comment_permissions(AppWithFeatureFlags, storage):
    app = AppWithFeatureFlags(
        {
            "pre_announcements": True,
            "person_id_for_project_users": True,
            "project_supplier_comments": True,
        }
    )
    # Setup: project 1 owned by company A, company B is a supplier, company C is a
    # subsupplier of Company B.
    # project 2 owned by company B, company C is a supplier
    # project 3 owned by company A, company B is supervisor, company C is supplier
    orgA = factories.create_org(storage, "Company A")
    orgB = factories.create_org(storage, "Company B")
    orgC = factories.create_org(storage, "Company C")
    company_a_project = factories.create_default_project(
        storage, "Company A Project", pa_form_enabled=True, org=orgA
    )
    company_b_project = factories.create_default_project(
        storage, "Company B Project", pa_form_enabled=True, org=orgB
    )
    company_a_project_b_supervisor = factories.create_default_project(
        storage, "Company A Project, B Supervisor", pa_form_enabled=True, org=orgA
    )
    user_contract = app.login(orgB, role=USER_ROLE_BASIC)
    project_a_tree = factories.create_suppliers_tree(
        storage,
        company_a_project,
        [
            factories.supplier(
                "Company B",
                supplier_role=SUPPLIER_ROLE,
                subcontractors=[factories.supplier("Company C")],
            ),
        ],
        org="Company A",
    )
    factories.create_suppliers_tree(
        storage,
        company_b_project,
        [
            factories.supplier("Company C"),
        ],
        org="Company B",
    )
    supervisor_tree = factories.create_suppliers_tree(
        storage,
        company_a_project_b_supervisor,
        [
            factories.supplier("Company B", supplier_role=SUPERVISOR_ROLE),
            factories.supplier("Company C", supplier_role=SUPPLIER_ROLE),
        ],
        org="Company A",
    )
    factories.create_supplier_comment(
        storage,
        project_id=company_a_project["id"],
        supplier_id=project_a_tree[0].subcontractors[0].id,
        org_id=orgC["id"],
    )
    factories.create_supplier_comment(
        storage,
        project_id=company_a_project_b_supervisor["id"],
        supplier_id=supervisor_tree[1].id,
        org_id=orgC["id"],
    )
    factories.create_project_user_contract(
        storage, "Company A Project", user_contract, orgB, role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage, "Company B Project", user_contract, orgB, role=PROJECT_USER_ROLE_MEMBER
    )
    factories.create_project_user_contract(
        storage,
        "Company A Project, B Supervisor",
        user_contract,
        orgB,
        role=PROJECT_USER_ROLE_MEMBER,
    )
    related_projects_for_C = app.get_with_org(
        "/api/company/%s/related-projects" % orgC["id"]
    ).json["related_projects"]
    assert [
        {
            "name": project["name"],
            "can_view_comments": project["can_view_comments"],
            "has_comments": project["has_comments"],
        }
        for project in related_projects_for_C
    ] == [
        {
            "name": "Company A Project",
            "can_view_comments": False,
            "has_comments": False,
        },
        {
            "name": "Company A Project, B Supervisor",
            "can_view_comments": True,
            "has_comments": True,
        },
        {"name": "Company B Project", "can_view_comments": True, "has_comments": False},
    ]


def test_check_user_user_found(app, qvarn, storage):
    org = factories.create_org(storage, 'Darius Global company', registration_id='3145-9876-X')
    person = factories.create_person(qvarn, 'Anthony Bull', '<EMAIL>')
    contract = app.login(org, person, person_email='<EMAIL>')
    resp = app.post_json_with_org('/api/company/%s/check-user' % org['id'], {
        'email': '<EMAIL>'
    })
    user_info = resp.json['user_info']
    assert user_info == {
        'full_name': 'Anthony Bull',
        'email': '<EMAIL>',
        'contract_id': contract['id'],
        'is_active': True,
        'has_bol_permission': True,
        'person_id': person['id'],
    }


def test_check_user_user_found_mitt_id06(app_with_core,
                                         setup_and_teardown_organisation_person,
                                         core_mitt_id06,
                                         ):
    app = app_with_core
    org_person, org_record = setup_and_teardown_organisation_person(email='<EMAIL>',
                                                                    names=['Anthony Bull'],
                                                                    roles=['BOL_USER']
                                                                    )
    resp = app.post_json_with_org('/api/company/%s/check-user' % org_record['uuid'], {
        'email': '<EMAIL>'
    })
    user_info = resp.json['user_info']
    assert resp.json['ok'] is True
    assert user_info == {
        'full_name': 'Anthony Bull',
        'email': '<EMAIL>',
        'contract_id': None,
        'is_active': True,
        'has_bol_permission': True,
        'person_id': org_person.person.uuid,
    }


def test_check_user_user_not_found_mitt_id06(app_with_core,
                                             setup_and_teardown_organisation_person,
                                             core_mitt_id06,
                                             ):
    app = app_with_core
    org_person, org_record = setup_and_teardown_organisation_person(email='<EMAIL>',
                                                                    names=['Anthony Bull'],
                                                                    roles=['BOL_USER']
                                                                    )
    resp = app.post_json_with_org('/api/company/%s/check-user' % org_record['uuid'], {
        'email': '<EMAIL>'
    })
    user_info = resp.json['user_info']
    assert resp.json['ok'] is True
    assert user_info is None


def test_check_user_multiple_persons_same_email(app, qvarn, storage):
    org = factories.create_org(storage, 'Darius Global company', registration_id='3145-9876-X')
    person = factories.create_person(qvarn, 'Anthony Bull', '<EMAIL>')
    factories.create_person(qvarn, 'Not Bull', '<EMAIL>')
    user_account = app.login(org, person, person_email='<EMAIL>')
    resp = app.post_json_with_org('/api/company/%s/check-user' % org['id'], {
        'email': '<EMAIL>'
    })
    user_info = resp.json['user_info']
    assert user_info == {
        'full_name': 'Anthony Bull',
        'email': '<EMAIL>',
        'contract_id': user_account['id'],
        'is_active': True,
        'has_bol_permission': True,
        'person_id': person['id'],
    }


def test_check_user_user_not_found(app, qvarn, storage):
    org = factories.create_org(storage, 'Darius Global company', registration_id='3145-9876-X')
    org_target = factories.create_org(storage, 'Darius local comp', registration_id='3166-9876-Z')
    person = factories.create_person(qvarn, 'Anthony Bull', '<EMAIL>')
    app.login(org, person)
    resp = app.post_json_with_org('/api/company/%s/check-user' % org_target['id'], {
        'email': '<EMAIL>'
    })
    assert resp.json['ok'] is True
    assert resp.json['user_info'] is None


def test_check_user_user_found_disabled_user(app, qvarn, storage):
    org = factories.create_org(storage)
    person = factories.create_person(qvarn, '- -', '<EMAIL>')
    contract = app.login(org, person, person_email='<EMAIL>')
    resp = app.post_json_with_org('/api/company/%s/check-user' % org['id'], {
        'email': '<EMAIL>'
    })
    user_info = resp.json['user_info']
    assert user_info == {
        'full_name': '- -',
        'email': '<EMAIL>',
        'contract_id': contract['id'],
        'is_active': False,
        'has_bol_permission': True,
        'person_id': person['id'],
    }


def test_company_list_status_matches_details_status(app, storage):
    # BOL-1391. Actual result: the company with no projects is listed as incomplete.
    # Generating a report with different status will be shown only in it's details and report.
    # Expected result: both statuses in companies list and it's details should match.
    org = factories.create_org(storage, 'Test Org 19',
                               registration_id='1234-4321-Q')
    app.login(org, role=USER_ROLE_MAIN)
    factories.create_report(storage, org, status=STATUS_INVESTIGATE,
                            interested_org_id=org['id'])
    resp_details = app.get_with_org('/api/company/%s' % org['id'])
    resp_list = app.get_with_org('/api/company-list')
    company_list_status = resp_list.json['companies'][0]['company_status']
    company_details_status = resp_details.json['status']['overall_status']
    assert company_list_status == company_details_status


@pytest.mark.parametrize(
    'gov_id,found',
    [
        ('123456 7897', True),
        # url encoded space
        ('123456%207897', True),
        ('123 456 7897', True),
        ('123456-7897', True),
        ('1 2 3 4 5 6 7 8 9 7', True),
    ]
)
def test_find_company_not_normalized_gov_id(app, storage, gov_id, found):
    org = factories.create_org(storage, 'Test Org 19',
                               registration_id='123456-7897', country='SE')
    response = app.get_with_org('/api/find-company',
                                dict(query=gov_id, country='SE'))
    assert bool(response.json['results']) == found and [
        {'id': org['id'],
         'name': 'Test Org 19',
         'country': 'SWE',
         'gov_org_id': '123456-7897'},
    ]


@pytest.mark.parametrize(
    'gov_id,found',
    [
        ('123456 7897', True),
        # url encoded space
        ('123456%207897', True),
        ('123 456 7897', True),
        ('1 2 3 4 5 6 7 8 9 7', True),
    ]
)
def test_find_company_not_normalized_gov_id_PL(app, storage, gov_id, found):
    org = factories.create_org(storage, 'Test Org 19',
                               registration_id='1234567897', country='PL')
    response = app.get_with_org('/api/find-company',
                                dict(query=gov_id, country='PL'))
    assert bool(response.json['results']) == found and [
        {'id': org['id'],
         'name': 'Test Org 19',
         'country': 'SWE',
         'gov_org_id': '123456-7897'},
    ]


def test_company_details_with_ftax(app, storage):
    expected_gov_org_ids = [
        {
            'country': 'SWE',
            'gov_org_id': '852372-1298',
            'org_id_type': 'registration_number',
        },
        {
            'country': 'SWE',
            'gov_org_id': '1234567',
            'org_id_type': 'f-tax',
        },
    ]

    supplier_gov_org_ids = [make_reg_number('852372-1298'), make_f_tax('1234567')]
    factories.create_org(storage, 'Supplier', gov_org_ids_raw=supplier_gov_org_ids)
    factories.create_suppliers_tree(storage, 'My Project', [
        factories.supplier('Main Supplier', supplier_role=SUPERVISOR_ROLE, subcontractors=[
            factories.supplier('Supplier'),
        ]),
    ])

    user_contract = app.login('Main Supplier')
    factories.create_project_user_contract(storage, 'My Project', user_contract, 'Main Supplier')
    org = get_org(storage, name='Supplier')
    response = app.get_with_org('/api/company/%s' % org['id'])

    assert response.json == {
        'name': 'Supplier',
        'company_id': org['id'],
        'country': 'SWE',
        'gov_org_ids': expected_gov_org_ids,
        'status': None,
        'permissions': ['view_related_projects'],
        'company_details_url':
        f'https://companies-example.id06.se/#/{org["id"]}/organization/basicinfo'
    }


def test_search_project_not_confirmed(app, storage,
                                      ff_add_project_client_on,
                                      ff_block_project_client_on):
    org_mc = factories.create_org(storage, 'MC')
    org_client = factories.create_org(storage, 'Client')
    org_a = factories.create_org(storage, 'supplier A', registration_id='supp-A-id')
    # Login as Main Contractor
    app.login(org_mc, role=USER_ROLE_MAIN)
    # Create a project, add a project client
    project = factories.create_default_project(storage, 'added client project',
                                               org=org_mc,
                                               added_client_confirmed=False,
                                               added_client_can_view=True,
                                               created_by_org_id=org_mc['id'],
                                               project_creator_role=MAIN_CONTRACTOR_ROLE,
                                               client_company_id=org_client['id'])
    factories.create_suppliers_tree(storage, project, [
        factories.supplier('MC', supplier_role=MAIN_CONTRACTOR_ROLE),
        factories.supplier('Client'),
        factories.supplier('supplier A'),
    ], org=org_mc)
    org_a_gov_id = org_a['gov_org_ids'][0]['gov_org_id']
    resp = app.get_with_org('/api/company-list', {'search': org_a_gov_id})

    assert len(resp.json['companies']) == 1
    assert resp.json['companies'][0]['name'] == 'supplier A'

    # Login as Client
    app.login(org_client, role=USER_ROLE_BASIC)
    resp = app.get_with_org('/api/company-list', {'search': org_a_gov_id})
    assert len(resp.json['companies']) == 0


def test_find_company_same_as_main_contractor_project_client(app, storage):
    org = factories.create_org(storage, 'Kele Ltd.', registration_id='TC-1998')
    app.login(org, role=USER_ROLE_MAIN)
    response = app.get_with_org('/api/find-company',
                                dict(query='TC-1998', country='SWE', is_project_client='true'))
    assert len(response.json["results"]) == 0
    assert response.json['errors'] == {'error': ['Cannot add main contractor as project client']}
    assert response.json['ok'] is False
