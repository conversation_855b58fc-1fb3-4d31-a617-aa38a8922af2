import json
from urllib.parse import urljoin

import bolfak
from bolfak.config import (
    get_no_permission_redirect_url,
    get_bol_permission,
    get_service_portal_url,
)
from bolfak.featureflags import FEATURE_FLAGS, feature_active


def test_config_js(app, config):
    feature_flags = {}
    for flag in FEATURE_FLAGS.keys():
        feature_flags[flag] = feature_active(flag)

    service_portal_url = get_service_portal_url(config)
    if feature_active('use_stv_theme'):
        register_new_company_url = urljoin(service_portal_url, '/#/registration/findcompany')
    else:
        register_new_company_url = urljoin(service_portal_url, '/#/registration/createaccount')

    expected_config = {
        'ai_connection_string': None,
        'version': bolfak.__version__,
        'feature_flags': feature_flags,
        'bol_permission': get_bol_permission(config),
        'no_permission_url': get_no_permission_redirect_url(config),
        'default_storage': 'qvarn',
        'register_new_company_url': register_new_company_url,
        'page_size': config.getint('main', 'page_size', fallback=100),
        'change_user_details_url': config.get('main', 'change_user_details_url', fallback=''),
        'backend_url': config.get('main', 'backend_url', fallback=''),
        'standalone_url': config.get('main', 'standalone_url', fallback=''),
    }

    resp = app.get('/api/config.js')
    data = resp.body.decode('utf-8')

    variable, config_str = data.split('=', 1)
    assert variable.strip() == 'window.bolfak_config'

    # Remove semicolon and parse as JSON
    config_json = json.loads(config_str.strip()[:-1])

    assert config_json == expected_config
