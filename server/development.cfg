[main]
base_path = /api
static_path = /api/static/
static_map = ../build/client/static/SHA256SUMS
host = 0.0.0.0
port = 7002
debug = true
insecure_testdata_api = true
insecure_test_api = true
swagger_api = true
frontend_url = http://localhost:8082/
backend_url = http://localhost:7002/api/
auth_data_caching_duration_in_seconds = 5
service_portal_url = http://localhost:8081/
service_provider = id06

person_org_contract_type = user_account
org_contract_type = customership
# person_org_contract_type = tilaajavastuu_account
# org_contract_type = tilaajavastuu_subscription
sp_framework_api_url = http://localhost:8081/api/sp-framework-api
sp_framework_api_verify_tls = true
shared_cookie_domain = localhost
default_storage = qvarn
locale_dir = locale/id06
company_registry_url = https://company-api.alpha.id06.se/api/
companies_url = https://companies-alpha.id06.se
# if you want to test 'load more' more easily
# page_size = 20
change_user_details_url = https://spalpha.id06.se/#/profile/basic-details

[celery]
broker_url = amqp://localhost
task_default_queue = celery-que-bol-local
task_default_exchange = celery-exc-bol-local

[bol-data-api]
base_url = http://localhost:8070
client_id = @!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F
client_secret = supersecret
verify_requests = true
scope =
    uapi_reports_search_id_get,
    uapi_reports_post,

    uapi_ext_bol_project_list_get,
    uapi_ext_bol_project_suppliers_get,
    uapi_ext_bol_company_list_get,

    uapi_jobs_id_get,
    uapi_jobs_search_id_get,
    uapi_jobs_post,
    uapi_jobs_id_put,

    bda_project_users_get,
    bda_project_users_post,
    bda_project_users_put,
    bda_project_users_delete,
    bda_project_users_search,

    uapi_data_cache_get,
    uapi_data_cache_id_delete,
    uapi_data_cache_id_get,
    uapi_data_cache_id_put,
    uapi_data_cache_post,
    uapi_data_cache_search_id_get,

    bda_creditsafe_account_get,
    bda_creditsafe_account_post,
    bda_creditsafe_account_put,
    bda_creditsafe_account_delete,
    bda_creditsafe_account_search,

[company-qvarn-poc]
base_url = http://localhost:9070
client_id = @!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F
client_secret = supersecret
verify_requests = true
scope =
    uapi_orgs_multiple_post,

[core-system-api]
base_url = http://localhost:5001
client_id = @!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F
client_secret = supersecret
verify_requests = true
scope =
    view_extended:organisation_person,
    view:person,
    view:organisation,
    subscribe:organisationrecord,
    view:organisationrecord,
    view_report:organisationrecord,

[qvarn]
verify_requests = true
base_url = http://localhost:9080
url_for_orgs = http://localhost:9080
url_for_cards = http://localhost:9080
url_for_persons = http://localhost:9080
url_for_bol_suppliers = http://localhost:8070/api/v1/boldata
url_for_projects = http://localhost:8070/api/v1/boldata
url_for_reports = http://localhost:8070/api/v1/boldata
client_id = @!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F
client_secret = supersecret
scope =
    uapi_contracts_get,
    uapi_contracts_post,
    uapi_contracts_id_delete,
    uapi_contracts_id_document_get,
    uapi_contracts_id_document_put,
    uapi_contracts_id_get,
    uapi_contracts_id_put,
    uapi_contracts_search_id_get,

    uapi_orgs_get,
    uapi_orgs_post,
    uapi_orgs_id_get,
    uapi_orgs_id_put,
    uapi_orgs_id_delete,
    uapi_orgs_id_sync_get,
    uapi_orgs_search_id_get,

    uapi_persons_get,
    uapi_persons_id_delete,
    uapi_persons_id_get,
    uapi_persons_id_private_get,
    uapi_persons_id_private_put,
    uapi_persons_id_put,
    uapi_persons_post,
    uapi_persons_search_id_get,

    uapi_projects_get,
    uapi_projects_id_delete,
    uapi_projects_id_get,
    uapi_projects_id_put,
    uapi_projects_post,
    uapi_projects_search_id_get,

    uapi_reports_get,
    uapi_reports_post,
    uapi_reports_id_get,
    uapi_reports_id_put,
    uapi_reports_id_delete,
    uapi_reports_id_pdf_get,
    uapi_reports_id_pdf_put,
    uapi_reports_search_id_get,

    uapi_bol_suppliers_get,
    uapi_bol_suppliers_id_delete,
    uapi_bol_suppliers_id_get,
    uapi_bol_suppliers_id_put,
    uapi_bol_suppliers_post,
    uapi_bol_suppliers_search_id_get,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

    uapi_ext_bol_company_list_get
    uapi_ext_bol_project_list_get
    uapi_ext_bol_project_suppliers_get

    uapi_cards_search_id_get

threads = 10
extended_project_fields = true

[gluu]
# base_url = https://bolagsfakta-gluu-dev2.pov.lt
base_url = https://auth-azure-alpha.id06.se
end_session_support = true

[contract]
base_url = http://contract-api
verify_requests = true
scopes =
    contract_creditsafe_account_read,
    contract_creditsafe_account_create,
    contract_creditsafe_account_delete,
    contract_creditsafe_account_update,

[user-account-api]
base_url = http://user-account-api
client_id = @!F245.8207.02D2.AA4E!0001!EF88.84B4!0008!2BA0.454F
client_secret = supersecret
verify_requests = true
threads = 1
scope =
    user_account_read
    user_account_create
    user_account_delete
    user_account_update

[sessions]
type = file
dsn = redispass@localhost:6379/0
cookie_name = session_id
cookie_domain =
cookie_path = /api/
timeout = 14400
data_dir = var/sessions
httponly = true
secure = false
encrypt_key = 'very'
validate_key = 'secret'


# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[formatters]
keys = jsonl, console, accesslog, file

[formatter_jsonl]
class = bolfak.logging.JsonFormatter
format =
    [
        "asctime", "process", "levelname", {
            "message": "message",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName",
            "logger": "name",
            "request": "requestId",
            "user": "user"
        }
    ]

[formatter_console]
class = bolfak.logging.ColorFormatter
format = {blue}%(asctime)s{reset} {green}[%(levelname)s:%(name)s]{reset} %(message)s
datefmt = %H:%M:%S.%f

[formatter_accesslog]
class = bolfak.logging.ColorFormatter
format = {cyan}%(address)s{reset} - - [{blue}%(asctime)s{reset}] {brown}"%(request)s"{reset} %(status_color)s%(status)s{reset} {cyan}%(size)sb{reset} {magenta}%(duration_ms)dms{reset}
datefmt = %d/%b/%Y %H:%M:%S

[formatter_file]
class = bolfak.logging.Formatter
format = %(asctime)s %(programName)s[%(process)d] [%(levelname)s] %(message)s


[handlers]
keys = console, accesslog, debug, file

[handler_jsonl]
class = logging.handlers.RotatingFileHandler
args = ('var/app.log.jsonl', 'a', 1000000, 5)
formatter = jsonl
level = DEBUG

[handler_file]
class = logging.handlers.RotatingFileHandler
args = ('var/app.log', 'a', 1000000, 5)
formatter = file
level = INFO

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = INFO

[handler_accesslog]
class = StreamHandler
args = (sys.stdout,)
formatter = accesslog
level = INFO

[handler_debug]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = DEBUG


[loggers]
keys = root, accesslog, roles, bulkimports, stv_client, csrf, i18n, qvarnclient, urllib3

[logger_root]
level = DEBUG
handlers = console, file

[logger_accesslog]
qualname = access_log
handlers = accesslog
propagate = 0

[logger_roles]
# set to DEBUG if you want to see detailed information about permission decisions
level = INFO
qualname = bolfak.services.roles
handlers = debug

[logger_bulkimports]
level = DEBUG
qualname = bolfak.services.bulkimport
handlers = debug

[logger_stv_client]
# "INFO: Calling Sp Framework Utils Client endpoint: /collect-ui-info" is annoying and content-less
qualname = stv.data.clients.base.client
level = WARNING
handlers =

[logger_csrf]
# "INFO: Sending CSRF token" good for you I don't care
qualname = bolfak.plugins.csrf_plugin
level = WARNING
handlers =

[logger_i18n]
# "INFO: Detected locale: en" well done now go away
qualname = bolfak.i18n
level = WARNING
handlers =

[logger_qvarnclient]
# "INFO: '%s' resource created with id '%s' is noisy
qualname = qvarnclient.qvarnapi
level = WARNING
handlers =

[logger_urllib3]
# hide DEBUG so we don't have two messages in App Insights for every HTTP POST
# submitting information to App Insights
qualname = urllib3
level = INFO
handlers =


# Company report providers

[statusreports]
report_version_id06 =
bulkimport_data_provider = bolfak.statusreports.sandbox.SandboxInfoProvider
report_swedish_data_provider = bolfak.statusreports.noop.NoopProvider
report_foreign_data_provider = bolfak.statusreports.noop.NoopProvider
tax_data_provider = bolfak.statusreports.noop.NoopProvider


[statusreports.sandbox]
sleep = 2


[statusreports.bisnode]
user_id =
user_password =
customer_code = BJA8
customer_code_owner = 022551
language = SE
from_country = SE
to_country = SE


[statusreports.creditsafe]
use_test_server = true
username =
password =
lod_cust_free_text = The report is taken on behalf of ID06
symmetric_secret_key = secret_terces

[statusreports.creditsafe_ggs]
# You need to extract and set these to value from Alpha/Beta/Prod creds
# (base64-decoded) as we have only 1 CS GGS account!
creditsafe_ggs_wsdl_username =
creditsafe_ggs_wsdl_password =

[autoaccount]
autoaccount_url = https://webservice.creditsafe.se/AutoAccount/AutoAccountService.asmx?WSDL
autoaccount_username =
autoaccount_password =
autoaccount_email = <EMAIL>
autoaccount_request_package = ID06_TEST_AA
autoaccount_use_testing_org_gov_org_id = True
autoaccount_testing_org_gov_org_id = **********
autoaccount_id06_gov_org_id = **********

[feature-flags]
extended_report = True
celery_for_sendgrid = True
lazy_qvarn_startup = True
import_sole_traders = True
company_registry = False
pagination = True
bda_client = True
bda_company_list = True
bda_project_suppliers = True
visitors = True
project_report = True
pre_announcements = True
pa_form_checkbox_disabled = False
non_paed_suppliers = True
contract_api_creditsafe_contract = False
user_account_api = False
on_azure = False
add_project_client = True
block_project_client = True
skip_pa_reg_step = True
create_and_activate_cs_accounts = True
person_id_for_project_users = True
core_mitt_id06 = False
project_supplier_comments = True
dependency_request_cache = False

# Email sending via SendGrid

[sendgrid]
sendgrid_sender = ID06 alpha <<EMAIL>>
sendgrid_api_key = *********************************************************************
