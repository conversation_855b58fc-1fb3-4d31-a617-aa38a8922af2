[main]
base_path = /api
static_path = /api/static/
static_map = ../build/client/static/SHA256SUMS
host = 0.0.0.0
port = 7002
debug = true
insecure_testdata_api = true
insecure_test_api = true
swagger_api = true
frontend_url = http://localhost:8082/
backend_url = http://localhost:7002/api/
auth_data_caching_duration_in_seconds = 5
service_portal_url = https://spalpha.id06.se/
service_provider = id06
bol_permission = bolagsdeklaration_user
content_config_name = id06-fs3.yaml
person_org_contract_type = user_account
org_contract_type = customership
sp_framework_api_url = https://spalpha.id06.se/api/sp-framework-api
sp_framework_api_verify_tls = true
shared_cookie_domain = .id06.se
locale_dir = locale/id06
default_storage = qvarn
company_registry_url = https://company-api.alpha.id06.se/api/
companies_url = https://companies-alpha.id06.se/
change_user_details_url = https://spalpha.id06.se/#/profile/basic-details

[bol-data-api]
base_url = https://bol-data-api.alpha.vaultit.org
client_id = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E
client_secret = eejiw1ooreet3ohK
verify_requests = true
scope =
    uapi_reports_search_id_get,

    uapi_ext_bol_project_list_get,
    uapi_ext_bol_project_suppliers_get,
    uapi_ext_bol_company_list_get,

    bda_project_users_get,
    bda_project_users_post,
    bda_project_users_put,
    bda_project_users_delete,
    bda_project_users_search,

    uapi_data_cache_get,
    uapi_data_cache_id_delete,
    uapi_data_cache_id_get,
    uapi_data_cache_id_put,
    uapi_data_cache_post,
    uapi_data_cache_search_id_get,

    bda_creditsafe_account_get,
    bda_creditsafe_account_post,
    bda_creditsafe_account_put,
    bda_creditsafe_account_delete,
    bda_creditsafe_account_search,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

[company-qvarn-poc]
base_url = https://company-identical-api.alpha.id06.se/api
client_id = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E
client_secret = eejiw1ooreet3ohK
verify_requests = true
threads = 1
scope =
    uapi_orgs_multiple_post,

[stamp]
base_url = https://stampdata-api.alpha.vaultit.org
verify_requests = true
scopes =
    stampdata_companies_whitelist_get,
    stampdata_events_post,
    stampdata_event_batches_id_status_get,
    stampdata_visitor_companies_post,

[celery]
broker_url = amqp://localhost
task_default_queue = celery-que-bol-local
task_default_exchange = celery-exc-bol-local

[qvarn]
verify_requests = true
base_url = https://auth.alpha.vaultit.org
client_id = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E
client_secret = eejiw1ooreet3ohK
scope =
    uapi_contracts_get,
    uapi_contracts_post,
    uapi_contracts_id_delete,
    uapi_contracts_id_document_get,
    uapi_contracts_id_document_put,
    uapi_contracts_id_get,
    uapi_contracts_id_put,
    uapi_contracts_search_id_get,

    uapi_orgs_get,
    uapi_orgs_post,
    uapi_orgs_id_get,
    uapi_orgs_id_put,
    uapi_orgs_id_delete,
    uapi_orgs_id_sync_get,
    uapi_orgs_search_id_get,

    uapi_persons_get,
    uapi_persons_id_delete,
    uapi_persons_id_get,
    uapi_persons_id_private_get,
    uapi_persons_id_private_put,
    uapi_persons_id_put,
    uapi_persons_post,
    uapi_persons_search_id_get,

    uapi_projects_get,
    uapi_projects_id_delete,
    uapi_projects_id_get,
    uapi_projects_id_put,
    uapi_projects_post,
    uapi_projects_search_id_get,

    uapi_reports_get,
    uapi_reports_post,
    uapi_reports_id_get,
    uapi_reports_id_put,
    uapi_reports_id_delete,
    uapi_reports_id_pdf_get,
    uapi_reports_id_pdf_put,
    uapi_reports_search_id_get,

    uapi_bol_suppliers_get,
    uapi_bol_suppliers_id_delete,
    uapi_bol_suppliers_id_get,
    uapi_bol_suppliers_id_put,
    uapi_bol_suppliers_post,
    uapi_bol_suppliers_search_id_get,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

    uapi_ext_bol_company_list_get
    uapi_ext_bol_project_list_get
    uapi_ext_bol_project_suppliers_get

    uapi_cards_search_id_get

threads = 10
extended_project_fields = true

url_for_orgs = https://company-identical-api.alpha.id06.se/api
url_for_persons = https://qii-api.alpha.id06.se
url_for_cards = https://card-be-api.alpha.id06.se
url_for_bol_suppliers = https://bol-data-api.alpha.vaultit.org/api/v1/boldata
url_for_projects = https://bol-data-api.alpha.vaultit.org/api/v1/boldata
url_for_reports = https://bol-data-api.alpha.vaultit.org/api/v1/boldata


[gluu]
base_url = https://auth-azure-alpha.id06.se
end_session_support = true

[sessions]
type = file
cookie_name = session_id
cookie_domain =
cookie_path = /api/
timeout = 14400
data_dir = var/sessions
httponly = true
secure = false
encrypt_key = 'very'
validate_key = 'secret'

[contract]
base_url = https://contract-api.alpha.id06.se
verify_requests = true
scopes =
    contract_creditsafe_account_read,
    contract_creditsafe_account_create,
    contract_creditsafe_account_delete,
    contract_creditsafe_account_update,

[user-account-api]
base_url = https://user-account-api.alpha.id06.se
client_id = @!D0B3.42FF.3A77.681D!0001!0105.03F6!0008!6214.003E
client_secret = eejiw1ooreet3ohK
verify_requests = true
threads = 1
scope =
    user_account_read
    user_account_create
    user_account_delete
    user_account_update


# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[formatters]
keys = jsonl, console, file

[formatter_jsonl]
class = bolfak.logging.JsonFormatter
format =
    [
        "asctime", "process", "levelname", {
            "message": "message",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName",
            "logger": "name",
            "request": "requestId",
            "user": "user"
        }
    ]

[formatter_console]
class = bolfak.logging.Formatter
format = %(asctime)s [%(levelname)s] %(message)s

[formatter_file]
class = bolfak.logging.Formatter
format = %(asctime)s %(programName)s[%(process)d] [%(levelname)s] %(message)s


[handlers]
keys = console, debug, file, jsonl

[handler_jsonl]
class = logging.handlers.RotatingFileHandler
args = ('var/app.log.jsonl', 'a', 1000000, 5)
formatter = jsonl
level = DEBUG

[handler_file]
class = logging.handlers.RotatingFileHandler
args = ('var/app.log', 'a', 1000000, 5)
formatter = file
level = INFO

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = INFO

[handler_debug]
class = StreamHandler
args = (sys.stdout,)
formatter = console
level = DEBUG


[loggers]
keys = root, roles, bulkimports

[logger_root]
level = DEBUG
handlers = console, file, jsonl

[logger_roles]
level = DEBUG
qualname = bolfak.services.roles
handlers = debug

[logger_bulkimports]
level = DEBUG
qualname = bolfak.services.bulkimport
handlers = debug


# Company report providers

[statusreports]
bulkimport_data_provider = bolfak.statusreports.sandbox.SandboxInfoProvider
#report_data_provider = bolfak.statusreports.noop.NoopProvider
report_swedish_data_provider = bolfak.statusreports.noop.NoopProvider
report_foreign_data_provider = bolfak.statusreports.connect.ConnectReportProvider
tax_data_provider = bolfak.statusreports.noop.NoopProvider


[statusreports.sandbox]
sleep = 2


[statusreports.bisnode]
user_id =
user_password =
customer_code = BJA8
customer_code_owner = 022551
language = SE
from_country = SE
to_country = SE


[statusreports.creditsafe]
use_test_server = true
username =
password =
lod_cust_free_text = The report is taken on behalf of ID06
symmetric_secret_key = secret_terces


[statusreports.creditsafe_ggs]
creditsafe_ggs_wsdl_username =
creditsafe_ggs_wsdl_password =


[autoaccount]
autoaccount_url = https://webservice.creditsafe.se/AutoAccount/AutoAccountService.asmx?WSDL
autoaccount_username = # Get from Alpha secrets
autoaccount_password = # Get from Alpha secrets
autoaccount_email = <EMAIL>
autoaccount_request_package = ID06_TEST_AA
autoaccount_use_testing_org_gov_org_id = True
autoaccount_testing_org_gov_org_id = **********
autoaccount_id06_gov_org_id = **********

[feature-flags]
extended_report = True
lazy_qvarn_startup = True
import_sole_traders = True
company_registry = True
pagination = True
bda_client = True
bda_project_suppliers = True
bda_company_list = True
visitors = True
project_report = True
pre_announcements = True
pa_form_checkbox_disabled = True
non_paed_suppliers = True
contract_api_creditsafe_contract = True
user_account_api = True
on_azure = False
add_project_client = True
skip_pa_reg_step = True
create_and_activate_cs_accounts = True
block_project_client = True
person_id_for_project_users = True
core_mitt_id06 = False
project_supplier_comments = True
dependency_request_cache = False

# Email sending via SendGrid

[sendgrid]
sendgrid_sender = ID06 alpha <<EMAIL>>
sendgrid_api_key = *********************************************************************
