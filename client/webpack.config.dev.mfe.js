const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const webpackConfig = require('./webpack.config.base');

const FRONTBACK_URL = process.env.FRONTBACK_URL || 'http://localhost:7002';

const moduleFederationConfig = require('./webpack.moduleFederationConfigbase')(process.env);
moduleFederationConfig.remotes.host = 'host@http://localhost:8080/remoteEntry.js';

webpackConfig.mode = 'development';
webpackConfig.performance = {
  hints: false,
};

webpackConfig.output = {
  publicPath: 'http://localhost:8082/mfe',
  clean: true,
};

webpackConfig.plugins.push(
  new ModuleFederationPlugin(moduleFederationConfig)
);

webpackConfig.devServer = {
  allowedHosts: 'all',
  host: '0.0.0.0',
  port: 8082,
  hot: true,
  compress: true,

  historyApiFallback: {
    index: '/index.id06.html',
  },

  client: {
    progress: false,
    reconnect: true,
  },
  proxy: [
    {
      // Stand-alone proxy rule needed for loading api/config.js
      context: ['/api'],
      target: FRONTBACK_URL,
      secure: false,
      changeOrigin: true,
    }
  ],
  /* eslint-disable max-len */
  headers: {
    'X-XSS-Protection': '1; mode=block',
    'X-Content-Type-Options': 'nosniff',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Security-Policy':
      "default-src 'none'; " +
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' " +
      'https://js-agent.newrelic.com https://bam.nr-data.net ' +
      'https://resource.finnchat.com ' +
      'https://js.hs-scripts.com https://js.hsadspixel.net ' +
      'http://js.hs-analytics.net https://js.hsleadflows.net ' +
      'https://www.googletagmanager.com https://www.googleadservices.com ' +
      'https://googleads.g.doubleclick.net ' +
      'https://www.google-analytics.com ' +
      'https://www.googleadservices.com/ ' +
      'https://www.google.com/ ' +
      'https://static.hotjar.com/ ' +
      'https://vars.hotjar.com/ ' +
      'https://script.hotjar.com/ ' +
      'https://www.youtube.com/ ' +
      'https://connect.facebook.net/ ' +
      'https://s.ytimg.com/ ' +
      'https://app.interactiveads.ai/ ' +
      FRONTBACK_URL +
      '; ' +
      "connect-src 'self' ws://localhost:8080 " +
      'https://www.google-analytics.com/ ' +
      'https://*.in.applicationinsights.azure.com/ ' +
      'https://vc.hotjar.io/ https://in.hotjar.com/ ' +
      FRONTBACK_URL +
      '; ' +
      "img-src 'self' 'unsafe-inline' data: https:; " +
      "style-src 'self' 'unsafe-inline' fonts.googleapis.com; " +
      "font-src 'self' fonts.gstatic.com; " +
      "object-src 'self'; " +
      'frame-src https://zeckit.com https://dev.zeckit.com https://app.interactiveads.ai/ ' +
      'https://www.googletagmanager.com/ ' +
      'https://vars.hotjar.com/ ; ',
  },
};

module.exports = webpackConfig;
