{"name": "bolagsfakta", "version": "1.0.0", "description": "", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "webpack --env TARGET=alpha --config webpack.config.prod.js --progress", "build-alpha-mfe": "webpack --env TARGET=alpha --config webpack.config.alpha-mfe.js --progress", "start": "webpack serve --config webpack.config.dev.js --progress --open", "snyk-protect": "snyk protect", "prepare": "npm run snyk-protect", "test": "TZ=UTC jest", "lint": "eslint --config .eslintrc.js ."}, "repository": {"type": "git", "url": "*********************:v3/vaultit/BOL/BOL"}, "author": "ID06 AB", "license": "UNLICENSED", "private": true, "dependencies": {"@material-design-icons/font": "^0.14.13", "@microsoft/applicationinsights-react-js": "^17.3.2", "@microsoft/applicationinsights-web": "^3.3.2", "axios": "^1.6.7", "bootstrap": "4.0.0-alpha.6", "buffer-shims": "1.0.0", "c3": "0.6.14", "chai": "4.2.0", "chai-exclude": "1.0.12", "classnames": "2.2.6", "core-js": "2.6.9", "deepcopy": "0.6.3", "es-ctags": "0.0.2", "flux": "2.1.1", "font-awesome": "4.7.0", "intl": "1.2.5", "iso-3166-1": "1.1.0", "jquery": "3.4.1", "keymirror": "0.1.1", "moment": "2.24.0", "prop-types": "15.7.2", "query-string": "5.1.1", "react": "^18.3.1", "react-bootstrap-table-next": "3.1.7", "react-bootstrap-typeahead": "3.4.6", "react-collapsible": "2.10.0", "react-cookie": "0.4.9", "react-datetime": "3.2.0", "react-dom": "^18.3.1", "react-ga": "2.7.0", "react-gtm-module": "2.0.8", "react-highlighter": "0.4.3", "react-infinite-scroller": "1.2.4", "react-intl": "6.6.8", "react-markdown": "^9.0.1", "react-popper": "1.3.3", "react-router-dom": "^6.26.1", "react-stickynode": "4.1.1", "react-tooltip": "3.10.0", "react-transition-group": "1.2.1", "reactstrap": "5.0.0", "resolve": "1.11.0", "s": "0.1.1", "scroll-into-view": "1.9.7", "snyk": "1.1044.0", "styled-components": "^6.1.19", "stylis-plugin-class-namespace": "^1.0.4", "transliteration": "2.1.3"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/plugin-transform-class-properties": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/preset-env": "7.25.3", "@babel/preset-react": "7.24.7", "@cfaester/enzyme-adapter-react-18": "^0.8.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "autoprefixer": "6.7.7", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "bootstrap-loader": "2.1.0", "cheerio": "^1.0.0-rc.3", "copy-webpack-plugin": "^6.3.1", "css-loader": "^7.1.2", "enzyme": "^3.11.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.2", "eslint-webpack-plugin": "^4.2.0", "file-loader": "6.2.0", "html-loader": "0.4.5", "html-webpack-plugin": "^5.6.0", "imports-loader": "0.6.5", "jest": "29.7.0", "jest-cli": "29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "10.0.0", "jsdom-global": "3.0.2", "json-loader": "0.5.7", "less": "2.7.3", "mini-css-extract-plugin": "^2.9.1", "mocha": "2.5.3", "nan": "2.20.0", "postcss": "^8.5.1", "postcss-loader": "^8.1.1", "postcss-prefix-selector": "^2.1.0", "prettier": "^3.3.3", "react-hot-loader": "1.3.1", "regenerator-runtime": "0.14.1", "resolve-url-loader": "5.0.0", "sass-embedded": "^1.80.6", "sass-loader": "10.5.2", "sinon": "17.0.1", "style-loader": "^4.0.0", "url-loader": "4.1.1", "webpack": "5.94.0", "webpack-cli": "5.1.4", "webpack-dev-server": "5.1.0", "webpack-merge": "^6.0.1"}, "jshintConfig": {"esnext": true}, "snyk": true, "resolutions": {"styled-components": "^6"}}