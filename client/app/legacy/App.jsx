/* eslint-disable import/first */
import { featureActive } from '../helpers/FeatureFlags';
import '../../styles/styles.id06.scss';
import 'font-awesome/css/font-awesome.min.css';
import '@material-design-icons/font';


if (featureActive('finnchat')) {
  const script = document.createElement('script');
  script.src = 'https://resource.finnchat.com/tilaajavastuu.js';

  document.head.appendChild(script);
}

import React from 'react';

import 'jquery';

import '../helpers/Polyfill';

import '../stores/Stores';
import BolagsfaktaRoutes from './Routes';

import { AppInsightsErrorBoundary } from '@microsoft/applicationinsights-react-js';
import { reactPlugin } from '../AppInsights';
/* eslint-enable */

const App = () => (
  <AppInsightsErrorBoundary
    onError={() => <h1>Something went wrong!</h1>}
    appInsights={reactPlugin}
  >
    <BolagsfaktaRoutes />
  </AppInsightsErrorBoundary>
);

export default App;
