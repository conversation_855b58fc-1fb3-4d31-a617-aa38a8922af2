import React from 'react';
import { injectIntl, defineMessages } from 'react-intl';
import FormattedMessage from './i18n/FormattedMessage'

export const id06FAQUrl = 'https://id06.kb.kundo.se/category/id06-bolagsdeklaration/';
export const id06TosUrl = 'https://id06.se/gdpr-avtal/';
export const id06UserGuidePdf =
  'https://id06.se/id06-bolagsdeklaration-anvandarguide/';

const messages = defineMessages({
  faq: {
    id: 'customerSupport.faq',
    description: 'FAQ',
    defaultMessage: 'FAQ',
  },
  userGuidePdf: {
    id: 'customerSupport.userGuidePdf',
    description: 'ID06 Bolagsdeklaration Användarguide.pdf',
    defaultMessage: 'ID06 Bolagsdeklaration Användarguide.pdf',
  },
  TnC: {
    id: 'customerSupport.TnC',
    description: 'GDPR and agreements',
    defaultMessage: 'GDPR and agreements',
  },
});

const HelpPageComponent = injectIntl(props => (
  <div id="help_page" className="container page">
    <h3 className="text-primary pt-5 pointer-events-none">
      <FormattedMessage
        id="customerSupport.customerSupportTitle"
        description="Customer support title"
        defaultMessage="CUSTOMER SUPPORT"
      />
    </h3>

    <div className="card noborder">
      <h4 className="text-primary pointer-events-none">
        <FormattedMessage
          id="customerSupport.userGuidesTitle"
          description="User guides title"
          defaultMessage="USER GUIDES"
        />
      </h4>
      <div className="row container">
        <p className="col-sm-4">
          <a id="customerSupport.userGuidePdf" href={id06UserGuidePdf} target="_blank"
            rel="noopener noreferrer">
            <i
              id="customerSupport.userGuidePdf_icon"
              className="fa fa-file-pdf-o fa-pr--bol"
              aria-hidden="true"
            />
            <span>{props.intl.formatMessage(messages.userGuidePdf)}</span>
          </a>
        </p>
        <p className="help-para col-sm-8 pointer-events-none">
          <FormattedMessage
            id="customerSupport.userGuidePdfDesc"
            description="User guide description"
            defaultMessage="Descriptions and instructions for how to use ID06 Bolagsdeklaration 
              (only available in Swedish)."
          />
        </p>
      </div>
      <div className="row container">
        <p className="col-sm-4">
          <a id="customerSupport.faq" href={id06FAQUrl} target="_blank" rel="noopener noreferrer">
            <span>{props.intl.formatMessage(messages.faq)}</span>
          </a>
        </p>
        <p className="help-para col-sm-8 pointer-events-none">
          <FormattedMessage
            id="customerSupport.faqDesc"
            description="FAQ description"
            defaultMessage="Frequently asked questions about ID06 Bolagsdeklaration."
          />
        </p>
      </div>
    </div>
    <div className="card noborder">
      <h4 className="text-primary pointer-events-none">
        <FormattedMessage
          id="customerSupport.legalTitle"
          description="Legal section title"
          defaultMessage="TERMS AND CONDITIONS"
        />
      </h4>
      <div className="row container">
        <p className="col-sm-4">
          <a id="customerSupport.TnC" href={id06TosUrl} target="_blank"
            rel="noopener noreferrer">
            <span>{props.intl.formatMessage(messages.TnC)}</span>
          </a>
        </p>
        <p className="help-para col-sm-8 pointer-events-none">
          <FormattedMessage
            id="customerSupport.TnCDesc"
            description="Terms and conditions description"
            // eslint-disable-next-line max-len
            defaultMessage="ID06 General provisions and specific terms and conditions for ID06 Bolagsdeklaration."
          />
        </p>
      </div>
    </div>
  </div>
));

export default HelpPageComponent;
