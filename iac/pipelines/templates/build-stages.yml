stages:
- stage: build
  variables:
    workDir: $(System.DefaultWorkingDirectory)
  jobs:
  - job: test_safety
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'pip | "$(Agent.OS)" | safety'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(pip_cache_dir)
    - task: Bash@3
      target: pyContainer
      displayName: Test safety
      inputs:
        targetType: inline
        workingDirectory: $(workDir)
        script: |
          pip3 config set global.disable-pip-version-check True
          export PATH="$HOME/.local/bin:$PATH"
          # Note: due to a bug in safety 3.6.0, we need to specify typer<0.17 manually
          pip3 install safety 'typer<0.17'
          # 704789,70477,70478,70480: beaker-project (https://github.com/b) vulnerabilities misattributed to beaker (https://pypi.org/project/Beaker/)
          # 70612: jinja2 server-side template injection, not a real vuln, we don't use user-provided strings as templates
          safety check -r server/requirements-dev.txt --full-report --ignore=70479,70477,70478,70480,70612
          safety check -r server/requirements.txt --full-report --ignore=70479,70477,70478,70480,70612

  - job: test_crontabs
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: Bash@3
      target: pyContainer
      displayName: Test crontabs
      inputs:
        targetType: inline
        workingDirectory: $(workDir)
        failOnStderr: true
        script: |
          pip3 config set global.disable-pip-version-check True
          export PATH=/home/<USER>/.local/bin:$PATH
          pip3 install chkcrontab
          rc=0
          for crontab in docker/crontab.*; do
              cp $crontab ${crontab/./_}
              chkcrontab ${crontab/./_} || rc=1
          done
          exit $rc

  - job: test_bandit
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'pip | "$(Agent.OS)" | bandit'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(pip_cache_dir)
    - task: Bash@3
      target: pyContainer
      displayName: Test bandit
      inputs:
        targetType: inline
        workingDirectory: $(workDir)
        script: |
          pip3 config set global.disable-pip-version-check True
          export PATH=/home/<USER>/.local/bin:$PATH
          pip3 install bandit
          cd server && bandit --ini bandit.ini -r bolfak/ -b bandit_baseline.json

  - job: test_mypy
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'pip | "$(Agent.OS)" | safety'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(pip_cache_dir)
    - task: Bash@3
      target: pyContainer
      displayName: Test with mypy
      inputs:
        targetType: inline
        workingDirectory: $(workDir)
        script: |
          pip3 config set global.disable-pip-version-check True
          export PATH=/home/<USER>/.local/bin:$PATH
          pip3 install -rserver/requirements-mypy.txt
          cd server
          mypy --pretty --check-untyped-defs bolfak

  - job: build_client
    timeoutInMinutes: 15
    cancelTimeoutInMinutes: 2
    steps:
    - task: npmAuthenticate@0
      inputs:
        workingFile: $(workDir)/client/.npmrc
    - task: CmdLine@2
      displayName: Build client
      target: nodeContainer
      inputs:
        workingDirectory: $(workDir)
        script: |
          make -C client build
    - task: CmdLine@2
      displayName: Build client version
      target: pyContainer
      inputs:
        workingDirectory: $(workDir)
        script: |
          py_version=$(cd server && python3 setup.py --version)
          echo '{"version": "$(py_version)"}' > build/client/version.json
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(workDir)/build
        artifactName: buildClient

  - job: build_server
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'v2 | server-pip-cache'  # let all branches share a single cache
        path: $(pip_cache_dir)
    - task: CmdLine@2
      displayName: Build Server
      target: bolBuild
      inputs:
        workingDirectory: $(workDir)
        script: |
          make -C server flake8 &&
          make -C server build-assets SASSC=sassc &&
          make -C server build-wheels USE_MSGFMT=1
    - task: PublishBuildArtifacts@1
      inputs:
        pathToPublish: $(workDir)/build
        artifactName: buildServer

# Run build push docker image in parallel with unit tests
# on master branch to save time for deployments
- stage: build_push_docker_master
  variables:
    workDir: $(System.DefaultWorkingDirectory)
  displayName: Master Docker build and push
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/master'), startsWith(variables['Build.SourceBranch'], 'refs/heads/release-')))
  dependsOn: build
  jobs:
  - template: build-push-amd64-job.yml


- stage: unit_test
  dependsOn: build
  variables:
    workDir: $(System.DefaultWorkingDirectory)
  jobs:
  - job: unittest_server
    strategy:
      parallel: 3
    displayName: unittest_server
    timeoutInMinutes: 60 # Temporary increase from 40 to be able to run the necessary tests with person_id_for_project_users enabled and disabled
    cancelTimeoutInMinutes: 2
    container: bolBuild
    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: bol
          POSTGRES_USER: dbuser
          POSTGRES_PASSWORD: dbpwd
        options: "--health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5"
      core-sysapi-test: coreSysAPITest
    variables:
      DATABASE_URL: **************************************
      BOL_TEST_SQLA_URL: postgresql://postgres/bol
      BOL_TEST_SQLA_USERNAME: dbuser
      BOL_TEST_SQLA_PASSWORD: dbpwd
      BOL_TEST_BDA_DSN: **************************************
      BOL_TEST_QVARN_API: bda+****************************************
      BOL_TEST_CORE_API_URL: http://core-sysapi-test:5001
    steps:
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'v2 | server-pip-cache'  # let all branches share a single cache
        path: $(pip_cache_dir)
    - script: |
        echo "Waiting for PostgreSQL to start..."
        for i in {1..30}; do
          if pg_isready -h postgres -p 5432; then
            echo "PostgreSQL is up!"
            exit 0
          fi
          echo "Waiting for PostgreSQL... attempt $i"
          sleep 2
        done
        echo "PostgreSQL did not start in time"
        exit 1
      displayName: 'Wait for PostgreSQL'
    - script: |
        mkdir -p $(workDir)/server/coverage/
        export PGPASSWORD=dbpwd
        psql -h "postgres" -U dbuser -d "bol" \
             -f docker-compose/db/qvarn_init_schema.sql \
             -f docker-compose/db/bda_init_schema.sql
        echo docker ps -a
        docker ps -a
        echo docker images
        docker images
        make -C server cover-ci
      env:
        PYTHONWARNINGS: "ignore::sqlalchemy.exc.SAWarning,ignore::DeprecationWarning:pkg_resources,ignore::ImportWarning"
        SYSTEM_JOBPOSITIONINPHASE: $[variables['System.JobPositionInPhase']]
        COVERAGE_FILE: $(workDir)/server/.coverage.$(System.JobPositionInPhase)
      displayName: Unittest server
    - script: |
        pwd
        echo "Generated coverage files:"
        find $(workDir)/server -name ".coverage.*"
        ls -la $(workDir)/server
        echo "Listing files for debugging"
        find $(workDir) -maxdepth 3 | grep -v .git | sort
      displayName: 'List Directory Files'
    - task: PublishTestResults@2
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: 'server/junit/test-results.*.xml'
    - script: |
        mkdir -p $(Pipeline.Workspace)/coverage-temp
        cp $(workDir)/server/.coverage.* $(Pipeline.Workspace)/coverage-temp/
      displayName: 'Collect Coverage Files'
    - publish: $(Pipeline.Workspace)/coverage-temp
      artifact: coverage-files-$(System.JobPositionInPhase)
      displayName: 'Publish Coverage Files'
  - job: MergeCoverage
    displayName: Merge coverage
    dependsOn: unittest_server
    condition: succeeded()
    steps:
      - checkout: self
        displayName: 'Checkout Code'
      - task: DownloadPipelineArtifact@2
        inputs:
          patterns: 'coverage-files-*/*'
          path: $(workDir)/coverage-files
        displayName: 'Download Coverage Files'
      - script: |
          echo "Copying coverage files..."
          find $(workDir)/coverage-files -name ".coverage.*" \
            -exec cp {} $(workDir)/server/ \;
          cd server
          python -m venv env
          source env/bin/activate
          pip install coverage
          echo "Generating XML report with path adjustment:"
          coverage combine --keep .coverage.*
          coverage xml --ignore-errors -o coverage.xml
          echo "Generated coverage.xml:"
          ls -la coverage.xml
        displayName: 'Merge Coverage Files'
      - task: PublishCodeCoverageResults@2
        displayName: 'Publish Combined Coverage Results'
        inputs:
          codeCoverageTool: Cobertura
          summaryFileLocation: '$(workDir)/server/coverage.xml'
          pathToSources: '$(workDir)/server/bolfak'
          failIfCoverageEmpty: true
  - job: unittests_client
    timeoutInMinutes: 10
    cancelTimeoutInMinutes: 2
    steps:
    - task: npmAuthenticate@0
      inputs:
        workingFile: $(workDir)/client/.npmrc
    - task: Bash@3
      target: nodeContainer
      displayName: Unittest client
      inputs:
        targetType: inline
        workingDirectory: $(workDir)
        script: |
          make -C client test

# We run build and push docker
# after unit tests in PR branches
- stage: build_push_docker_pr
  variables:
    workDir: $(System.DefaultWorkingDirectory)
  displayName: PR Docker build and push
  condition: and(succeeded(), ne(variables['Build.SourceBranch'], 'refs/heads/master'))
  # It does not make sens to bock build by unit tests for the most of releases, including release-branch releases.
  # dependsOn: unit_test
  jobs:
  - template: build-push-amd64-job.yml


- stage: robot_test
  variables:
    workDir: $(System.DefaultWorkingDirectory)
    outDir: $(workdir)/robottests/output
    # sudo chown to have permission to write and publish output
    collectDockerLogs: |
      sudo chown -R $USER $(outDir)
      mkdir -p $(outDir)/docker_logs
      docker images | tee $(outDir)/docker_logs/docker-images.txt
      for container in $(docker ps -a --format "{{.Names}}"); do
        docker logs $container > $(outDir)/docker_logs/$container.log 2>&1
        echo "$container.log saved"
      done
      du -h $(outDir)
    robotCleanupScript: |
      rm -rf $(workDir)/robottests
      docker rm -f $(docker ps -aq)
      docker system prune -fa
      docker ps -a
  displayName: Robot tests
  dependsOn:
  - build_push_docker_master
  - build_push_docker_pr
  condition: or(succeeded('build_push_docker_master'), succeeded('build_push_docker_pr'))
  jobs:
  - job: check_for_robot_test_changes
    steps:
    - task: CmdLine@2
      name: gitDiff
      displayName: Check for changes in robottests/
      inputs:
        script: |
          # This checks whether any commit in this branch has made any changes
          # to files under robottests/.
          git diff --exit-code --name-only origin/master... -- robottests/
          case $? in
            0)
              echo 'Did not find changes in robottests/'
              echo '##vso[task.setvariable variable=changes;isOutput=true]no'
              ;;
            1)
              echo 'Found changes in robottests/'
              echo '##vso[task.setvariable variable=changes;isOutput=true]yes'
              ;;
            *)
              # probably 128; indicates a git error, e.g. branch not found
              exit 1
          esac

  - job: test_robot_id06_smoke
    workspace:
      clean: all
    timeoutInMinutes: 180
    cancelTimeoutInMinutes: 2
    steps:
    - task: Docker@2
      displayName: Login
      inputs:
        containerRegistry: $(dockerRegistryServiceConnectionAlpha)
        command: 'login'
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'pip | "$(Agent.OS)" | test_robot_id06_full'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(pip_cache_dir)
    - task: CmdLine@2
      env:
        IMAGE_TAG: $(imageTag)
      displayName: Setup and run robots
      inputs:
        workingDirectory: $(workDir)
        script: |
          set -x
          mkdir -p $(outDir)
          cd docker-compose
          $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) -f mitt-id06.yml down
          $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) -f mitt-id06.yml build --pull
          free -m
          rc=0
          $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) run --rm syncfixtures > $(outDir)/syncfixtures.log || rc=$?
          test $rc -eq 0 && $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) run --rm -e ROBOT_EXTRA_ARGS='-i Smoketest --exclude core' robottests || rc=$?

          $(collectDockerLogs)
          exit $rc
    - task: PublishBuildArtifacts@1
      condition: always()
      inputs:
        pathToPublish: $(outDir)
        artifactName: robottests_smoke
    - task: CmdLine@2
      displayName: Cleanup. Delete robottests folder, delete images and volumes
      condition: always()
      inputs:
        workingDirectory: $(workDir)
        script: $(robotCleanupScript)

  - job: test_robot_id06_full
    workspace:
      clean: all # what to clean up before the job runs
    # Run this job only on master branch or when there were changes made to the robottests/ subdirectory
    dependsOn: check_for_robot_test_changes
    condition: >
      and(succeeded(),
          or(eq(dependencies.check_for_robot_test_changes.outputs['gitDiff.changes'], 'yes'),
             contains(variables['build.sourceBranch'], 'refs/heads/master')))
    timeoutInMinutes: 300
    cancelTimeoutInMinutes: 2
    steps:
    - task: Docker@2
      displayName: Login
      inputs:
        containerRegistry: $(dockerRegistryServiceConnectionAlpha)
        command: 'login'
    - task: Cache@2
      displayName: Cache pip
      inputs:
        key: 'pip | "$(Agent.OS)" | test_robot_id06_full'
        restoreKeys: |
          pip | "$(Agent.OS)"
        path: $(pip_cache_dir)
    - task: CmdLine@2
      env:
        IMAGE_TAG: $(imageTag)
      displayName: Setup and run robots
      inputs:
        workingDirectory: $(workDir)
        # sudo chmod -R commands below added to avoid the
        # same agents in Azure to have permission denied when deleting
        # files from the old run. Also to allow the agent to have permission
        # to write and publish the output.
        script: |
          set -x
          mkdir -p $(outDir)
          cd docker-compose
          $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) -f mitt-id06.yml down
          $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) -f mitt-id06.yml build --pull
          free -m
          rc=0
          $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) run --rm syncfixtures > $(outDir)/syncfixtures.log || rc=$?
          test $rc -eq 0 && $(DOCKER_COMPOSE) $(ROBOT_SWE_COMPOSE) run --rm robottests || rc=$?

          $(collectDockerLogs)
          exit $rc
    - task: PublishBuildArtifacts@1
      condition: always()
      inputs:
        pathToPublish: $(outDir)
        artifactName: robottests_full
    - task: CmdLine@2
      displayName: Cleanup. Delete robottests folder, delete images and volumes
      condition: always()
      inputs:
        workingDirectory: $(workDir)
        # Deleting the robottests folder post job as a short-term solution to
        # avoid the agents to have permission denied issues when trying to delete
        # this folder and thus blocking subsequent runs on the same agent.
        # See comments in: https://vaultit.atlassian.net/browse/BOL-4825
        #
        # Also removing images and volumes, since it was discovered that
        # old images from previous runs were used. Let's start fresh instead.
        script: $(robotCleanupScript)
