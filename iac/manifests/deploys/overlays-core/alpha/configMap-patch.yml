- op: replace
  path: /data/gluu_base_url
  value: "https://auth-core-alpha.id06.se"
- op: replace
  path: /data/frontend_url
  value: "https://mitt.alpha.id06.se/"
- op: replace
  path: /data/standalone_url
  value: "https://bol.alpha.id06.se/"
- op: replace
  path: /data/backend_url
  value: "https://bol.alpha.id06.se/api/"
- op: replace
  path: /data/old_domain
  value: "bfalpha.id06.se"
- op: replace
  path: /data/report_swedish_data_provider
  value: "bolfak.statusreports.noop.NoopProvider"
- op: replace
  path: /data/report_foreign_data_provider
  value: "bolfak.statusreports.creditsafe_connect_core.CreditsafeConnectCoreReportProvider"
- op: replace
  path: /data/bol_stamp_base_url
  value: "https://stampdata-api.alpha.vaultit.org"
- op: replace
  path: /data/shared_cookie_domain
  value: ".id06.se"
- op: replace
  path: /data/bulkimport_data_provider
  value: "bolfak.statusreports.sandbox.SandboxInfoProvider"
- op: replace
  path: /data/worker_process_count
  value: "4"
- op: replace
  path: /data/log_level
  value: "INFO"
- op: replace
  path: /data/test_api
  value: "true"
- op: replace
  path: /data/sendgrid_sender
  value: ID06 alpha <<EMAIL>>
- op: replace
  path: /data/autoaccount_request_package
  value: ID06_TEST_AA
- op: replace
  path: /data/autoaccount_use_testing_org_gov_org_id
  value: "true"
- op: replace
  path: /data/autoaccount_testing_org_gov_org_id
  value: "**********"
# Feature flags
- op: replace
  path: /data/feature_flag_import_sole_traders
  value: "false"
- op: replace
  path: /data/swagger_api
  value: "true"
- op: replace
  path: /data/feature_flag_skip_pa_reg_step
  value: "true"
# Visitor whitelist
- op: replace
  path: /data/visitor_whitelist_org_ids
  value: "fe98145c-070d-4b71-86d0-77b99d70173d,55d82900-ba08-4291-988e-90d36c472f13"
# Celery
- op: replace
  path: /data/CELERY_TASK_DEFAULT_QUEUE
  value: "celery-que-bol-core-alpha"
- op: replace
  path: /data/CELERY_TASK_DEFAULT_EXCHANGE
  value: "celery-exc-bol-alpha"
# Deprecated
- op: replace
  path: /data/NEW_RELIC_ENVIRONMENT
  value: "bolswe-alpha"
