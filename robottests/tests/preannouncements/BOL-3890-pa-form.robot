*** Settings ***
Force Tags      BOL-3890-form
Resource        ../keywords/preannouncement.robot
Library         DateTime
Library         OperatingSystem
Suite Setup     Suite Setup

*** Variables ***
${pa_intro_text}                Use the form below to register information about your company and your agreement in the project. Also check the contract information provided by your customer. The information stated below will be reviewed by your customer, who will then submit the preannouncement. All companies above you in the supply chain will then review the data. When the preannouncement is confirmed, you will receive a notification.
@{professional_work_area}       cleaning


*** Test Cases ***
Check preannouncement form existance
    [Tags]      BOL-3890-form      BOL-4261
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Navigate and check PA form existence        Register
    Wait Until Element Is Visible               pa-intro
    Element Should Contain                      pa-intro                    ${pa_intro_text}
    Element Should Be Visible                   pa_register_button
    Element Should Contain                      pa_register_button          Register
    Close PA form

    Sign In As Main Contractor
    Navigate and check PA form existence        Waiting for registration
    Element Should Not Be Visible               pa-intro
    Element Should Not Be Visible               pa_register_button
    Close PA form

    Sign In As Client
    Navigate and check PA form existence        Waiting for registration
    Element Should Not Be Visible               pa-intro
    Element Should Not Be Visible               pa_register_button
    Close PA form

Check PA Contract Information section
    [Tags]      BOL-3890-form        BOL-4348.1
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Navigate and check PA form existence        Register
    Wait Until Element Is Visible               contract_info
    Element Should Be Visible                   contract_info
    Element Should Contain                      contract_info       Contract information
    Wait Until Element Is Visible               xpath://*[contains(text(), 'Start date')]
    Element Should Contain                      xpath://*[contains(text(), 'Start date')]/following-sibling::*/span       ${start_date}
    Element Should Contain                      xpath://*[contains(text(), 'End date')]/following-sibling::*/span         ${end_date}
    Element Should Contain                      xpath://*[contains(text(), 'Professional work area')]/following-sibling::*/span         Cleaning
    Element Should Contain                      xpath://*[contains(text(), 'Contract type')]/following-sibling::*/span                  Contracting
    Close PA form

Check PA Contract Information with multiple professional work areas
    [Tags]      BOL-3890-form        BOL-4348.2
    ${professional_work_area}=          Create List         cleaning   scaffolding     other
    Create PA Project And Add First Main Contractor         Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier             Test PA Form        RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Navigate and check PA form existence        Register
    Wait Until Element Is Visible               contract_info
    Element Should Be Visible                   contract_info
    Element Should Contain                      contract_info               Contract information
    Element Should Contain                      xpath://*[contains(text(), 'Professional work area')]/following-sibling::*/span         Cleaning, Scaffolding, Other
    Close PA Form

Check PA Project information
    [Tags]      BOL-3890-form        BOL-4262
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Navigate and check PA form existence        Register
    Wait Until Element Is Visible               xpath://*[contains(text(), 'Project name')]/following-sibling::*/span
    Element Should Be Visible                   project_info
    Element Should Contain                      project_info       Project information
    Element Should Contain                      xpath://*[contains(text(), 'Project name')]/following-sibling::*/span               Test PA Form
    # Client Organisation, see variables.py file
    Element Should Contain                      xpath://*[contains(text(), 'Construction client')]/following-sibling::*/span        Trafikverket
    # Main Contractor Organisation, see variables.py file
    Element Should Contain                      xpath://*[contains(text(), 'Main contractor')]/following-sibling::*/span            RobotTests
    Element Should Contain                      xpath://*[contains(text(), 'Customer')]/following-sibling::*/span                   RobotTests
    Close PA Form

Check PA Project information when Contractor is different than Main Contractor
    [Tags]      BOL-3890-form        BOL-4410
    Fixture Create Project              PA-Form-AUTO   Test PA Form       TC-0000     pa_form_enabled=${True}
    Sign In As Client
    Open Projects
    Filter Project List By Search       Test PA Form
    Navigate To Project Details         Test PA Form
    Add First Supplier As Simple Supplier
    Add Second Supplier As Main Contractor

    Sign In As Simple Supplier
    Preannounce Subsupplier             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form and Register           Test PA Form       4
    Confirm First Subsupplier
    Sign In As Subsupplier
    Preannounce Second Subsupplier           Test PA Form       Test company 38     TC-2002     contracting     ${professional_work_area}

    Sign In As Second Subsupplier
    Open Projects
    Filter Project List By Search       Test PA Form
    Navigate To Project Details         Test PA Form
    Navigate and check PA form existence        Register
    Wait Until Element Is Visible               xpath://*[contains(text(), 'Project name')]/following-sibling::*/span
    Element Should Contain                      project_info       Project information
    Element Should Contain                      xpath://*[contains(text(), 'Project name')]/following-sibling::*/span               Test PA Form
    Element Should Contain                      xpath://*[contains(text(), 'Construction client')]/following-sibling::*/span        Trafikverket
    Element Should Contain                      xpath://*[contains(text(), 'Main contractor')]/following-sibling::*/span            RobotTests Company 1
    Element Should Contain                      xpath://*[contains(text(), 'Contractor')]/following-sibling::*/span                 RobotTests
    Element Should Contain                      xpath://*[contains(text(), 'Customer')]/following-sibling::*/span                   Test company 38
    Close PA Form

Check PA data section when "waiting for registration" and "in review"
    [Tags]      BOL-3890-form        BOL-4418.1
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}
    Navigate and check PA form existence                Waiting for registration
    Wait Until Element Is Visible                       preannoucement_data
    Element Should Contain                              preannoucement_data         Preannouncement
    Element Should Contain                              xpath://*[contains(text(), 'Status')]/following-sibling::*/span                 Waiting for registration
    Element Should Contain                              xpath://*[contains(text(), 'Assigned to')]/following-sibling::*/span            Test company 38
    Element Should Contain                              xpath://*[contains(text(), 'Assigned to date')]/following-sibling::*/span       ${start_date}
    Close PA Form

    Sign In As Subsupplier
    Open PA Form and Register                           Test PA Form       3
    Navigate and check PA form existence                In review
    Wait Until Element Is Visible                       preannoucement_data
    Element Should Contain                              xpath://*[contains(text(), 'Status')]/following-sibling::*/span                 In review
    Element Should Contain                              xpath://*[contains(text(), 'Assigned to')]/following-sibling::*/span            RobotTests
    Element Should Contain                              xpath://*[contains(text(), 'Assigned to date')]/following-sibling::*/span       ${start_date}
    Close PA Form

Check PA data section when PA is confirmed
    [Tags]      BOL-3890-form        BOL-4418.2
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form and Register                           Test PA Form       3

    Sign In As Main Contractor
    Navigate and check PA form existence                Review
    Confirm And Submit PA
    Click Element                                       xpath://span[@title="In review"]
    Wait Until Element Is Visible                       preannoucement_data
    Element Should Contain                              preannoucement_data         Preannouncement
    Element Should Contain                              xpath://*[contains(text(), 'Status')]/following-sibling::*/span                 In review
    Element Should Contain                              xpath://*[contains(text(), 'Assigned to')]/following-sibling::*/span            Trafikverket
    Element Should Contain                              xpath://*[contains(text(), 'Assigned to date')]/following-sibling::*/span       ${start_date}
    Close PA Form

    Sign In As Client
    Navigate and check PA form existence                Review
    Confirm PA
    Wait Until Element Does Not Contain                 css:.project-suppliers>table        Review
    Navigate and check PA form existence                Confirmed
    Wait Until Element Is Visible                       preannoucement_data
    Element Should Contain                              xpath://*[contains(text(), 'Status')]/following-sibling::*/span                     Confirmed
    Element Should Not Be Visible                       xpath://*[contains(text(), 'Assigned to')]/following-sibling::*/span
    Element Should Not Be Visible                       xpath://*[contains(text(), 'Assigned to date')]/following-sibling::*/span
    Element Should Contain                              xpath://*[contains(text(), 'Confirmed by')]/following-sibling::*/span               Trafikverket
    Element Should Contain                              xpath://*[contains(text(), 'Confirmation date')]/following-sibling::*/span          ${start_date}
    Close PA Form

Check PA data section when PA is rejected
    [Tags]      BOL-3890-form        BOL-4418.3
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form and Register                           Test PA Form       3

    Sign In As Main Contractor
    Navigate and check PA form existence                Review
    Reject PA
    Wait Until Element Does Not Contain                 css:.project-suppliers>table        Review
    Click Element                                       xpath://span[@title="Rejected"]
    Wait Until Element Is Visible                       preannoucement_data
    Element Should Contain                              preannoucement_data         Preannouncement
    Element Should Contain                              xpath://*[contains(text(), 'Status')]/following-sibling::*/span                 Rejected
    Element Should Not Be Visible                       xpath://*[contains(text(), 'Assigned to')]/following-sibling::*/span
    Element Should Not Be Visible                       xpath://*[contains(text(), 'Assigned to date')]/following-sibling::*/span
    Element Should Contain                              xpath://*[contains(text(), 'Rejected by')]/following-sibling::*/span            RobotTests
    Element Should Contain                              xpath://*[contains(text(), 'Rejected date')]/following-sibling::*/span          ${start_date}
    Close PA Form

Check Company Information section for an Warning status company
    [Tags]      BOL-3890-form        BOL-4263.1
    Fixture Create Organisation     556425-1212         Astra Nordic                    SWE         has_cs_account=${True}
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Fixture Create Supplier                             PA-Form-AUTO       556425-1212
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      556425-1212     contracting     ${professional_work_area}
    ${CreditSafeXMLContent}=        Get File            ../robottests/tests/fixtures/creditsafe_data_astra_nordic.xml
    Fixture Create Report           creditsafe_xml=${CreditSafeXMLContent}      org_reg_no=556425-1212      interested_org_reg_no=TCR-10000

    Wait Until Element Contains                         css:.project-suppliers>table        Waiting for registration
    Click Element                                       xpath://span[@title="Waiting for registration"]
    Wait Until Element Is Visible                       company_info
    Element Should Contain                              company_info                        Company information
    Element Should Contain                              xpath://*[contains(text(), 'Company name')]/following-sibling::*/span       Astra Nordic
    Element Should Contain                              xpath://*[contains(text(), 'Business ID')]/following-sibling::*/span        556425-1212
    Element Should Contain                              xpath://*[contains(text(), 'Country of registration')]/following-sibling::*/span    SWE
    Element Should Contain                              css:.pa_company_status .card-title > span                                           WARNING!
    ${overall_risk_icon}=       Get Overall Risk Icon
    ${overall_risk_title}=      Get Overall Risk Title
    Should Contain              ${overall_risk_icon}        status-stop
    Should Contain              ${overall_risk_title}       Warning!

    ${expected_icons}=          Create List
    ...     status-stop             status-stop             status-stop             status-stop
    ...     status-investigate      status-incomplete       status-attention
    ${expected_statuses}=       Create List
    ...     F-tax control: F-tax missing
    ...     Company status: Liquidation declared
    ...     Debt balance company or private individual at Swedish Enforcement Authority: The company has in total 50 000 SEK in debt balance of which company or private individual debts are 50 000 SEK
    ...     Rating: Liquidation declared or liquidation continues
    ...     Swedish VAT register: Not registered in the Swedish VAT-register
    ...     Control of requirement for auditor: No information available
    ...     Swedish employer register: Is not registered in the employer register, nor does it report any employees
    Check Company Report Risk Indicators        ${expected_icons}       ${expected_statuses}

    Check Company Report Link       Astra Nordic

Check Company Information section for an OK company status
    [Tags]      BOL-3890-form        BOL-4263.2
    Fixture Create Organisation     556702-6903         Jackhammer Studios                    SWE       has_cs_account=${True}
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Fixture Create Supplier                             PA-Form-AUTO       556702-6903
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      556702-6903     contracting     ${professional_work_area}
    ${CreditSafeXMLContent}=        Get File            ../robottests/tests/fixtures/creditsafe_data_jackhammer_studios.xml
    Fixture Create Report           creditsafe_xml=${CreditSafeXMLContent}      org_reg_no=556702-6903      interested_org_reg_no=TCR-10000

    Wait Until Element Contains                         css:.project-suppliers>table        Waiting for registration
    Click Element                                       xpath://span[@title="Waiting for registration"]
    Wait Until Element Is Visible                       company_info
    Element Should Contain                              company_info                        Company information
    Element Should Contain                              xpath://*[contains(text(), 'Company name')]/following-sibling::*/span       Jackhammer Studios
    Element Should Contain                              xpath://*[contains(text(), 'Business ID')]/following-sibling::*/span        556702-6903
    Element Should Contain                              xpath://*[contains(text(), 'Country of registration')]/following-sibling::*/span    SWE
    Element Should Contain                              css:.pa_company_status .card-title > span                                           OK
    ${overall_risk_icon}=       Get Overall Risk Icon
    ${overall_risk_title}=      Get Overall Risk Title
    Should Contain              ${overall_risk_icon}        status-ok
    Should Contain              ${overall_risk_title}       OK
    Check Company Report Link                               Jackhammer Studios

Check Company Information section when there is no report generated
    [Tags]        BOL-3890-form        BOL-4263.3       headless
    # This test fails on CI unless you use a headless browser, for unclear reasons (running out of RAM?)
    [Setup]       Open Headless Browser Window
    [Teardown]    Close All Browsers

    Fixture Create Organisation     556702-6903         Jackhammer Studios                    SWE       has_cs_account=${True}
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Fixture Create Supplier                             PA-Form-AUTO       556702-6903
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      556702-6903     contracting     ${professional_work_area}

    Wait Until Element Contains                         css:.project-suppliers>table        Waiting for registration
    Click Element                                       xpath://span[@title="Waiting for registration"]
    Wait Until Element Is Visible                       company_info
    Element Should Contain                              company_info                        Company information
    Element Text Should Be                              xpath://*[contains(text(), 'Country of registration')]/following-sibling::*/span    SWE
    Element Text Should Be                              css:.pa_company_status>span         Status cannot be determined because the report is missing. Check that there is support for monitoring the country of registration according to the FAQ. If there is support and the report is still missing, contact <EMAIL>
    Click Element                                       xpath://*[text()='FAQ']/..
    Switch Window                                       ID06 Bolagsdeklaration | Frågor och svar - ID06
    Set Window Size             ${1280}    ${1600}
    Verify FAQ page
    Switch Bol Window
    Close PA Form

Check Company Information section for a foreign and VAT registered company
    [Tags]      BOL-3890-form        BOL-4468        BOL-4263.4
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      FC-1001     contracting     ${professional_work_area}    country=fi

    Wait Until Element Contains                         css:.project-suppliers>table        Waiting for registration
    Click Element                                       xpath://span[@title="Waiting for registration"]
    Wait Until Element Is Visible                       company_info
    Element Should Contain                              company_info                        Company information
    Element Should Contain                              xpath://*[contains(text(), 'Company name')]/following-sibling::*/span               Foreign Company 1
    Element Should Contain                              xpath://*[contains(text(), 'Business ID')]/following-sibling::*/span                FC-1001
    Element Should Contain                              xpath://*[contains(text(), 'VAT')]/following-sibling::*/span                        VAT-1001
    Element Should Contain                              xpath://*[contains(text(), 'Country of registration')]/following-sibling::*/span    FIN
    Close PA Form

Check Collective Agreement section one question flow
    [Tags]      BOL-3890-form       BOL-4264.1
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Element Should Contain          one_man_business                        Is the company a one-man business?
    Element Should Contain          css:#one_man_business>.field-help       A one-man business is a company with one employee or a sole trader without employees.
    Check PA Yes/No options availability        one_man_business_yes        one_man_business_no

    Click Element                   one_man_business_yes
    Element Should Not Be Visible   collective_agreement
    Element Should Not Be Visible   collective_agreement_type
    Click Element                           pa_foreman_on_site_no
    Click Element                           pa_register_button
    Wait Until Element Does Not Contain     css:.project-suppliers>table        Register

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3       Review
    Wait Until Element Is Visible           xpath://*[@id="one_man_business_view_mode"]/div[1]
    Element Should Contain                  xpath://*[@id="one_man_business_view_mode"]/div[1]              Is the company a one-man business?\nA one-man business is a company with one employee or a sole trader without employees.
    Element Should Contain                  xpath://*[@id="one_man_business_view_mode"]/div[2]              Yes
    Element Should Not Be Visible           xpath://*[@id="collective_agreement_view_mode"]
    Element Should Not Be Visible           xpath://*[@id="collective_agreement_type_view_mode"]
    Close PA Form

Check Collective Agreement section full flow
    [Tags]      BOL-3890-form       BOL-4264.2
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Element Should Contain          one_man_business                        Is the company a one-man business?
    Element Should Contain          css:#one_man_business>.field-help       A one-man business is a company with one employee or a sole trader without employees.
    Check PA Yes/No options availability        one_man_business_yes        one_man_business_no

    Click Element                   one_man_business_yes
    Element Should Not Be Visible   collective_agreement
    Element Should Not Be Visible   collective_agreement_type

    Click Element                   one_man_business_no
    Element Should Contain          collective_agreement                Is the company a member of an employers' organization or has the company signed a suspension agreement?
    Check PA Yes/No options availability        pa_collective_agreement_yes     pa_collective_agreement_no

    Click Element                   pa_collective_agreement_no
    Element Should Not Be Visible   collective_agreement_type

    Click Element                           pa_collective_agreement_yes
    Element Should Contain                  collective_agreement_type           Enter the name of the agreement
    ${placeholder}=                         Get Element Attribute       xpath:*//input[@id="pa_collective_agreement_name"]          placeholder
    Should Be Equal As Strings              ${placeholder}              Example: construction machinery agreement

    Type Text                               pa_collective_agreement_name        Collective Test
    Click Element                           pa_foreman_on_site_no
    Click Element                           pa_register_button
    Wait Until Element Does Not Contain     css:.project-suppliers>table        Register

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3       Review
    Wait Until Element Is Visible           xpath://*[@id="one_man_business_view_mode"]/div[1]
    Element Should Contain                  xpath://*[@id="one_man_business_view_mode"]/div[1]              Is the company a one-man business?\nA one-man business is a company with one employee or a sole trader without employees.
    Element Should Contain                  xpath://*[@id="one_man_business_view_mode"]/div[2]              No
    Element Should Contain                  xpath://*[@id="collective_agreement_view_mode"]/div[1]          Is the company a member of an employers' organization or has the company signed a suspension agreement?
    Element Should Contain                  xpath://*[@id="collective_agreement_view_mode"]/div[2]          Yes
    Element Should Contain                  xpath://*[@id="collective_agreement_type_view_mode"]/div[1]     Enter the name of the agreement
    Element Should Contain                  xpath://*[@id="collective_agreement_type_view_mode"]/div[2]     Collective Test
    Close PA Form

Check Permanent Establishment section for a foreign company 
    [Tags]      BOL-3890-form       BOL-4033            BOL-4495.1
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      FC-1001     contracting     ${professional_work_area}    country=fi

    Sign In As Foreign Subsupplier
    Open PA Form                                Test PA Form       3        Register
    Wait Until Element Is Visible               permanent_place
    Element Should Contain                      permanent_place             Is there a basis for the company to have a permanent establishment when the work begins?
    Check PA Yes/No options availability        permanent_place_yes         permanent_place_no
    Click Element                               permanent_place_no
    Click Element                               one_man_business_yes
    Click Element                               pa_foreman_on_site_no
    Click Element                               pa_register_button
    Wait Until Element Does Not Contain         css:.project-suppliers>table        Register  

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3       Review
    Wait Until Element Is Visible           xpath://*[@id="permanent_place_view_mode"]/div[1]
    Element Should Contain                  xpath://*[@id="permanent_place_view_mode"]/div[1]        Is there a basis for the company to have a permanent establishment when the work begins?
    Element Should Contain                  xpath://*[@id="permanent_place_view_mode"]/div[2]        No
    Close Pa Form

Check Permanent Establishment for a SWE company
    [Tags]      BOL-3890-form       BOL-4033            BOL-4495.2
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Element Should Not Be Visible   permanent_place
    Click Element                           one_man_business_yes
    Click Element                           pa_foreman_on_site_no
    Click Element                           pa_register_button
    Wait Until Element Does Not Contain     css:.project-suppliers>table        Register

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3       Review
    Wait Until Element Is Visible           xpath://*[@id="one_man_business_view_mode"]/div[1]
    Element Should Not Be Visible           xpath://*[@id="permanent_place_view_mode"]
    Close PA Form

Check Foreman section one question flow
    [Tags]      BOL-3890-form       BOL-4265.1
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Click Element                   one_man_business_yes
    Element Should Contain          pa_foreman_on_site          Will there be a foreman on site?
    Check PA Yes/No options availability        pa_foreman_on_site_yes      pa_foreman_on_site_no
    Click Element                               pa_foreman_on_site_no
    Click Element                               pa_register_button
    Wait Until Element Does Not Contain         css:.project-suppliers>table        Register

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3       Review
    Wait Until Element Is Visible           xpath://*[@id="pa_foreman_on_site_view_mode"]
    Element Should Contain                  xpath://*[@id="pa_foreman_on_site_view_mode"]/div[1]            Will there be a foreman on site?
    Element Should Contain                  xpath://*[@id="pa_foreman_on_site_view_mode"]/div[2]            No
    Close PA Form

Check Foreman section full flow
    [Tags]      BOL-3890-form       BOL-4265.2
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Click Element                   one_man_business_yes
    Element Should Contain          pa_foreman_on_site          Will there be a foreman on site?
    Check PA Yes/No options availability        pa_foreman_on_site_yes      pa_foreman_on_site_no
    Click Element                   pa_foreman_on_site_yes
    Fill In Foreman Field           First name      Selma
    Fill In Foreman Field           Last name       Hellström
    Fill In Foreman Field           Phone number    0190417621
    Fill In Foreman Field           Email           <EMAIL>
    Click Element                   pa_register_button
    Wait Until Element Does Not Contain     css:.project-suppliers>table        Register

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3       Review
    Wait Until Element Is Visible           xpath://*[@id="pa_foreman_on_site_view_mode"]
    Check Foreman Field Name and Value      First name      Selma
    Check Foreman Field Name and Value      Last name       Hellström
    Check Foreman Field Name and Value      Phone number    0190417621
    Check Foreman Field Name and Value      Email            <EMAIL>
    Close PA Form

Check Informant Supplier section visible after registration
    [Tags]      BOL-3890-form       BOL-4038       BOL-4519
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Click Element                   one_man_business_yes
    Click Element                               pa_foreman_on_site_no
    Element Should Not Be Visible               informant_supplier_info
    Click Element                               pa_register_button
    Wait Until Element Does Not Contain         css:.project-suppliers>table        Register
    Open PA Form                                Test PA Form       3        In review
    Check Informant Supplier Fields             ${TC38_USER_FIRST_NAME}     ${TC38_USER_LAST_NAME}     ${TC38_USER_EMAIL}       ${TC38_USER_PHONE_NUMBER}
    Close PA Form

    Sign In As Main Contractor
    Open PA Form                                Test PA Form       3        Review
    Check Informant Supplier Fields             ${TC38_USER_FIRST_NAME}     ${TC38_USER_LAST_NAME}     ${TC38_USER_EMAIL}       ${TC38_USER_PHONE_NUMBER}
    Close PA Form

Check Informant Customer section visible after buyer confirmation
    [Tags]      BOL-3890-form       BOL-4169       BOL-4520
    Create PA Project And Add First Main Contractor     Test PA Form       TC-0000     TCR-10000
    Sign In As Main Contractor
    Preannounce Subsupplier                             Test PA Form       RobotTests      TC-1038     contracting     ${professional_work_area}

    Sign In As Subsupplier
    Open PA Form                    Test PA Form       3        Register
    Wait Until Element Is Visible   one_man_business
    Click Element                   one_man_business_yes
    Click Element                               pa_foreman_on_site_no
    Click Element                               pa_register_button
    Wait Until Element Does Not Contain         css:.project-suppliers>table        Register
    Open PA Form                                Test PA Form       3        In review
    Wait Until Element Is Visible               informant_supplier_info
    Element Should Not Be Visible               informant_customer_info
    Close PA Form

    Sign In As Main Contractor
    Open PA Form                            Test PA Form       3        Review
    Confirm And Submit PA
    Open PA Form                            Test PA Form       3        In review
    Check Informant Customer Fields         ${ROBOT_USER_FIRST_NAME}    ${ROBOT_USER_LAST_NAME}     ${ROBOT_USER_EMAIL}     ${ROBOT_USER_PHONE_NUMBER}
    Close PA Form

    Sign In As Subsupplier
    Open PA Form                            Test PA Form       3        In review
    Check Informant Customer Fields         ${ROBOT_USER_FIRST_NAME}    ${ROBOT_USER_LAST_NAME}     ${ROBOT_USER_EMAIL}     ${ROBOT_USER_PHONE_NUMBER}
    Close PA Form

    Sign In As Client
    Open PA Form                            Test PA Form       3        Review
    Check Informant Customer Fields         ${ROBOT_USER_FIRST_NAME}    ${ROBOT_USER_LAST_NAME}     ${ROBOT_USER_EMAIL}     ${ROBOT_USER_PHONE_NUMBER}
    Close PA Form


*** Keywords ***
Suite Setup
    # It should be already disabled via docker/testing.envfile, but disabling
    # it again here lets us run the test interactively against a local dev server
    # started with `make run`, which enables the skip_pa_reg_step feature by default.
    Fixture Featureflag Disable     skip_pa_reg_step
    Fixture Featureflag Disable     block_project_client

# Supplier company id TCR-10000
Sign In As Simple Supplier
    Sign In As Robot User

# Subsupplier company id TC-1038
Sign In As Subsupplier
    Sign In As Test Company 38 User

# Subsupplier company in TC-2002
Sign In As Second Subsupplier
    Sign In As ROBOT_USER4

Preannounce Second Subsupplier
    [Arguments]                         ${project_title}        ${parent_name}      ${subsupplier_id}       ${contract_type}         ${professional_work_area}
    Preannounce Subsupplier             ${project_title}        ${parent_name}      ${subsupplier_id}       ${contract_type}         ${professional_work_area}

Navigate and check PA form existence
    [Arguments]                         ${pa_form_title}
    Open Projects
    Filter Project List By Search       Test PA Form
    Navigate To Project Details         Test PA Form
    Click Element                       xpath://span[@title="${pa_form_title}"]
    Element Should Be Visible           preannouncement-content

Confirm First Subsupplier
    Sign In As Simple Supplier
    Open Projects
    Filter Project List By Search       Test PA Form
    Navigate To Project Details         Test PA Form
    Click Project Suppliers Tab
    Click Element                           xpath://span[@title="Review"]
    Confirm And Submit PA

    Sign In As Client
    Open Projects
    Filter Project List By Search       Test PA Form
    Navigate To Project Details         Test PA Form
    Click Project Suppliers Tab
    Click Element                           xpath://span[@title="Review"]
    Confirm PA

Add First Supplier As Simple Supplier
    Add First Supplier                  TCR-10000           RobotTest
    Fill In Required Fields And Save    Supplier

Add Second Supplier As Main Contractor
    Open Full Project Tree
    Open Node Submenu For Title         Trafikverket
    Click Submenu Add Supplier
    Type Text                               //input[@id='search_id']          TC-2001
    Click Element When Visible              company_find_button
    Wait Until Element Is Not Visible       company_find_button
    Select Role                             Main contractor
    Click Element When Visible              contract_types_dropdown
    Click Visible Element                   css:.dropdown-contract-type-contracting
    ${start_date}   ${end_date}             Select 1 week timeframe
    Set Global Variable                     ${start_date}
    Set Global Variable                     ${end_date}
    ${area}=    Set Variable    area
    FOR     ${area}     IN     @{professional_work_area}
        Click Element      work-areas-label-wrapper-${area}
    END
    Search Supplier Contact Email           <EMAIL>
    Wait Until Page Contains                Contact not found. This user will not have access to service.
    Click Visible Element                   company_save_button
    Wait Until Element Is Not Visible       company_save_button
    Element Should Not Be Visible           add_subcontractor_find_company
    Close Project Tree

Check Company Report Link
    [Arguments]     ${company_name}
    Element Text Should Be      company_report_link         View complete report
    Click link                  company_report_link
    Switch Window               title=Company report
    Element Should Be Visible   css:.logo
    Page Should Contain         ${company_name}
    Page Should Contain         Company report
    Close Window

Get Overall Risk Icon
    ${overall_risk_icon}=       Get Element Attribute       css:.pa_company_status .card-title > i          class
    RETURN                    ${overall_risk_icon}

Get Overall Risk Title
    ${overall_risk_title}=      Get Element Attribute       css:.pa_company_status .card-title > i          title
    RETURN                    ${overall_risk_title}

Check Informant Supplier Fields
    [Arguments]                             ${first_name}       ${last_name}        ${email_address}        ${phone_number}
    Wait Until Element Is Visible           informant_supplier_info
    Element Should Contain                  informant_supplier_info                              Informant supplier
    Element Should Contain                  xpath://*[@id="informant_supplier_info"]/div[1]      Name
    Element Should Contain                  xpath://*[@id="informant_supplier_info"]//*[contains(text(), 'Name')]/following-sibling::*/span             ${first_name} ${last_name}
    Element Should Contain                  xpath://*[@id="informant_supplier_info"]/div[2]      Email address
    Element Should Contain                  xpath://*[@id="informant_supplier_info"]//*[contains(text(), 'Email address')]/following-sibling::*/span    ${email_address}
    Element Should Contain                  xpath://*[@id="informant_supplier_info"]/div[3]      Phone number
    Element Should Contain                  xpath://*[@id="informant_supplier_info"]//*[contains(text(), 'Phone number')]/following-sibling::*/span     ${phone_number}

Check Informant Customer Fields
    [Arguments]                             ${first_name}       ${last_name}        ${email_address}        ${phone_number}
    Wait Until Element Is Visible           informant_customer_info
    Element Should Contain                  informant_customer_info                             Informant customer
    Element Should Contain                  xpath://*[@id="informant_customer_info"]/div[1]     Name
    Element Should Contain                  xpath://*[@id="informant_customer_info"]//*[contains(text(), 'Name')]/following-sibling::*/span             ${first_name} ${last_name}
    Element Should Contain                  xpath://*[@id="informant_customer_info"]/div[2]     Email address
    Element Should Contain                  xpath://*[@id="informant_customer_info"]//*[contains(text(), 'Email address')]/following-sibling::*/span    ${email_address}
    Element Should Contain                  xpath://*[@id="informant_customer_info"]/div[3]      Phone number
    Element Should Contain                  xpath://*[@id="informant_customer_info"]//*[contains(text(), 'Phone number')]/following-sibling::*/span     ${phone_number}

Verify FAQ page
    Element Should Be Visible       xpath=//input[@placeholder="Sök svar i kunskapsbanken"]
    Page Should Contain             ID06 Bolagsdeklaration
    Close Window
